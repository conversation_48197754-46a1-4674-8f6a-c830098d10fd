<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>wanda.cloud</groupId>
        <artifactId>parent</artifactId>
        <version>1.1.2-SNAPSHOT</version>
        <relativePath/>
    </parent>

    <groupId>wanda.card.kam</groupId>
    <artifactId>card-kam-consumer</artifactId>
    <version>1.0.0-SNAPSHOT</version>

    <properties>
        <maven.deploy.skip>true</maven.deploy.skip>
    </properties>

    <dependencies>
        <dependency>
            <groupId>wanda.cloud</groupId>
            <artifactId>wanda-consumer-spring-cloud-starter</artifactId>
        </dependency>
        
        <!-- 添加card-kam-common依赖 -->
        <dependency>
            <groupId>wanda.card.kam</groupId>
            <artifactId>card-kam-common-provider</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>wanda.commons.httpclients</groupId>
            <artifactId>card-kam-httpclient</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>cmc.card</groupId>
            <artifactId>lark-card-info-admin-contract</artifactId>
            <version>2.0.2-SNAPSHOT</version>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <layers>
                        <enabled>true</enabled>
                    </layers>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
