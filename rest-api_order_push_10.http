### 充值订单推送接口测试 - 小数据集（10张卡）
POST http://gateway-admin-b-qas.wandafilm.com/card-kam/ysht/recharge-order/push HTTP/1.1
Content-Type: application/json
Connection: keep-alive
User-Agent: IntelliJ-HTTP-Client
Accept: */*
Proxy-Connection: keep-alive

{
  "rechargeOrderNo": "RO202501070031",
  "contractNo": "CNT20250107002",
  "customerCode": "CUST001",
  "customerName": "测试客户公司3",
  "sellerId": "SELLER001",
  "seller": "张三",
  "areaCode": "AREA001",
  "areaName": "华北区域",
  "cinemaInnerCode": "CINEMA001",
  "cinemaName": "万达影城测试店",
  "everyCardRechargeAmount": 10000,
  "everyCardPresentAmount": 1000,
  "count": 10,
  "amount": 110000,
  "operator": "测试操作员",
  "cardInfos": [
    {
      "cardNo": "6225880000000001",
      "cardTypeCode": "SVCARD001",
      "cardTypeName": "储值卡"
    },
    {
      "cardNo": "6225880000000002",
      "cardTypeCode": "VIPCARD001",
      "cardTypeName": "会员卡"
    },
    {
      "cardNo": "6225880000000003",
      "cardTypeCode": "GIFTCARD001",
      "cardTypeName": "礼品卡"
    },
    {
      "cardNo": "6225880000000004",
      "cardTypeCode": "SVCARD001",
      "cardTypeName": "储值卡"
    },
    {
      "cardNo": "6225880000000005",
      "cardTypeCode": "VIPCARD001",
      "cardTypeName": "会员卡"
    },
    {
      "cardNo": "6225880000000006",
      "cardTypeCode": "GIFTCARD001",
      "cardTypeName": "礼品卡"
    },
    {
      "cardNo": "6225880000000007",
      "cardTypeCode": "SVCARD001",
      "cardTypeName": "储值卡"
    },
    {
      "cardNo": "6225880000000008",
      "cardTypeCode": "VIPCARD001",
      "cardTypeName": "会员卡"
    },
    {
      "cardNo": "6225880000000009",
      "cardTypeCode": "GIFTCARD001",
      "cardTypeName": "礼品卡"
    },
    {
      "cardNo": "6225880000000010",
      "cardTypeCode": "SVCARD001",
      "cardTypeName": "储值卡"
    }
  ]
}

### 说明
# 小数据集测试，包含10张卡
# 每张卡充值100元，赠送10元
# 总金额：10 * (100 + 10) = 1,100元 = 110,000分
# 适合快速测试和调试
