spring:
  cloud:
    sentinel:
      datasource:
        gw-flow:
          nacos:
            serverAddr: nacos.wd-prd.com:80
            dataId: ${spring.application.name}-gateway-rules
            namespace: sentinel-prd
            groupId: SENTINEL_GROUP
            dataType: json
            ruleType: gw-flow
        gw-api-group:
          nacos:
            serverAddr: nacos.wd-prd.com:80
            dataId: ${spring.application.name}-gateway-api-rules
            namespace: sentinel-prd
            groupId: SENTINEL_GROUP
            dataType: json
            ruleType: gw-api-group
    gateway:
      routes:
        - id: commons-front-api
          uri: lb://commons-front-api
          predicates:
            - Path=/dict/batch_get
          filters:
            - Tob
        - id: plus-card-front-api
          uri: lb://plus-card-front-api
          predicates:
            - Path=/plus/card/**
          filters:
            - Tob
        - id: plus-sale-tob-route
          uri: lb://plus-sale-admin-api
          predicates:
            - Path=/plus_sale_tob/**
        - id: star-select-card-sale-tob-route
          uri: lb://star-select-card-sale-admin-api
          predicates:
            - Path=/star_select_card_sale_tob/**
        - id: wanda-kam-admin-tob-api
          uri: lb://wanda-kam-admin-api
          predicates:
            - Path=/kam_htxt_tob/**
        - id: pos-front-api
          uri: lb://pos-front-api
          predicates:
            - Path=/wplus/**,/finance/**
      cross-domain:
        enabled: true
        trusts: 
          - '*wandafilm.com*'
        maxAge: 3600
      config:
        checkIpType: 2
        refreshChannelInterval: 3
        noCheckIp:
          - sell
logging:
  level: 
    ROOT: INFO
