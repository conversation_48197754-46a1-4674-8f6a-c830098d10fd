# 充值订单推送接口测试文档

## 文件说明

### 1. rest-api_1.http
- **用途**: 包含1000张卡的完整测试数据
- **文件大小**: 约5000行
- **适用场景**: 压力测试、大数据量测试

### 2. rest-api_small.http  
- **用途**: 包含10张卡的小数据集
- **文件大小**: 约70行
- **适用场景**: 快速功能测试、调试

### 3. generate_test_data.py
- **用途**: 生成测试数据的Python脚本
- **功能**: 可以自定义卡数量生成测试数据

## 测试数据说明

### 基本信息
- **接口路径**: `POST /card-kam/ysht/recharge-order/push`
- **Content-Type**: `application/json`
- **每张卡充值金额**: 100元 (10000分)
- **每张卡赠送金额**: 10元 (1000分)

### 卡信息
- **卡号格式**: 16位数字，前缀622588
- **卡类型**: 
  - SVCARD001 - 储值卡
  - VIPCARD001 - 会员卡  
  - GIFTCARD001 - 礼品卡
- **卡号范围**: 6225880000000001 到 6225880000001000

### 金额计算
- **1000张卡总金额**: 1000 × (100 + 10) = 110,000元 = 11,000,000分
- **10张卡总金额**: 10 × (100 + 10) = 1,100元 = 110,000分

## 使用方法

### 在IDEA中使用
1. 打开IDEA
2. 在项目中打开 `rest-api_1.http` 或 `rest-api_small.http` 文件
3. 确保本地服务已启动（端口8080）
4. 点击请求旁边的绿色箭头执行测试
5. 查看响应结果

### 修改服务地址
如果服务不在localhost:8080，请修改HTTP文件中的URL：
```
POST http://your-server:port/card-kam/ysht/recharge-order/push
```

### 自定义测试数据
运行Python脚本生成自定义数量的测试数据：
```bash
python3 generate_test_data.py
```

## 预期响应

### 成功响应
```json
{
  "status": 0,
  "msg": "请求成功",
  "data": null
}
```

### 失败响应
```json
{
  "status": -1,
  "msg": "错误信息",
  "data": null
}
```

## 注意事项

1. **数据唯一性**: 订单号包含时间戳，确保每次测试的唯一性
2. **卡号唯一性**: 每张卡的卡号都是唯一的
3. **金额一致性**: 总金额 = 卡数量 × (充值金额 + 赠送金额)
4. **服务状态**: 确保本地服务正常启动
5. **网络连接**: 确保能正常访问服务端口

## 故障排除

### 常见问题
1. **连接拒绝**: 检查服务是否启动，端口是否正确
2. **400错误**: 检查请求参数是否符合验证规则
3. **500错误**: 查看服务端日志，检查业务逻辑异常

### 调试建议
1. 先使用小数据集测试基本功能
2. 确认小数据集正常后再使用大数据集
3. 查看服务端日志获取详细错误信息
