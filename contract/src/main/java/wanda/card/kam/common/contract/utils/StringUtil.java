package wanda.card.kam.common.contract.utils;

public class StringUtil {

    public static String doSensitive(String original) {
        // 处理空字符串或null的情况
        if (original == null || original.isEmpty()) {
            return original;
        }
        int length = original.length();
        // 如果字符串长度小于等于8位，直接返回原字符串
        if (length <= 8) {
            return original;
        }
        // 取前4位
        String prefix = original.substring(0, 4);
        // 取后4位
        String suffix = original.substring(length - 4);
        // 计算需要替换为*的字符数量
        int starsCount = length - 8;
        // 构建星号字符串
        // 拼接结果
        return prefix + "*".repeat(starsCount) + suffix;
    }
}
