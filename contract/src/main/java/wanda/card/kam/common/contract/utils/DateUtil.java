package wanda.card.kam.common.contract.utils;

import org.apache.logging.log4j.util.Strings;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

public class DateUtil {

    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    public static String dateTime2Str(LocalDateTime localDateTime) {
        if (localDateTime == null) return "";
        return localDateTime.format(FORMATTER);
    }

    public static LocalDateTime str2DateTime(String str) {
        if (Strings.isEmpty(str)){
            return null;
        }
        return LocalDateTime.parse(str, FORMATTER);
    }
}
