package wanda.card.kam.common.contract.constant;

import lombok.AllArgsConstructor;
import wanda.stark.core.lang.EnumDisplayNameSupport;
import wanda.stark.core.lang.EnumValueSupport;
import wanda.stark.core.lang.Enums;

@AllArgsConstructor
public enum PushStatus implements EnumValueSupport, EnumDisplayNameSupport {

    WAIT_PUSH(1, "待推送"),
    PUSH_SUCCESS(2, "推送成功"),
    PUSH_FAIL(3, "推送失败");
    private final int value;
    private final String displayName;

    public static PushStatus valueOf(int value) {
        return Enums.valueOf(PushStatus.class, value);
    }

    public static PushStatus valueOf(int value, boolean throwNotFound) {
        return Enums.valueOf(PushStatus.class, value, throwNotFound);
    }

    @Override
    public String displayName() {
        return displayName;
    }

    @Override
    public int value() {
        return value;
    }
}
