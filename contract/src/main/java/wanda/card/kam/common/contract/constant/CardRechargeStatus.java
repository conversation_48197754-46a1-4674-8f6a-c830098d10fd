package wanda.card.kam.common.contract.constant;

import lombok.AllArgsConstructor;
import wanda.stark.core.lang.EnumDisplayNameSupport;
import wanda.stark.core.lang.EnumValueSupport;
import wanda.stark.core.lang.Enums;

@AllArgsConstructor
public enum CardRechargeStatus implements EnumValueSupport, EnumDisplayNameSupport {

    WAIT_RECHARGE(1, "待充值"),
    RECHARGING(2, "充值中"),
    SUCCESS(3, "充值成功"),
    FAILED(4, "充值失败"),
    INVALID(5,"已作废");

    private final int value;
    private final String displayName;

    @Override
    public String displayName() {
        return displayName;
    }

    @Override
    public int value() {
        return value;
    }

    public static CardRechargeStatus valueOf(int value) {
        return Enums.valueOf(CardRechargeStatus.class, value);
    }

    public static CardRechargeStatus valueOf(int value, boolean throwNotFound) {
        return Enums.valueOf(CardRechargeStatus.class, value, throwNotFound);
    }
}
