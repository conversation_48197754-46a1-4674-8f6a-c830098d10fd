package wanda.card.kam.admin.contract.dto;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.util.List;

/**
 * 充值订单充值DTO
 *
 * <AUTHOR>
 */
public class RechargeOrderRechargeDto {

    /**
     * 充值订单充值请求
     */
    @Getter
    @Setter
    public static class RechargeOrderRechargeRequest {
        /**
         * 充值请求流水号(批次号)
         */
        @NotBlank(message = "充值请求流水号(批次号)不能为空")
        private String batchNo;

        /**
         * 合同号
         */
        @NotBlank(message = "合同号不能为空")
        private String contractNo;

        /**
         * 充值订单号
         */
        @NotBlank(message = "充值订单号不能为空")
        private String rechargeOrderNo;

        /**
         * 每张卡充值金额（分）
         */
        @NotNull(message = "每张卡充值金额(分)不能为空")
        @Positive(message = "每张卡充值金额(分)必须大于0")
        private Integer everyCardRechargeAmount;


        /**
         * 每张卡赠送金额（分）
         */
        @NotNull(message = "每张卡赠送金额(分)不能为空,无赠送传0")
        @Min(value = 0, message = "每张卡赠送金额(分)不能小于0")
        private Integer everyCardPresentAmount;

        /**
         * 操作人
         */
        @NotBlank(message = "操作人不能为空")
        private String operator;

        /**
         * 卡号列表
         */
        @NotEmpty(message = "卡号列表不能为空")
        private List<String> cardNos;
    }
}