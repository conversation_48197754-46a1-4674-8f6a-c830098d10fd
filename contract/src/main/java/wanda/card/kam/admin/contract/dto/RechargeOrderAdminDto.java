package wanda.card.kam.admin.contract.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import wanda.card.kam.common.contract.constant.CardRechargeStatus;
import wanda.card.kam.common.contract.constant.OperationLogType;
import wanda.card.kam.common.contract.constant.RechargeOrderStatus;
import wanda.stark.core.data.PageInfo;

import javax.persistence.Transient;
import java.time.LocalDateTime;
import java.util.List;

public class RechargeOrderAdminDto {

    @Getter
    @Setter
    @ToString
    public static class ListRequest {
        /**
         * 合同编码（精准查询）
         */
        private String contractNo;

        /**
         * 客户编码（精准查询）
         */
        private String customerCode;

        /**
         * 客户名称（模糊查询）
         */
        private String customerName;

        /**
         * 销售员（模糊查询）
         */
        private String seller;

        /**
         * 充值订单号（精准查询）
         */
        private String rechargeOrderNo;

        /**
         * 创建时间开始 yyyy-mm-dd
         */
        private String createTimeStart;

        /**
         * 创建时间结束 yyyy-mm-dd
         */
        private String createTimeEnd;

        /**
         * 0:全部, 1:待充值, 2:部分充值, 3:充值成功, 4:充值失败
         */
        private int status;

        /**
         * 区域代码
         */
        private String areaCode;

        /**
         * 影城内码
         */
        private String cinemaInnerCode;
        private PageInfo pageInfo;
    }

    @Getter
    @Setter
    @ToString
    public static class RechargeOrder{
        private Long id;

        /**
         * 充值订单号
         */
        private String rechargeOrderNo;

        /**
         * 合同编码
         */
        private String contractNo;

        /**
         * 客户编码
         */
        private String customerCode;

        /**
         * 客户名称
         */
        private String customerName;

        /**
         * 销售员万信号
         */
        private String sellerId;

        /**
         * 销售员名
         */
        private String seller;

        /**
         * 区域编码
         */
        private String areaCode;

        /**
         * 区域名称
         */
        private String areaName;

        /**
         * 影城内码
         */
        private String cinemaInnerCode;

        /**
         * 影城名称
         */
        private String cinemaName;

        /**
         * 每张卡充值金额（分）
         */
        private Integer everyCardRechargeAmount;

        /**
         * 每张卡赠送金额（分）
         */
        private Integer everyCardPresentAmount;
        /**
         * 卡数量
         */
        private Integer count;

        /**
         * 总充值金额（含赠送金额）分
         */
        private Integer amount;

        /**
         * 状态
         */
        private RechargeOrderStatus status;

        /**
         * 创建时间
         */
        private LocalDateTime createTime;
    }

    @Getter
    @Setter
    @ToString
    public static class RechargeOrderDetail{
        private Long id;

        /**
         * 充值订单号
         */
        private String rechargeOrderNo;

        /**
         * 合同编码
         */
        private String contractNo;

        /**
         * 客户编码
         */
        private String customerCode;

        /**
         * 客户名称
         */
        private String customerName;

        /**
         * 销售员万信号
         */
        private String sellerId;

        /**
         * 销售员名
         */
        private String seller;

        /**
         * 区域编码
         */
        private String areaCode;

        /**
         * 区域名称
         */
        private String areaName;

        /**
         * 影城内码
         */
        private String cinemaInnerCode;

        /**
         * 影城名称
         */
        private String cinemaName;

        /**
         * 每张卡充值金额（分）
         */
        private Integer everyCardRechargeAmount;

        /**
         * 每张卡赠送金额（分）
         */
        private Integer everyCardPresentAmount;
        /**
         * 卡数量
         */
        private Integer count;

        /**
         * 总充值金额（含赠送金额）分
         */
        private Integer amount;

        /**
         * 状态
         */
        private RechargeOrderStatus status;

        /**
         * 创建时间
         */
        private LocalDateTime createTime;

        /**
         * 更新时间
         */
        private LocalDateTime updateTime;

        private List<OrderLog> logs;
    }

    @Getter
    @Setter
    @ToString
    public static class OrderLog{
        private LocalDateTime createTime;
        private OperationLogType logType;
        private String operationLog;
    }

    @Getter
    @Setter
    @ToString
    public static class CardDetailListRequest{
        /**
         * 充值订单号
         */
        private String rechargeOrderNo;
        private String cardNo;
        private LocalDateTime startTime;
        private LocalDateTime endTime;
        private CardRechargeStatus status;
        private PageInfo pageInfo;
    }

    @Getter
    @Setter
    @ToString
    public static class RechargeCardDetail{
        private Long id;

        /**
         * 充值订单号
         */
        private String rechargeOrderNo;
        /**
         * 卡号（脱敏展示）
         */
        private String cardNo;

        /**
         * 卡类型编码
         */
        private String cardTypeCode;

        /**
         * 卡类型名称
         */
        private String cardTypeName;

        /**
         * 充值金额（分）
         */
        private Integer rechargeAmount;

        /**
         * 赠送金额（分）
         */
        private Integer presentAmount;

        /**
         * 余额变动（充值金额+赠送金额）（分）
         */
        private Integer changeBalance;

        /**
         * 充值前余额（分）
         */
        private Integer beforeBalance;

        /**
         * 充值后余额（分）
         */
        private Integer afterBalance;

        /**
         * 充值状态 1:待充值 2:充值中 3:充值成功 4:充值失败
         */
        private CardRechargeStatus status;

        /**
         * 创建时间
         */
        private LocalDateTime createTime;

        /**
         * 充值时间
         */
        private LocalDateTime rechargeTime;

        /**
         * 更新时间
         */
        private LocalDateTime updateTime;
    }
}
