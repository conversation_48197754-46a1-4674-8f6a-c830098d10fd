package wanda.card.kam.admin.contract;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import wanda.card.kam.admin.contract.dto.RechargeOrderDto.UpdateRequest;
import wanda.card.kam.admin.contract.dto.RechargeOrderPushDto.RechargeOrderPushRequest;
import wanda.card.kam.admin.contract.dto.RechargeOrderRechargeDto.RechargeOrderRechargeRequest;
import wanda.stark.core.data.R;

import javax.validation.Valid;

/**
 * 充值订单服务
 *
 * <AUTHOR>
 */
@Validated
@FeignClient(contextId = "rechargeOrderService", name = ServiceAutoConfiguration.APPLICATION_NAME)
public interface RechargeOrderService {

    /**
     * 推送充值订单
     *
     * @param request 推送请求
     * @return 推送响应
     */
    @PostMapping("/recharge-order/push")
    R<Void> pushRechargeOrder(@Valid @RequestBody RechargeOrderPushRequest request);

    /**
     * 充值订单变更
     */
    @PostMapping("/recharge-order/update")
    R<Void> updateRechargeOrder(@Valid @RequestBody UpdateRequest request);

    /**
     * 充值订单充值
     *
     * @param request 充值请求
     * @return 充值响应
     */
    @PostMapping("/recharge-order/recharge")
    R<Void> rechargeCard(@Valid @RequestBody RechargeOrderRechargeRequest request);
}
