package wanda.card.kam.admin.contract;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import wanda.card.kam.admin.contract.dto.RechargeOrderAdminDto.*;
import wanda.stark.core.data.PageResult;
import wanda.stark.core.data.R;

import javax.validation.Valid;

/**
 * 充值订单后台服务
 */
@Validated
@FeignClient(contextId = "rechargeOrderAdminService", name = ServiceAutoConfiguration.APPLICATION_NAME)
public interface RechargeOrderAdminService {

    @PostMapping("/recharge-order/list")
    PageResult<RechargeOrder> list(@Valid @RequestBody ListRequest request);

    /**
     * 获取充值订单详情
     */
    @GetMapping("/recharge-order/detail")
    R<RechargeOrderDetail> getRechargeOrderDetail(@Valid @RequestParam Long rechargeOrderId);

    /**
     * 获取充值订单卡充值列表
     */
    @PostMapping("/recharge-order/cardDetail/list")
    PageResult<RechargeCardDetail> getRechargeOrderCardDetailList(@Valid @RequestBody CardDetailListRequest request);
}
