package wanda.card.kam.admin.contract.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import wanda.card.kam.common.contract.constant.CardRechargeStatus;

import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;

public class RechargeOrderDto {

    @Getter
    @Setter
    @ToString
    public static class UpdateRequest {

        /**
         * 充值订单号
         */
        @NotBlank(message = "充值订单号不能为空")
        private String rechargeOrderNo;

        /**
         * 合同编码
         */
        @NotBlank(message = "合同编码不能为空")
        private String contractNo;

        @NotBlank(message = "操作人不能为空")
        private String operator;
    }

}
