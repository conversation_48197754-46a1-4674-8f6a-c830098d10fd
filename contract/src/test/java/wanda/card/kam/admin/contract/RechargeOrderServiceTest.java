package wanda.card.kam.admin.contract;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import wanda.card.kam.admin.contract.dto.RechargeOrderPushDto.RechargeOrderCardPushInfo;
import wanda.card.kam.admin.contract.dto.RechargeOrderPushDto.RechargeOrderPushRequest;
import wanda.card.kam.admin.contract.dto.RechargeOrderRechargeDto.RechargeOrderRechargeRequest;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 充值订单服务测试
 *
 * <AUTHOR>
 */
class RechargeOrderServiceTest {

    private Validator validator;

    @BeforeEach
    void setUp() {
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        validator = factory.getValidator();
    }

    @Test
    @DisplayName("测试充值请求参数校验-正常情况")
    void testRechargeOrderRechargeRequestValid() {
        // Given
        RechargeOrderRechargeRequest request = new RechargeOrderRechargeRequest();
        request.setBatchNo("BATCH001");
        request.setContractNo("CONTRACT001");
        request.setRechargeOrderNo("ORDER001");
        request.setEveryCardRechargeAmount(10000); // 100元
        request.setEveryCardPresentAmount(0); // 无赠送
        request.setOperator("admin");
        request.setCardNos(Arrays.asList("CARD001", "CARD002"));

        // When
        Set<ConstraintViolation<RechargeOrderRechargeRequest>> violations = validator.validate(request);

        // Then
        assertTrue(violations.isEmpty(), "正常参数应该通过校验");
    }

    @Test
    @DisplayName("测试充值请求参数校验-批次号为空")
    void testRechargeOrderRechargeRequestBatchNoBlank() {
        // Given
        RechargeOrderRechargeRequest request = createValidRequest();
        request.setBatchNo("");

        // When
        Set<ConstraintViolation<RechargeOrderRechargeRequest>> violations = validator.validate(request);

        // Then
        assertEquals(1, violations.size());
        assertTrue(violations.stream().anyMatch(v -> v.getMessage().contains("充值请求流水号(批次号)不能为空")));
    }

    @Test
    @DisplayName("测试充值请求参数校验-合同号为空")
    void testRechargeOrderRechargeRequestContractNoBlank() {
        // Given
        RechargeOrderRechargeRequest request = createValidRequest();
        request.setContractNo("");

        // When
        Set<ConstraintViolation<RechargeOrderRechargeRequest>> violations = validator.validate(request);

        // Then
        assertEquals(1, violations.size());
        assertTrue(violations.stream().anyMatch(v -> v.getMessage().contains("合同号不能为空")));
    }

    @Test
    @DisplayName("测试充值请求参数校验-充值订单号为空")
    void testRechargeOrderRechargeRequestOrderNoBlank() {
        // Given
        RechargeOrderRechargeRequest request = createValidRequest();
        request.setRechargeOrderNo("");

        // When
        Set<ConstraintViolation<RechargeOrderRechargeRequest>> violations = validator.validate(request);

        // Then
        assertEquals(1, violations.size());
        assertTrue(violations.stream().anyMatch(v -> v.getMessage().contains("充值订单号不能为空")));
    }

    @Test
    @DisplayName("测试充值请求参数校验-充值金额为空")
    void testRechargeOrderRechargeRequestAmountNull() {
        // Given
        RechargeOrderRechargeRequest request = createValidRequest();
        request.setEveryCardRechargeAmount(null);

        // When
        Set<ConstraintViolation<RechargeOrderRechargeRequest>> violations = validator.validate(request);

        // Then
        assertEquals(1, violations.size());
        assertTrue(violations.stream().anyMatch(v -> v.getMessage().contains("每张卡充值金额(分)不能为空")));
    }

    @Test
    @DisplayName("测试充值请求参数校验-充值金额小于等于0")
    void testRechargeOrderRechargeRequestAmountNotPositive() {
        // Given
        RechargeOrderRechargeRequest request = createValidRequest();
        request.setEveryCardRechargeAmount(0);

        // When
        Set<ConstraintViolation<RechargeOrderRechargeRequest>> violations = validator.validate(request);

        // Then
        assertEquals(1, violations.size());
        assertTrue(violations.stream().anyMatch(v -> v.getMessage().contains("每张卡充值金额(分)必须大于0")));
    }

    @Test
    @DisplayName("测试充值请求参数校验-赠送金额为空")
    void testRechargeOrderRechargeRequestPresentAmountNull() {
        // Given
        RechargeOrderRechargeRequest request = createValidRequest();
        request.setEveryCardPresentAmount(null);

        // When
        Set<ConstraintViolation<RechargeOrderRechargeRequest>> violations = validator.validate(request);

        // Then
        assertEquals(1, violations.size());
        assertTrue(violations.stream().anyMatch(v -> v.getMessage().contains("每张卡赠送金额(分)不能为空,无赠送传0")));
    }

    @Test
    @DisplayName("测试充值请求参数校验-赠送金额小于0")
    void testRechargeOrderRechargeRequestPresentAmountNegative() {
        // Given
        RechargeOrderRechargeRequest request = createValidRequest();
        request.setEveryCardPresentAmount(-1);

        // When
        Set<ConstraintViolation<RechargeOrderRechargeRequest>> violations = validator.validate(request);

        // Then
        assertEquals(1, violations.size());
        assertTrue(violations.stream().anyMatch(v -> v.getMessage().contains("每张卡赠送金额(分)不能小于0")));
    }

    @Test
    @DisplayName("测试充值请求参数校验-操作人为空")
    void testRechargeOrderRechargeRequestOperatorBlank() {
        // Given
        RechargeOrderRechargeRequest request = createValidRequest();
        request.setOperator("");

        // When
        Set<ConstraintViolation<RechargeOrderRechargeRequest>> violations = validator.validate(request);

        // Then
        assertEquals(1, violations.size());
        assertTrue(violations.stream().anyMatch(v -> v.getMessage().contains("操作人不能为空")));
    }

    @Test
    @DisplayName("测试充值请求参数校验-卡号列表为空")
    void testRechargeOrderRechargeRequestCardNosEmpty() {
        // Given
        RechargeOrderRechargeRequest request = createValidRequest();
        request.setCardNos(Arrays.asList());

        // When
        Set<ConstraintViolation<RechargeOrderRechargeRequest>> violations = validator.validate(request);

        // Then
        assertEquals(1, violations.size());
        assertTrue(violations.stream().anyMatch(v -> v.getMessage().contains("卡号列表不能为空")));
    }

    @Test
    @DisplayName("测试充值请求参数校验-赠送金额为0正常")
    void testRechargeOrderRechargeRequestPresentAmountZero() {
        // Given
        RechargeOrderRechargeRequest request = createValidRequest();
        request.setEveryCardPresentAmount(0);

        // When
        Set<ConstraintViolation<RechargeOrderRechargeRequest>> violations = validator.validate(request);

        // Then
        assertTrue(violations.isEmpty(), "赠送金额为0应该通过校验");
    }

    @Test
    @DisplayName("测试充值请求参数校验-赠送金额大于0正常")
    void testRechargeOrderRechargeRequestPresentAmountPositive() {
        // Given
        RechargeOrderRechargeRequest request = createValidRequest();
        request.setEveryCardPresentAmount(5000); // 50元赠送

        // When
        Set<ConstraintViolation<RechargeOrderRechargeRequest>> violations = validator.validate(request);

        // Then
        assertTrue(violations.isEmpty(), "赠送金额大于0应该通过校验");
    }

    @Test
    @DisplayName("测试推送请求参数校验-卡信息列表正常情况")
    void testRechargeOrderPushRequestValid() {
        // Given
        RechargeOrderPushRequest request = createValidPushRequest();

        // When
        Set<ConstraintViolation<RechargeOrderPushRequest>> violations = validator.validate(request);

        // Then
        assertTrue(violations.isEmpty(), "正常参数应该通过校验");
    }

    @Test
    @DisplayName("测试推送请求参数校验-卡信息列表为空")
    void testRechargeOrderPushRequestCardInfosEmpty() {
        // Given
        RechargeOrderPushRequest request = createValidPushRequest();
        request.setCardInfos(new ArrayList<>());

        // When
        Set<ConstraintViolation<RechargeOrderPushRequest>> violations = validator.validate(request);

        // Then
        assertEquals(1, violations.size());
        assertTrue(violations.stream().anyMatch(v -> v.getMessage().contains("卡信息列表不能为空")));
    }

    @Test
    @DisplayName("测试推送请求参数校验-卡数量超过1000张")
    void testRechargeOrderPushRequestCardCountExceeds1000() {
        // Given
        RechargeOrderPushRequest request = createValidPushRequest();
        
        // 创建1001张卡的信息列表
        List<RechargeOrderCardPushInfo> cardInfos = new ArrayList<>();
        for (int i = 1; i <= 1001; i++) {
            RechargeOrderCardPushInfo cardInfo = new RechargeOrderCardPushInfo();
            cardInfo.setCardNo("CARD" + String.format("%04d", i));
            cardInfo.setCardTypeCode("TYPE001");
            cardInfo.setCardTypeName("普通卡");
            cardInfos.add(cardInfo);
        }
        request.setCardInfos(cardInfos);
        request.setCount(1001);
        request.setAmount((request.getEveryCardRechargeAmount() + request.getEveryCardPresentAmount()) * 1001);

        // When
        Set<ConstraintViolation<RechargeOrderPushRequest>> violations = validator.validate(request);

        // Then
        assertEquals(1, violations.size());
        assertTrue(violations.stream().anyMatch(v -> v.getMessage().contains("卡数量不能超过1000张")));
    }

    @Test
    @DisplayName("测试推送请求参数校验-卡数量等于1000张正常")
    void testRechargeOrderPushRequestCardCountEquals1000() {
        // Given
        RechargeOrderPushRequest request = createValidPushRequest();
        
        // 创建1000张卡的信息列表
        List<RechargeOrderCardPushInfo> cardInfos = new ArrayList<>();
        for (int i = 1; i <= 1000; i++) {
            RechargeOrderCardPushInfo cardInfo = new RechargeOrderCardPushInfo();
            cardInfo.setCardNo("CARD" + String.format("%04d", i));
            cardInfo.setCardTypeCode("TYPE001");
            cardInfo.setCardTypeName("普通卡");
            cardInfos.add(cardInfo);
        }
        request.setCardInfos(cardInfos);
        request.setCount(1000);
        request.setAmount((request.getEveryCardRechargeAmount() + request.getEveryCardPresentAmount()) * 1000);

        // When
        Set<ConstraintViolation<RechargeOrderPushRequest>> violations = validator.validate(request);

        // Then
        assertTrue(violations.isEmpty(), "1000张卡应该通过校验");
    }

    /**
     * 创建有效的充值请求对象
     */
    private RechargeOrderRechargeRequest createValidRequest() {
        RechargeOrderRechargeRequest request = new RechargeOrderRechargeRequest();
        request.setBatchNo("BATCH001");
        request.setContractNo("CONTRACT001");
        request.setRechargeOrderNo("ORDER001");
        request.setEveryCardRechargeAmount(10000);
        request.setEveryCardPresentAmount(0);
        request.setOperator("admin");
        request.setCardNos(Arrays.asList("CARD001", "CARD002"));
        return request;
    }

    /**
     * 创建有效的推送请求对象
     */
    private RechargeOrderPushRequest createValidPushRequest() {
        RechargeOrderPushRequest request = new RechargeOrderPushRequest();
        request.setRechargeOrderNo("RO202401010001");
        request.setContractNo("CONTRACT001");
        request.setCustomerCode("CUST001");
        request.setCustomerName("测试客户");
        request.setSellerId("SELLER001");
        request.setSeller("销售员");
        request.setAreaCode("AREA001");
        request.setAreaName("测试区域");
        request.setCinemaInnerCode("CINEMA001");
        request.setCinemaName("测试影院");
        request.setEveryCardRechargeAmount(5000); // 50元
        request.setEveryCardPresentAmount(1000);  // 10元
        request.setCount(2);
        request.setAmount(12000); // 120元 = (50+10)*2
        request.setOperator("操作员");

        // 构建卡信息列表
        RechargeOrderCardPushInfo card1 = new RechargeOrderCardPushInfo();
        card1.setCardNo("CARD001");
        card1.setCardTypeCode("TYPE001");
        card1.setCardTypeName("普通卡");

        RechargeOrderCardPushInfo card2 = new RechargeOrderCardPushInfo();
        card2.setCardNo("CARD002");
        card2.setCardTypeCode("TYPE001");
        card2.setCardTypeName("普通卡");

        request.setCardInfos(Arrays.asList(card1, card2));
        return request;
    }
}
