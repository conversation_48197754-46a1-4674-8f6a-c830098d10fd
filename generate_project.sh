#!/bin/bash

# Maven项目生成脚本
# 用法: ./generate_project.sh <service> <groupId> <artifactId> [package]
# service: service, common, consumer, task, api 之一
# groupId: 组织ID
# artifactId: 模块名（分词用-连接）
# package: 包名（可选，默认与groupId相同）

# 检查参数数量
if [ $# -lt 3 ] || [ $# -gt 4 ]; then
    echo "用法: $0 <service> <groupId> <artifactId> [package]"
    echo "参数说明:"
    echo "  service: service, common, consumer, task, api 之一"
    echo "  groupId: 组织ID"
    echo "  artifactId: 模块名（分词用-连接）"
    echo "  package: 包名（可选，默认与groupId相同）"
    echo ""
    echo "示例: $0 service com.example my-service-module"
    echo "示例: $0 api com.example my-api-module com.example.api"
    exit 1
fi

# 获取参数
SERVICE=$1
GROUP_ID=$2
ARTIFACT_ID=$3
PACKAGE=${4:-$GROUP_ID}  # 如果没有提供package参数，默认使用groupId

# 验证service参数
case $SERVICE in
    service|common|consumer|task|api)
        ;;
    *)
        echo "错误: service参数必须是以下之一: service, common, consumer, task, api"
        echo "当前输入: $SERVICE"
        exit 1
        ;;
esac

# 显示将要执行的命令
echo "即将执行以下Maven命令:"
echo "mvn archetype:generate \\"
echo "    -DarchetypeGroupId=wanda.cloud.archetypes \\"
echo "    -DarchetypeArtifactId=wanda-archetype-$SERVICE \\"
echo "    -DarchetypeVersion=1.0.0-SNAPSHOT \\"
echo "    -DinteractiveMode=false \\"
echo "    -DgroupId=$GROUP_ID \\"
echo "    -DartifactId=$ARTIFACT_ID \\"
echo "    -Dversion=1.0.0-SNAPSHOT \\"
echo "    -Dpackage=$PACKAGE"
echo ""

# 询问是否继续
read -p "是否继续执行？(y/N): " confirm
if [[ $confirm != [yY] && $confirm != [yY][eE][sS] ]]; then
    echo "操作已取消"
    exit 0
fi

# 执行Maven命令
mvn archetype:generate \
    -DarchetypeGroupId=wanda.cloud.archetypes \
    -DarchetypeArtifactId=wanda-archetype-$SERVICE \
    -DarchetypeVersion=1.1.2-SNAPSHOT \
    -DinteractiveMode=false \
    -DgroupId=$GROUP_ID \
    -DartifactId=$ARTIFACT_ID \
    -Dversion=1.0.0-SNAPSHOT \
    -Dpackage=$PACKAGE

# 检查执行结果
if [ $? -eq 0 ]; then
    echo ""
    echo "项目生成成功！"
    echo "项目目录: $ARTIFACT_ID"
else
    echo ""
    echo "项目生成失败，请检查参数和网络连接"
    exit 1
fi