### 充值订单充值接口测试 - 大数据集（1000张卡）
POST http://localhost:8080/card-kam/ysht/recharge-order/recharge
Content-Type: application/json

{
  "batchNo": "BATCH202501070100",
  "contractNo": "CNT20250807141408",
  "rechargeOrderNo": "RO20250807141408",
  "everyCardRechargeAmount": 10000,
  "everyCardPresentAmount": 1000,
  "operator": "测试操作员",
  "cardNos": [
    "6225880000000001", "6225880000000002", "6225880000000003", "6225880000000004", "6225880000000005",
    "6225880000000006", "6225880000000007", "6225880000000008", "6225880000000009", "6225880000000010",
    "6225880000000011", "6225880000000012", "6225880000000013", "6225880000000014", "6225880000000015",
    "6225880000000016", "6225880000000017", "6225880000000018", "6225880000000019", "6225880000000020",
    "6225880000000021", "6225880000000022", "6225880000000023", "6225880000000024", "6225880000000025",
    "6225880000000026", "6225880000000027", "6225880000000028", "6225880000000029", "6225880000000030",
    "6225880000000031", "6225880000000032", "6225880000000033", "6225880000000034", "6225880000000035",
    "6225880000000036", "6225880000000037", "6225880000000038", "6225880000000039", "6225880000000040",
    "6225880000000041", "6225880000000042", "6225880000000043", "6225880000000044", "6225880000000045",
    "6225880000000046", "6225880000000047", "6225880000000048", "6225880000000049", "6225880000000050",
    "6225880000000051", "6225880000000052", "6225880000000053", "6225880000000054", "6225880000000055",
    "6225880000000056", "6225880000000057", "6225880000000058", "6225880000000059", "6225880000000060",
    "6225880000000061", "6225880000000062", "6225880000000063", "6225880000000064", "6225880000000065",
    "6225880000000066", "6225880000000067", "6225880000000068", "6225880000000069", "6225880000000070",
    "6225880000000071", "6225880000000072", "6225880000000073", "6225880000000074", "6225880000000075",
    "6225880000000076", "6225880000000077", "6225880000000078", "6225880000000079", "6225880000000080",
    "6225880000000081", "6225880000000082", "6225880000000083", "6225880000000084", "6225880000000085",
    "6225880000000086", "6225880000000087", "6225880000000088", "6225880000000089", "6225880000000090",
    "6225880000000091", "6225880000000092", "6225880000000093", "6225880000000094", "6225880000000095",
    "6225880000000096", "6225880000000097", "6225880000000098", "6225880000000099", "6225880000000100",
    "6225880000000101", "6225880000000102", "6225880000000103", "6225880000000104", "6225880000000105",
    "6225880000000106", "6225880000000107", "6225880000000108", "6225880000000109", "6225880000000110",
    "6225880000000111", "6225880000000112", "6225880000000113", "6225880000000114", "6225880000000115",
    "6225880000000116", "6225880000000117", "6225880000000118", "6225880000000119", "6225880000000120",
    "6225880000000121", "6225880000000122", "6225880000000123", "6225880000000124", "6225880000000125",
    "6225880000000126", "6225880000000127", "6225880000000128", "6225880000000129", "6225880000000130",
    "6225880000000131", "6225880000000132", "6225880000000133", "6225880000000134", "6225880000000135",
    "6225880000000136", "6225880000000137", "6225880000000138", "6225880000000139", "6225880000000140",
    "6225880000000141", "6225880000000142", "6225880000000143", "6225880000000144", "6225880000000145",
    "6225880000000146", "6225880000000147", "6225880000000148", "6225880000000149", "6225880000000150",
    "6225880000000151", "6225880000000152", "6225880000000153", "6225880000000154", "6225880000000155",
    "6225880000000156", "6225880000000157", "6225880000000158", "6225880000000159", "6225880000000160",
    "6225880000000161", "6225880000000162", "6225880000000163", "6225880000000164", "6225880000000165",
    "6225880000000166", "6225880000000167", "6225880000000168", "6225880000000169", "6225880000000170",
    "6225880000000171", "6225880000000172", "6225880000000173", "6225880000000174", "6225880000000175",
    "6225880000000176", "6225880000000177", "6225880000000178", "6225880000000179", "6225880000000180",
    "6225880000000181", "6225880000000182", "6225880000000183", "6225880000000184", "6225880000000185",
    "6225880000000186", "6225880000000187", "6225880000000188", "6225880000000189", "6225880000000190",
    "6225880000000191", "6225880000000192", "6225880000000193", "6225880000000194", "6225880000000195",
    "6225880000000196", "6225880000000197", "6225880000000198", "6225880000000199", "6225880000000200",
    "6225880000000201", "6225880000000202", "6225880000000203", "6225880000000204", "6225880000000205",
    "6225880000000206", "6225880000000207", "6225880000000208", "6225880000000209", "6225880000000210",
    "6225880000000211", "6225880000000212", "6225880000000213", "6225880000000214", "6225880000000215",
    "6225880000000216", "6225880000000217", "6225880000000218", "6225880000000219", "6225880000000220",
    "6225880000000221", "6225880000000222", "6225880000000223", "6225880000000224", "6225880000000225",
    "6225880000000226", "6225880000000227", "6225880000000228", "6225880000000229", "6225880000000230",
    "6225880000000231", "6225880000000232", "6225880000000233", "6225880000000234", "6225880000000235",
    "6225880000000236", "6225880000000237", "6225880000000238", "6225880000000239", "6225880000000240",
    "6225880000000241", "6225880000000242", "6225880000000243", "6225880000000244", "6225880000000245",
    "6225880000000246", "6225880000000247", "6225880000000248", "6225880000000249", "6225880000000250",
    "6225880000000251", "6225880000000252", "6225880000000253", "6225880000000254", "6225880000000255",
    "6225880000000256", "6225880000000257", "6225880000000258", "6225880000000259", "6225880000000260",
    "6225880000000261", "6225880000000262", "6225880000000263", "6225880000000264", "6225880000000265",
    "6225880000000266", "6225880000000267", "6225880000000268", "6225880000000269", "6225880000000270",
    "6225880000000271", "6225880000000272", "6225880000000273", "6225880000000274", "6225880000000275",
    "6225880000000276", "6225880000000277", "6225880000000278", "6225880000000279", "6225880000000280",
    "6225880000000281", "6225880000000282", "6225880000000283", "6225880000000284", "6225880000000285",
    "6225880000000286", "6225880000000287", "6225880000000288", "6225880000000289", "6225880000000290",
    "6225880000000291", "6225880000000292", "6225880000000293", "6225880000000294", "6225880000000295",
    "6225880000000296", "6225880000000297", "6225880000000298", "6225880000000299", "6225880000000300",
    "6225880000000301", "6225880000000302", "6225880000000303", "6225880000000304", "6225880000000305",
    "6225880000000306", "6225880000000307", "6225880000000308", "6225880000000309", "6225880000000310",
    "6225880000000311", "6225880000000312", "6225880000000313", "6225880000000314", "6225880000000315",
    "6225880000000316", "6225880000000317", "6225880000000318", "6225880000000319", "6225880000000320",
    "6225880000000321", "6225880000000322", "6225880000000323", "6225880000000324", "6225880000000325",
    "6225880000000326", "6225880000000327", "6225880000000328", "6225880000000329", "6225880000000330",
    "6225880000000331", "6225880000000332", "6225880000000333", "6225880000000334", "6225880000000335",
    "6225880000000336", "6225880000000337", "6225880000000338", "6225880000000339", "6225880000000340",
    "6225880000000341", "6225880000000342", "6225880000000343", "6225880000000344", "6225880000000345",
    "6225880000000346", "6225880000000347", "6225880000000348", "6225880000000349", "6225880000000350",
    "6225880000000351", "6225880000000352", "6225880000000353", "6225880000000354", "6225880000000355",
    "6225880000000356", "6225880000000357", "6225880000000358", "6225880000000359", "6225880000000360",
    "6225880000000361", "6225880000000362", "6225880000000363", "6225880000000364", "6225880000000365",
    "6225880000000366", "6225880000000367", "6225880000000368", "6225880000000369", "6225880000000370",
    "6225880000000371", "6225880000000372", "6225880000000373", "6225880000000374", "6225880000000375",
    "6225880000000376", "6225880000000377", "6225880000000378", "6225880000000379", "6225880000000380",
    "6225880000000381", "6225880000000382", "6225880000000383", "6225880000000384", "6225880000000385",
    "6225880000000386", "6225880000000387", "6225880000000388", "6225880000000389", "6225880000000390",
    "6225880000000391", "6225880000000392", "6225880000000393", "6225880000000394", "6225880000000395",
    "6225880000000396", "6225880000000397", "6225880000000398", "6225880000000399", "6225880000000400",
    "6225880000000401", "6225880000000402", "6225880000000403", "6225880000000404", "6225880000000405",
    "6225880000000406", "6225880000000407", "6225880000000408", "6225880000000409", "6225880000000410",
    "6225880000000411", "6225880000000412", "6225880000000413", "6225880000000414", "6225880000000415",
    "6225880000000416", "6225880000000417", "6225880000000418", "6225880000000419", "6225880000000420",
    "6225880000000421", "6225880000000422", "6225880000000423", "6225880000000424", "6225880000000425",
    "6225880000000426", "6225880000000427", "6225880000000428", "6225880000000429", "6225880000000430",
    "6225880000000431", "6225880000000432", "6225880000000433", "6225880000000434", "6225880000000435",
    "6225880000000436", "6225880000000437", "6225880000000438", "6225880000000439", "6225880000000440",
    "6225880000000441", "6225880000000442", "6225880000000443", "6225880000000444", "6225880000000445",
    "6225880000000446", "6225880000000447", "6225880000000448", "6225880000000449", "6225880000000450",
    "6225880000000451", "6225880000000452", "6225880000000453", "6225880000000454", "6225880000000455",
    "6225880000000456", "6225880000000457", "6225880000000458", "6225880000000459", "6225880000000460",
    "6225880000000461", "6225880000000462", "6225880000000463", "6225880000000464", "6225880000000465",
    "6225880000000466", "6225880000000467", "6225880000000468", "6225880000000469", "6225880000000470",
    "6225880000000471", "6225880000000472", "6225880000000473", "6225880000000474", "6225880000000475",
    "6225880000000476", "6225880000000477", "6225880000000478", "6225880000000479", "6225880000000480",
    "6225880000000481", "6225880000000482", "6225880000000483", "6225880000000484", "6225880000000485",
    "6225880000000486", "6225880000000487", "6225880000000488", "6225880000000489", "6225880000000490",
    "6225880000000491", "6225880000000492", "6225880000000493", "6225880000000494", "6225880000000495",
    "6225880000000496", "6225880000000497", "6225880000000498", "6225880000000499", "6225880000000500",
    "6225880000000501", "6225880000000502", "6225880000000503", "6225880000000504", "6225880000000505",
    "6225880000000506", "6225880000000507", "6225880000000508", "6225880000000509", "6225880000000510",
    "6225880000000511", "6225880000000512", "6225880000000513", "6225880000000514", "6225880000000515",
    "6225880000000516", "6225880000000517", "6225880000000518", "6225880000000519", "6225880000000520",
    "6225880000000521", "6225880000000522", "6225880000000523", "6225880000000524", "6225880000000525",
    "6225880000000526", "6225880000000527", "6225880000000528", "6225880000000529", "6225880000000530",
    "6225880000000531", "6225880000000532", "6225880000000533", "6225880000000534", "6225880000000535",
    "6225880000000536", "6225880000000537", "6225880000000538", "6225880000000539", "6225880000000540",
    "6225880000000541", "6225880000000542", "6225880000000543", "6225880000000544", "6225880000000545",
    "6225880000000546", "6225880000000547", "6225880000000548", "6225880000000549", "6225880000000550",
    "6225880000000551", "6225880000000552", "6225880000000553", "6225880000000554", "6225880000000555",
    "6225880000000556", "6225880000000557", "6225880000000558", "6225880000000559", "6225880000000560",
    "6225880000000561", "6225880000000562", "6225880000000563", "6225880000000564", "6225880000000565",
    "6225880000000566", "6225880000000567", "6225880000000568", "6225880000000569", "6225880000000570",
    "6225880000000571", "6225880000000572", "6225880000000573", "6225880000000574", "6225880000000575",
    "6225880000000576", "6225880000000577", "6225880000000578", "6225880000000579", "6225880000000580",
    "6225880000000581", "6225880000000582", "6225880000000583", "6225880000000584", "6225880000000585",
    "6225880000000586", "6225880000000587", "6225880000000588", "6225880000000589", "6225880000000590",
    "6225880000000591", "6225880000000592", "6225880000000593", "6225880000000594", "6225880000000595",
    "6225880000000596", "6225880000000597", "6225880000000598", "6225880000000599", "6225880000000600",
    "6225880000000601", "6225880000000602", "6225880000000603", "6225880000000604", "6225880000000605",
    "6225880000000606", "6225880000000607", "6225880000000608", "6225880000000609", "6225880000000610",
    "6225880000000611", "6225880000000612", "6225880000000613", "6225880000000614", "6225880000000615",
    "6225880000000616", "6225880000000617", "6225880000000618", "6225880000000619", "6225880000000620",
    "6225880000000621", "6225880000000622", "6225880000000623", "6225880000000624", "6225880000000625",
    "6225880000000626", "6225880000000627", "6225880000000628", "6225880000000629", "6225880000000630",
    "6225880000000631", "6225880000000632", "6225880000000633", "6225880000000634", "6225880000000635",
    "6225880000000636", "6225880000000637", "6225880000000638", "6225880000000639", "6225880000000640",
    "6225880000000641", "6225880000000642", "6225880000000643", "6225880000000644", "6225880000000645",
    "6225880000000646", "6225880000000647", "6225880000000648", "6225880000000649", "6225880000000650",
    "6225880000000651", "6225880000000652", "6225880000000653", "6225880000000654", "6225880000000655",
    "6225880000000656", "6225880000000657", "6225880000000658", "6225880000000659", "6225880000000660",
    "6225880000000661", "6225880000000662", "6225880000000663", "6225880000000664", "6225880000000665",
    "6225880000000666", "6225880000000667", "6225880000000668", "6225880000000669", "6225880000000670",
    "6225880000000671", "6225880000000672", "6225880000000673", "6225880000000674", "6225880000000675",
    "6225880000000676", "6225880000000677", "6225880000000678", "6225880000000679", "6225880000000680",
    "6225880000000681", "6225880000000682", "6225880000000683", "6225880000000684", "6225880000000685",
    "6225880000000686", "6225880000000687", "6225880000000688", "6225880000000689", "6225880000000690",
    "6225880000000691", "6225880000000692", "6225880000000693", "6225880000000694", "6225880000000695",
    "6225880000000696", "6225880000000697", "6225880000000698", "6225880000000699", "6225880000000700",
    "6225880000000701", "6225880000000702", "6225880000000703", "6225880000000704", "6225880000000705",
    "6225880000000706", "6225880000000707", "6225880000000708", "6225880000000709", "6225880000000710",
    "6225880000000711", "6225880000000712", "6225880000000713", "6225880000000714", "6225880000000715",
    "6225880000000716", "6225880000000717", "6225880000000718", "6225880000000719", "6225880000000720",
    "6225880000000721", "6225880000000722", "6225880000000723", "6225880000000724", "6225880000000725",
    "6225880000000726", "6225880000000727", "6225880000000728", "6225880000000729", "6225880000000730",
    "6225880000000731", "6225880000000732", "6225880000000733", "6225880000000734", "6225880000000735",
    "6225880000000736", "6225880000000737", "6225880000000738", "6225880000000739", "6225880000000740",
    "6225880000000741", "6225880000000742", "6225880000000743", "6225880000000744", "6225880000000745",
    "6225880000000746", "6225880000000747", "6225880000000748", "6225880000000749", "6225880000000750",
    "6225880000000751", "6225880000000752", "6225880000000753", "6225880000000754", "6225880000000755",
    "6225880000000756", "6225880000000757", "6225880000000758", "6225880000000759", "6225880000000760",
    "6225880000000761", "6225880000000762", "6225880000000763", "6225880000000764", "6225880000000765",
    "6225880000000766", "6225880000000767", "6225880000000768", "6225880000000769", "6225880000000770",
    "6225880000000771", "6225880000000772", "6225880000000773", "6225880000000774", "6225880000000775",
    "6225880000000776", "6225880000000777", "6225880000000778", "6225880000000779", "6225880000000780",
    "6225880000000781", "6225880000000782", "6225880000000783", "6225880000000784", "6225880000000785",
    "6225880000000786", "6225880000000787", "6225880000000788", "6225880000000789", "6225880000000790",
    "6225880000000791", "6225880000000792", "6225880000000793", "6225880000000794", "6225880000000795",
    "6225880000000796", "6225880000000797", "6225880000000798", "6225880000000799", "6225880000000800",
    "6225880000000801", "6225880000000802", "6225880000000803", "6225880000000804", "6225880000000805",
    "6225880000000806", "6225880000000807", "6225880000000808", "6225880000000809", "6225880000000810",
    "6225880000000811", "6225880000000812", "6225880000000813", "6225880000000814", "6225880000000815",
    "6225880000000816", "6225880000000817", "6225880000000818", "6225880000000819", "6225880000000820",
    "6225880000000821", "6225880000000822", "6225880000000823", "6225880000000824", "6225880000000825",
    "6225880000000826", "6225880000000827", "6225880000000828", "6225880000000829", "6225880000000830",
    "6225880000000831", "6225880000000832", "6225880000000833", "6225880000000834", "6225880000000835",
    "6225880000000836", "6225880000000837", "6225880000000838", "6225880000000839", "6225880000000840",
    "6225880000000841", "6225880000000842", "6225880000000843", "6225880000000844", "6225880000000845",
    "6225880000000846", "6225880000000847", "6225880000000848", "6225880000000849", "6225880000000850",
    "6225880000000851", "6225880000000852", "6225880000000853", "6225880000000854", "6225880000000855",
    "6225880000000856", "6225880000000857", "6225880000000858", "6225880000000859", "6225880000000860",
    "6225880000000861", "6225880000000862", "6225880000000863", "6225880000000864", "6225880000000865",
    "6225880000000866", "6225880000000867", "6225880000000868", "6225880000000869", "6225880000000870",
    "6225880000000871", "6225880000000872", "6225880000000873", "6225880000000874", "6225880000000875",
    "6225880000000876", "6225880000000877", "6225880000000878", "6225880000000879", "6225880000000880",
    "6225880000000881", "6225880000000882", "6225880000000883", "6225880000000884", "6225880000000885",
    "6225880000000886", "6225880000000887", "6225880000000888", "6225880000000889", "6225880000000890",
    "6225880000000891", "6225880000000892", "6225880000000893", "6225880000000894", "6225880000000895",
    "6225880000000896", "6225880000000897", "6225880000000898", "6225880000000899", "6225880000000900",
    "6225880000000901", "6225880000000902", "6225880000000903", "6225880000000904", "6225880000000905",
    "6225880000000906", "6225880000000907", "6225880000000908", "6225880000000909", "6225880000000910",
    "6225880000000911", "6225880000000912", "6225880000000913", "6225880000000914", "6225880000000915",
    "6225880000000916", "6225880000000917", "6225880000000918", "6225880000000919", "6225880000000920",
    "6225880000000921", "6225880000000922", "6225880000000923", "6225880000000924", "6225880000000925",
    "6225880000000926", "6225880000000927", "6225880000000928", "6225880000000929", "6225880000000930",
    "6225880000000931", "6225880000000932", "6225880000000933", "6225880000000934", "6225880000000935",
    "6225880000000936", "6225880000000937", "6225880000000938", "6225880000000939", "6225880000000940",
    "6225880000000941", "6225880000000942", "6225880000000943", "6225880000000944", "6225880000000945",
    "6225880000000946", "6225880000000947", "6225880000000948", "6225880000000949", "6225880000000950",
    "6225880000000951", "6225880000000952", "6225880000000953", "6225880000000954", "6225880000000955",
    "6225880000000956", "6225880000000957", "6225880000000958", "6225880000000959", "6225880000000960",
    "6225880000000961", "6225880000000962", "6225880000000963", "6225880000000964", "6225880000000965",
    "6225880000000966", "6225880000000967", "6225880000000968", "6225880000000969", "6225880000000970",
    "6225880000000971", "6225880000000972", "6225880000000973", "6225880000000974", "6225880000000975",
    "6225880000000976", "6225880000000977", "6225880000000978", "6225880000000979", "6225880000000980",
    "6225880000000981", "6225880000000982", "6225880000000983", "6225880000000984", "6225880000000985",
    "6225880000000986", "6225880000000987", "6225880000000988", "6225880000000989", "6225880000000990",
    "6225880000000991", "6225880000000992", "6225880000000993", "6225880000000994", "6225880000000995",
    "6225880000000996", "6225880000000997", "6225880000000998", "6225880000000999", "6225880000001000"
  ]
}

### 说明
# 大数据集充值测试，包含1000张卡
# 每张卡充值100元，赠送10元
# 批次号：BATCH202501070100
# 充值订单号：RO202501070100（对应推送接口的订单）
# 总金额：1000 * (100 + 10) = 110,000元 = 11,000,000分
# 适合性能测试和大批量充值场景测试
# 注意：此测试可能需要较长时间处理，请确保系统有足够的处理能力

### 充值订单充值接口测试 - 分批充值（100张卡）
POST http://localhost:8080/card-kam/ysht/recharge-order/recharge
Content-Type: application/json

{
  "batchNo": "BATCH202501070101",
  "contractNo": "CNT20250107100",
  "rechargeOrderNo": "RO202501070101",
  "everyCardRechargeAmount": 10000,
  "everyCardPresentAmount": 1000,
  "operator": "测试操作员",
  "cardNos": [
    "6225880000002001", "6225880000002002", "6225880000002003", "6225880000002004", "6225880000002005",
    "6225880000002006", "6225880000002007", "6225880000002008", "6225880000002009", "6225880000002010",
    "6225880000002011", "6225880000002012", "6225880000002013", "6225880000002014", "6225880000002015",
    "6225880000002016", "6225880000002017", "6225880000002018", "6225880000002019", "6225880000002020",
    "6225880000002021", "6225880000002022", "6225880000002023", "6225880000002024", "6225880000002025",
    "6225880000002026", "6225880000002027", "6225880000002028", "6225880000002029", "6225880000002030",
    "6225880000002031", "6225880000002032", "6225880000002033", "6225880000002034", "6225880000002035",
    "6225880000002036", "6225880000002037", "6225880000002038", "6225880000002039", "6225880000002040",
    "6225880000002041", "6225880000002042", "6225880000002043", "6225880000002044", "6225880000002045",
    "6225880000002046", "6225880000002047", "6225880000002048", "6225880000002049", "6225880000002050",
    "6225880000002051", "6225880000002052", "6225880000002053", "6225880000002054", "6225880000002055",
    "6225880000002056", "6225880000002057", "6225880000002058", "6225880000002059", "6225880000002060",
    "6225880000002061", "6225880000002062", "6225880000002063", "6225880000002064", "6225880000002065",
    "6225880000002066", "6225880000002067", "6225880000002068", "6225880000002069", "6225880000002070",
    "6225880000002071", "6225880000002072", "6225880000002073", "6225880000002074", "6225880000002075",
    "6225880000002076", "6225880000002077", "6225880000002078", "6225880000002079", "6225880000002080",
    "6225880000002081", "6225880000002082", "6225880000002083", "6225880000002084", "6225880000002085",
    "6225880000002086", "6225880000002087", "6225880000002088", "6225880000002089", "6225880000002090",
    "6225880000002091", "6225880000002092", "6225880000002093", "6225880000002094", "6225880000002095",
    "6225880000002096", "6225880000002097", "6225880000002098", "6225880000002099", "6225880000002100"
  ]
}

### 说明
# 分批充值测试，包含100张卡
# 每张卡充值100元，赠送10元
# 适合中等规模批量充值测试
# 可以用于验证分批处理逻辑