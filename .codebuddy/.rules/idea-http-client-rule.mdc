---
description: 使用http工具验收测试接口使用
globs:
alwaysApply: false
---
# IntelliJ IDEA HTTP Client 最佳实践规则

## 1. 文件组织规范

### 文件命名规范
- 使用描述性文件名：`{模块}_{功能}_{数量}.http`
- 示例：`rest-api_recharge_10.http`、`rest-api_order_push_1000.http`
- 批量测试文件按数据量区分：`_10.http`（少量数据）、`_1000.http`（大量数据）

### 文件结构组织
- 每个HTTP文件专注于特定功能模块
- 相关的请求放在同一个文件中
- 使用分隔符`###`分隔不同的请求

## 2. 请求格式规范

### 基本请求格式
```http
### 请求描述
POST {{host}}/api/endpoint
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "param1": "value1",
  "param2": "value2"
}
```

### 请求注释规范
- 每个请求前必须添加描述性注释
- 使用`###`作为请求分隔符和注释标识
- 注释应简洁明了，说明请求目的

### 变量使用规范
- 使用`{{variable}}`格式定义变量
- 常用变量：`{{host}}`、`{{token}}`、`{{userId}}`等
- 在`http-client.env.json`中定义环境变量

## 3. 环境配置管理

### 环境文件配置
创建`http-client.env.json`文件：
```json
{
  "dev": {
    "host": "http://localhost:8080",
    "token": "dev-token-here"
  },
  "test": {
    "host": "https://test-api.example.com",
    "token": "test-token-here"
  },
  "prod": {
    "host": "https://api.example.com",
    "token": "prod-token-here"
  }
}
```

### 私有环境配置
- 敏感信息放在`http-client.private.env.json`中
- 该文件应添加到`.gitignore`中，不提交到版本控制

## 4. 请求头管理

### 通用请求头
```http
Content-Type: application/json
Accept: application/json
User-Agent: IntelliJ-HTTP-Client
```

### 认证头处理
```http
# Bearer Token认证
Authorization: Bearer {{token}}

# Basic认证
Authorization: Basic {{basicAuth}}

# 自定义认证头
X-API-Key: {{apiKey}}
```

## 5. 请求体规范

### JSON格式规范
- 使用标准JSON格式
- 保持良好的缩进和格式化
- 复杂对象使用多行格式

### 批量数据处理
- 大量数据测试时，考虑使用外部文件
- 使用`< file.json`语法引用外部文件
- 示例：`< ./test-data/recharge-orders.json`

## 6. 响应处理

### 响应验证脚本
```javascript
> {%
client.test("Request executed successfully", function() {
    client.assert(response.status === 200, "Response status is not 200");
});

client.test("Response has correct structure", function() {
    client.assert(response.body.hasOwnProperty("code"), "Response missing code field");
    client.assert(response.body.code === "0000", "Response code is not success");
});
%}
```

### 变量提取
```javascript
> {%
client.global.set("orderId", response.body.data.orderId);
client.global.set("timestamp", new Date().getTime());
%}
```

## 7. 测试数据管理

### 测试数据生成
- 使用动态变量：`{{$timestamp}}`、`{{$uuid}}`、`{{$randomInt}}`
- 创建可重复执行的测试数据
- 避免硬编码测试数据

### 数据清理
- 测试后清理创建的数据
- 使用DELETE请求或专门的清理接口
- 考虑使用事务回滚机制

## 8. 错误处理和调试

### 错误场景测试
```http
### 测试参数验证错误
POST {{host}}/api/recharge
Content-Type: application/json

{
  "amount": -100,
  "cardNo": ""
}

> {%
client.test("Validation error handled correctly", function() {
    client.assert(response.status === 400, "Should return 400 for invalid data");
});
%}
```

### 调试技巧
- 使用`client.log()`输出调试信息
- 检查响应头和状态码
- 使用断点调试响应处理脚本

## 9. 性能测试

### 批量请求测试
- 创建多个并发请求
- 使用循环生成大量测试数据
- 监控响应时间和成功率

### 压力测试注意事项
- 避免对生产环境进行压力测试
- 控制请求频率，避免对服务器造成过大压力
- 记录性能指标和响应时间

## 10. 团队协作规范

### 版本控制
- HTTP文件提交到版本控制系统
- 私有环境配置文件不提交
- 添加适当的`.gitignore`规则

### 文档说明
- 在项目根目录创建`HTTP_TEST_README.md`
- 说明测试文件的用途和使用方法
- 提供环境配置示例

### 代码审查
- HTTP测试文件也需要代码审查
- 检查请求的合理性和安全性
- 确保测试覆盖主要业务场景

## 11. 安全注意事项

### 敏感信息保护
- 不在HTTP文件中硬编码密码、token等敏感信息
- 使用环境变量或私有配置文件
- 定期更新测试用的认证信息

### 生产环境保护
- 明确标识生产环境配置
- 避免误操作生产环境
- 使用不同的认证机制区分环境

## 12. 最佳实践总结

### 文件组织
1. 按功能模块组织HTTP文件
2. 使用描述性文件名和注释
3. 合理使用分隔符组织请求

### 变量管理
1. 统一使用环境变量配置
2. 敏感信息单独管理
3. 使用动态变量提高测试灵活性

### 测试设计
1. 覆盖正常和异常场景
2. 使用响应验证脚本
3. 设计可重复执行的测试

### 团队协作
1. 建立统一的命名规范
2. 提供清晰的使用文档
3. 定期维护和更新测试文件

## 13. 常用代码片段

### 标准POST请求模板
```http
### {{description}}
POST {{host}}/api/{{endpoint}}
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "param1": "{{value1}}",
  "param2": "{{value2}}"
}

> {%
client.test("Request successful", function() {
    client.assert(response.status === 200);
    client.assert(response.body.code === "0000");
});
%}
```

### 批量数据测试模板
```http
### 批量{{operation}}测试
POST {{host}}/api/batch/{{endpoint}}
Content-Type: application/json
Authorization: Bearer {{token}}

< ./test-data/batch-{{operation}}.json

> {%
client.test("Batch operation successful", function() {
    client.assert(response.status === 200);
    client.assert(response.body.successCount > 0);
});
%}
```

通过遵循这些规范，可以提高HTTP测试的效率、可维护性和团队协作效果。