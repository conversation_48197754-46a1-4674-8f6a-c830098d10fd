# prd-rule

## 1. 项目背景
大客户卡续充业务现有流程：大客户在财管合同系统中提交卡续充申请单，审批通过后，销售拿着卡去线下影城完成充值。这套流程中收入申请单和充值动作是分开的，导致充值动作无法管理，且数据分析存在诸多不便，例如无法区分充值的卡类型编码。

|区域|总计|卡续充|
| ---- | ---- | ---- |
|收入金额|5,273,145|5,273,145|
|合同数|301|301|
|卡数量|10,637|10,637|
|客户数|249|249|

为提高效率和加强管控，卡续充业务流程变更为：财管提交收入申请单并提交卡号，在万达云完成对应卡号的充值。具体流程如下：
1. 第一步：财管提交收入申请单  
   合同系统储值卡续充增加字段【卡号】，业务人员导入卡号。

|序号|影城名称|业务事项*|卡号|充值金额|数量*|总金额|在途开票金额|已开票金|已认领金额|已提成基数|单张卡赠送金额|
| ---- | ---- | ---- | ---- | ---- | ---- | ---- | ---- | ---- | ---- | ---- | ---- |
| - | - | - | - | - | - | - | - | - | - | - | - |
2. 第二步：财管系统请求万达云校验卡号是否可充值
3. 第三步：收入申请单审批结束后，推送卡续充充值订单到万达云
4. 第四步：收入申请单在财管系统确认收款后，财管合同系统请求万达云卡充值接口，完成卡充值


## 2. 需求说明
### 2.1 接口需求
#### 2.1.1 续充卡验证接口-产研提供
提供卡验证接口，财管提供卡号、充值金额、影城、客户编码（非会员编码），请求该接口验证以下内容：
- a. 校验当前卡号状态为正常状态；  
  该卡号的发卡服务对应的客户，和本次卡续充对应的客户为同一客户；  
  该卡号对应的卡类型信息中设置的可充值。

|基本信息|内容|
| ---- | ---- |
|卡类别|储值|
|负责单位|院线|
|是否允许区域修改|不允许|
|卡类型名称|计次卡(安徽省)|
|卡类型编码|120000121|
|说明|1、本卡限安徽省内万达直营影城使用；2、本卡实际扣费以影片发行价为准；3、本卡线上购票收取手续费3元/张，见面会、首场、VIP厅等特殊场次不可用；4、本卡不记名、不兑现、不补卡、不可续充；5、春节在补足发行价的基础上加10元；6、本卡一经使用，即表示持卡人接受万达影城会员卡使用章程及相关业务规定；7、万达影城可在法律允许范围内对产品细则做出适当调整。|
|购卡说明-是否可售|是|
|购卡说明-是否可用|是|
|购卡说明-不支持操作类型|充值、补卡换卡、卡升级、卡延期、退卡挂失、转赠|
|卡类型适用范围-适用范围|合肥|
|卡类型适用范围-使用范围|可在使用本卡类型的所有影城通用|

- d. 本次充值金额，满足该卡号对应的卡类型在当前影城对应城市的最低充值限制。

|卡设置|内容|
| ---- | ---- |
|工本费|不限|
|延期费|不限|
|是否可用于卖品|是|
|最低充值金额(一线)|100.00元|
|最低充值金额(二线)|100.00元|
|最低充值金额(三/四线)|100.00元|
|补卡|不允许|
|有效期规则|永久有效|
|充值延期规则|不可充值延期|
|延期规则|不可延期|
|免年费条件|每年最低消费3次|
|年费|10.00元|
|数量限制-每场限购票数|不限|
|数量限制-每日限购票数|4张|
|数量限制-每片每日限购票数|不限|
|数量限制-每片总限购票数|不限|

#### 2.1.2 充值订单推送接口-产研提供
提供卡续充充值订单推送接口，财管合同审批通过后进行合同推送，推送的信息展示在列表中（见页面需求）。推送信息需包含充值订单号、卡号、充值金额、赠送金额等字段。

#### 2.1.3 充值订单变更接口-产研提供
提供充值订单变更接口，主要用于充值订单作废的场景。充值订单作废校验逻辑：本充值订单里的所有卡不可有充值记录。

#### 2.1.4 充值订单充值接口-产研提供
提供充值接口，支持对充值订单中的部分卡号进行充值，请求充值需带合同号、充值订单号、卡号、充值金额、赠送金额。该接口需校验：
1. 校验当前卡号状态为正常状态
2. 该卡号对应的卡类型信息中设置的可充值
3. 卡充值金额、赠送金额与充值订单金额一致
4. 该充值订单的该卡号是否有成功充值的记录（如有，则不允许再次充值）
5. 充值金额需传充值金额和赠送金额、充值人

校验完成后完成卡充值，增加一条卡服务记录，并调用充值结果回调接口，将结果返回给财管且更新状态。卡服务记录中需记录对应的充值来源：充值订单号。

#### 2.1.5 充值结果回调接口-财管提供
充值为异步充值，充值完成后，将充值订单号、充值请求流水、卡号、充值金额、赠送金额、充值结果等信息，请求该接口通知财管。

### 2.2 页面需求
在万达云【营销平台】增加菜单【卡管理-卡续充充值订单】

#### 1. 充值订单列表页
请看截图

|分类|字段|说明|
| ---- | ---- | ---- |
|查询条件|合同编码|输入框，精准查询|
|查询条件|客户编码|输入框，精准查询|
|查询条件|客户名称|输入框，模糊查询|
|查询条件|销售员|输入框，模糊查询|
|查询条件|充值订单|输入框，精准查询|
|查询条件|创建时间|日期区间|
|查询条件|状态|默认为全部，下拉框单选，状态见下说明|
|查询条件|区域|区域和影城有级联关系，进行权限控制|
|查询条件|影城|总部权限：可见所有，区域为全部区域，影城为全部影城，均可下拉查询；区域权限：区域默认为当前区域不可切换其他区域，影城可选为当前区域下的任何影城；影城权限：区域默认为当前影城对应的区域，影城为当前影城，无法选择其他区域和其他影城|
|列表|合同编码|展示|
|列表|充值订单|展示|
|列表|区域|展示|
|列表|影城|展示|
|列表|客户编码|展示|
|列表|客户名称|展示|
|列表|销售员|展示中文姓名|
|列表|卡数量|当前充值订单的卡数量|
|列表|总充值金额|当前订单的总充值金额（含赠送金额），卡数量*单卡（充值金额+赠送金额）|
|列表|状态|展示，见下说明|
|列表|创建时间|精确到秒，展示充值订单推送的时间|
|列表|详情|跳转到新页面，从详情页返回需记录列表的查询条件|

#### 2. 充值订单状态说明
|状态|状态说明|
| ---- | ---- |
|待充值|财管合同审批通过后，将充值订单推送，推送成功|
|部分充值|充值订单中的卡号，部分卡号充值成功，部分卡号充值失败或未充值|
|充值成功|充值订单中的卡号，全部卡号充值成功|
|充值失败|充值订单中的卡号，有充值失败的卡号，没有充值成功的卡号，可包含部分卡号未充值|

#### 3. 充值订单详情页
请看截图

|分类|字段|说明|
| ---- | ---- | ---- |
|充值订单|充值订单|展示|
|基础信息|合同编码| - |
|基础信息|客户编码/客户名称| - |
|基础信息|销售员万信号/销售姓名| - |
|基础信息|区域/影城| - |
|基础信息|卡数量| - |
|基础信息|总充值金额| - |
|变更日志| - |展示该充值订单的所有变更日志，包括：充值订单创建、充值订单作废、充值（需记录充值的卡张数）|
|充值明细-查询条件|卡号|输入框，精确查询|
|充值明细-查询条件|充值时间|日期查询区间|
|充值明细-查询条件|充值状态|默认全部，下拉框可选择，状态见下说明|
|充值明细-列表| - |该列表展示该充值订单所有的充值请求的充值记录，一个卡号如有多次充值请求，则展示多条|
|充值明细-列表|卡号|脱敏展示|
|充值明细-列表|卡类型编码/卡类型名称|根据卡号查询的卡类型编码和名称|
|充值明细-列表|充值金额|充值金额和赠送金额单独展示|
|充值明细-列表|赠送金额| - |
|充值明细-列表|余额变动|充值金额+赠送金额|
|充值明细-列表|充值前余额|本次充值前的余额|
|充值明细-列表|充值后余额|本次充值后的余额|
|充值明细-列表|充值状态|见下说明|

#### 4. 卡充值状态说明
卡号的充值状态：未充值、充值中、充值成功、充值失败

|状态|说明|
| ---- | ---- |
|未充值|财管没有在该充值订单中对该卡号请求充值|
|充值中|正在处理该笔卡的充值|
|充值成功|处理完成，充值结果为成功|
|充值失败|处理完成，充值结果为失败|

### 2.3 卡服务记录需求
卡服务记录需增加一条“充值”的服务类型，按照以下信息记录（未标颜色的表示与现在的充值服务一致）：

|时间:|营业日|服务类型|卡类别|卡类型|###|服务大区|
| ---- | ---- | ---- | ---- | ---- | ---- | ---- |
|2025/7/30 18:52|2025/7/30|充值3|储值卡|影享卡|8600096400025 455|南区|