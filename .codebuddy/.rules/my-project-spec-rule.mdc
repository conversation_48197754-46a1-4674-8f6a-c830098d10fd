---
description:
globs:
alwaysApply: true
---

# my-project-spec-rule

## 项目本地运行调试使用的jdk库路径

- /Library/Java/JavaVirtualMachines/jdk-11.0.26+4/Contents/Home

## 项目架构概述

### project项目名

- card-kam

### 系统架构

- 编程语言使用java11开发
- 基于Spring Cloud的分布式微服务架构
- 服务发现和配置管理：Nacos
- 分布式任务调度：XXL-Job
- 消息队列：RocketMQ
- 数据存储：MongoDB、MySQL
- 缓存：Redis
- 搜索引擎：Elasticsearch（Es）
- 使用gitlab做版本控制，使用ci/cd做持续集成
- 所有微服务模块发布到k8s上运行，由Rancher管理

### 模块组织规范

#### 标准项目组织模块

- `{project}-admin-service`（管理后台RPC服务）
- `{project}-front-service`（前端RPC服务）
- `{project}-common`（RPC服务、consumer、task的共用模块）
- `{project}-task`（后台定时任务模块）
- `{project}-consumer`（消息处理模块）
- `{project}-front-api`（前端API接口模块）
- `{project}-admin-api`（管理后台API接口模块）

#### 标准项目每个模块采用maven标准结构

#### 标准项目组织模块结构树形图

```
project总目录
├── {project}-common/      # 公共maven模块
│   ├── contract/          # 契约层maven模块
│   │   ├── constant/      # 常量定义和BusinessException类定义
│   │   └── enums/         # 枚举定义
│   └── provider/          # 服务层maven模块
│       ├── biz/           # 公共业务逻辑
│       ├── cache/         # 缓存层
│       ├── dao/           # 数据访问层
│       └── po/            # 持久化对象
├── {project}-consumer/    # 消息消费者maven模块
│   ├── consumers/         # 具体Consumer消费类实现
│   ├── biz/               # 逻辑层
│   ├── service/           # 服务层
│   ├── mapper/            # 对象映射
│   └── msg/               # 消息对象
├── {project}-task/        # 定时任务maven模块
│   ├── task/              # 具体任务类实现
│   ├── facade/            # 外观层
│   └── param/             # task参数对象
├── {project}-admin-api/   # 管理后台API maven模块
│   ├── controller/        # 具体业务接口类实现
│   ├── facade/            # 外观层
│   └── param/             # 接口参数对象
├── {project}-admin-service/   # 管理后台服务maven模块
│   ├── contract/          # 契约层maven模块
│   │   ├── servcie/       # 业务接口类
│   │   └── dto/           # 接口参数与返回值对象
│   └── provider/          # 服务层maven模块
│       ├── controller/    # 业务接口实现类
│       ├── biz/           # 业务逻辑
│       └── mapper/        # 对象映射
├── {project}-front-api/   # 前端API maven模块
│   ├── controller/        # 具体业务接口类实现
│   ├── facade/            # 外观层
│   ├── param/             # 接口参数对象
└── {project}-front-service/# 前端服务
    ├── contract/          # 契约层maven模块
    │   ├── servcie/       # 业务接口类
    │   └── dto/           # 接口参数与返回值对象
    └── provider/          # 服务层maven模块
        ├── controller/    # 业务接口实现类
        ├── biz/           # 业务逻辑
        └── mapper/        # 对象映射

```

#### 模块之间依赖关系

- {project}-front-api 依赖 {project}-front-service里的contract模块
- {project}-admin-api 依赖 {project}-admin-service里的contract模块
- {project}-common里的provider模块依赖同级的contract
- {project}-admin-servcie、{project}-front-service、{project}-consumer、{project}-task 依赖 {project}-common里的provider模块

## 代码生成指导

当需要创建新的功能时，请遵循以下模式：

- api模块的controller接口不要用valid注解校验，直接放到依赖的rpc接口valid注解校验
- 业务代码锁使用Redis分布式锁工具类方法`RedisLockSupport.lockTemplate`
- 事务不要使用spring事务
- 事务要使用自有`jsd`持久化框架的事务，实例参看文档 `mysql jsd事务实现` 一节
- 在`contract`模块定义服务接口和DTO
- 在`service`模块实现业务逻辑
- `service`每个rpc接口由Controller实现，接口逻辑依赖对应一个`Biz`业务逻辑类
- 如果`service`rpc接口返回值使用`R`类型作为统一响应格式
- 在`api`模块提供HTTP接口
- 如需消息处理，在consumer模块添加消费者`Consumer`类
- 添加相应的测试用例
- 日志记录使用注解`@Slf4j`
- `Consumer``topic`定义参看`消息处理Consumer类topic定义规范`
- 各模块发送消息请按`消息发送规范`使用
- 如果有调用三方api推送数据功能，请使用工具类RetryUtil，参照`调用三方系统api重试规范`
- 每次更新功能后，需要运行单元测试验证
- 每次追加功能后，如果已经存在对应的单测试类，需要追加对应的功能测试，注意不要把以前的单元测试覆盖了
- 所有新增的类都应该包含`@author`注解，作者为`fu.wei`。

## 数据库表规范

- 所有金额字段存储int型，单位默认是分

## 代码风格规范

### 类命名规范

- 实体类：以`Po`结尾（如：`MissionPo`、`RecordPo`）
- DTO类：以`Dto`结尾（如：`MemberDto`）
- VO类：以`Vo`结尾（如：`MissionVo`）
- 枚举类：以`Enum`结尾（如：`MissionTypeEnum`、`ErrorCodeEnum`）
- 消费者类：以`Consumer`结尾（如：`PersonalConsumer`）
- 业务逻辑类：以`Biz`结尾（如：`PersonalMissionExecuteBiz`）
- 检查器类：以`Checker`结尾（如：`BrownPageChecker`）
- 缓存类：以`Cache`结尾（如：`MissionCache`）
- 映射器类：以`Mapper`结尾（如：`PersonalMapper`）
- 外观类：以`Facade`结尾（如：`MemberFacade`）

### 方法命名规范

- 查询方法：以`get`、`find`、`list`开头
- 业务处理方法：以`process`、`execute`、`handle`开头
- 缓存操作方法：以`set`、`delete`、`remove`开头
- 保存方法：使用`save`（新增或更新）
- 分页查询：使用`pageList`

### 注解使用规范

#### 类级别注解

- Controller类：`@Slf4j` + `@RestController` + `@AllArgsConstructor`
- Service/Biz类：`@Slf4j` + `@Component` + `@AllArgsConstructor`（或使用@Autowired）
- Dao类：`@Repository`
- 消费者类：`@Slf4j` + `@MessageConsumer(topic = Topic.XXX)`
- 实体类：`@Data` + `@Entity`（MongoDB）或`@Table`（MySQL）
- 缓存类：`@Component` + `@AllArgsConstructor`

#### @author注解格式

```java
/**
 * 类描述
 *
 * <AUTHOR>
 **/
```

### http接口注释规范

- {project}-admin-api和{project}-front-api模块里接口注释规范要符合idea插件apiDoc规范，用于自动生成文档

## 开发最佳实践

### 日志记录规范

- 所有Controller方法必须记录入参和返回值
- 关键业务操作必须记录日志
- 使用`JsonUtils.encode()`序列化复杂对象
- 日志格式：`log.info("操作描述入参:{}", param)`、`log.info("操作描述返回:{}", result)`

### 异常处理规范

- 使用统一异常类`BusinessException`，存放在common/contract里
- 使用统一的错误码枚举`ErrorCodeEnum`，存放在common/contract里

### 消息处理模式

消费者处理消息的标准流程：

- 记录接收到的消息日志
- 构建业务参数对象
- 调用业务逻辑处理
- 记录处理结果日志

### 数据访问规范

#### 数据持久化框架使用公司内部实现jsd框架，通常在common模块provider目录里引用

```
<dependency>
    <groupId>wanda.cloud</groupId>
    <artifactId>jsd-spring-boot-starter</artifactId>
</dependency>
```

#### MongoDB使用规范

```java

@Entity(value = "collection_name", noClassnameStored = true)
@AutoIncrementId  // 自增ID
public class EntityPo implements DbEntity<Long> {
    @Id
    private Long id;
    // 其他字段...
}
```

#### MySQL分片规范

```java

@Table(name = "table_name")
@Sharding(dbKeys = "member_no")  // 分片键
public class EntityPo implements DbEntity<Long> {
    @Id
    private Long id;
    // 其他字段...
}
```

#### Dao层实现规范

- MongoDB：继承`MgoDao<EntityPo, Long>`
- MySQL单库：继承`JsdReadWriteDao<EntityPo, Long>`

```java
import java.time.LocalDateTime;
import wanda.stark.db.jsd.JsdReadWriteDao;
import wanda.stark.db.jsd.lang.Database;
import wanda.stark.db.jsd.lang.Transaction;
/**
 * 充值订单DAO
 * <AUTHOR>
 */
@Repository
public class RechargeOrderDao extends JsdReadWriteDao<RechargeOrder, Long> {

    public RechargeOrderDao() {
        super(DB_NAME);
    }

    /**
     * 事务处理
     *
     * @param consumer
     */
    public void transactionHandle(Consumer<Transaction> consumer) {
        openWrite().begin(consumer);
    }
    
    /**
     * 根据条件参数查单个对象
     */
    public RechargeOrder findByRechargeOrderNo(String rechargeOrderNo) {
        return openWrite().select(RechargeOrder.class)
                .where(f("recharge_order_no", rechargeOrderNo))
                .result()
                .one(RechargeOrder.class);
    }

    /**
     * 根据条件参数查列表
     */
    public List<RechargeOrder> findByRechargeOrderNo(String rechargeOrderNo, String tt) {
        return openWrite().select(RechargeOrder.class)
                .where(f("recharge_order_no", rechargeOrderNo).add("tt", tt))
                .result()
                .all(RechargeOrder.class);
    }


    /**
     * 已经获得对象，自定义更新列
     */
    public void updateByColumns(RechargeOrder rechargeOrder, String... columns) {
        openWrite().update(rechargeOrder, columns).result();
    }

    /**
     * 直接根据选中字段，更新自定列
     */
    public void updateStatus(String rechargeOrderNo, RechargeOrderStatus status) {
        openWrite().update(RechargeOrder.class)
                .set(uv("status", status).add("update_time", LocalDateTime.now()))
                .where(f("recharge_order_no", rechargeOrderNo))
                .result();
    }

    /**
     * 删除操作
     */
    public void deleteByRechargeOrderNo(String rechargeOrderNo) {
        openWrite().delete(RechargeOrder.class)
                .where(f("recharge_order_no", rechargeOrderNo))
                .result();
    }
}
```

- MySQL分片：继承`JsdShardDao<EntityPo, Long>`
- 使用MySQL分片的话统一使用 指定分片号 作为分片键

#### mysql jsd事务实现

- MySQL jsd事务实现，一般在biz类里用主表dao实现，实例如下

```java
public class RechargeOrderPushServiceBiz {
    private final RechargeOrderDao rechargeOrderDao;

    public void save() {
        ....
        //事务处理
        rechargeOrderDao.transactionHandle(tx -> {
            // 3. 保存充值订单基本信息
            tx.insert(rechargeOrder).result(false);
            // 4. 批量保存卡信息
            tx.insert(cardInfos).result(false);
            // 5. 记录操作日志
            tx.insert(operationLog).result(false);
        });
        ....
    }
}
```

### 工具类规范

- 工具类统一放到common模块provider里util包下
- 人民币换算工具类MoneyUtil
- Redis分布式锁工具类RedisLockSupport

### 缓存使用规范

- 缓存类使用`@Component`注解
- 提供`delete()`方法清除缓存
- 数据变更后及时清除相关缓存
- 使用配置文件管理缓存TTL

### 测试规范

- 测试类使用org.junit.jupiter.api包下的`@Test`注解标记方法
- 单元测试加`@DisplayName`注解标记方法
- 测试方法命名：`test{功能}`
- 为核心Biz类也提供全面场景单元测试
- 提供完整的HTTP请求测试示例

#### 单元测试

```java

@ExtendWith(MockitoExtension.class)
class MemberServiceTest {

    @Mock
    private MemberDao memberDao;

    @InjectMocks
    private MemberService memberService;

    @Test
    void testFindByNo() {
        // Given
        String memberNo = "123456";
        MemberPo memberPo = new MemberPo();
        memberPo.setMemberNo(memberNo);

        when(memberDao.findByMemberNo(memberNo)).thenReturn(memberPo);

        // When
        MemberDto result = memberService.findByNo(memberNo);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getMemberNo()).isEqualTo(memberNo);
    }
}
```

### 配置管理规范

- 使用`application-{profile}.yml`格式
- 敏感配置通过Nacos配置中心管理
- 数据库连接配置统一管理
- 缓存配置使用`config-map`方式

### 服务间调用规范

- 使用RPC方式进行服务间调用
- 统一使用契约接口定义
- 使用Feign客户端进行服务调用
- 在contract模块中定义服务接口

### 消息处理Consumer类topic定义规范

- 命名规范：{project}-处理业务逻辑名
- topic名使用常量，常量类`Topic`放到common\provide下

### 消息发送规范

- 消息体使用Map或专门的消息对象
- 发送消息后记录日志
- 使用common\provider下工具发送`PublisherUtil.send(Topic.XXX, message)`发送消息
- topic使用common\contact常量类`Topic`定义

```java
import lombok.extern.slf4j.Slf4j;
import wanda.stark.core.codec.JsonUtils;
import wanda.stark.core.msg.Publisher;

@Slf4j
public class PublisherUtil {
    private PublisherUtil() {
    }

    public static void send(String topic, Object body) {
        log.info(">>>发送消息, topic:{}, body:{}", topic, JsonUtils.encode(body));
        try {
            Publisher.get().publish(topic, body);
        } catch (Exception e) {
            log.error(">>>发送消息异常", e);
        }
    }
}
```

### 调用三方系统api重试规范

- 使用common\provider重试工具`RetryUtil.retry`

```java
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.concurrent.Callable;

/**
 * 重试工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class RetryUtil {
    private RetryUtil() {
    }

    public static String retry(Callable<String> function, int retryMaxCount, int sleepTime) {
        int retryCount = 0;
        while (true) {
            try {
                return function.call();
            } catch (IOException e) {
                if (++retryCount >= retryMaxCount) {
                    log.info(">>> 重试次数超限，不在进行重试操作");
                    throw new RuntimeException(e);
                }
                log.error(">>> io异常, 开始重试{}次", retryCount, e);
                try {
                    //出现异常后，休眠一段时间后
                    Thread.sleep(sleepTime);
                } catch (InterruptedException e1) {
                    log.error(">>> 重试错误", e1);
                    Thread.currentThread().interrupt();
                }
            } catch (Exception e) {
                log.error(">>> 重试错误", e);
                throw new RuntimeException(e);
            }
        }
    }

    public static void retry(Runnable runnable, int retryMaxCount, int sleepTime) {
        retry(() -> {
            runnable.run();
            return "ok";
        }, retryMaxCount, sleepTime);
    }

}
```

## 技术栈特定规范

### Spring Cloud

- 使用`@EnableDiscoveryClient`启用服务发现
- 使用`@ComponentScan(basePackages = {"project"})`扫描组件，比如{project}为card-kam，basePackages应为card.kam
- 主类命名：`ServiceApplication`、`ApiApplication`、`ConsumerApplication`

### Maven配置

- 使用`wanda.cloud.parent`作为父POM
- 版本号使用`${revision}`占位符
- 模块化组织：contract、provider等

### 字段命名规范

- 使用驼峰命名：`memberNo`、`cinemaCode`
- 布尔字段以`is`开头：`isConsumeHandle`
- 时间字段以`Time`结尾：`settleTime`、`enableTime`

