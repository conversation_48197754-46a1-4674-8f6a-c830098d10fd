# task-caiguan-recharge-card

## 充值订单充值接口
- api接口已定义，在card-kam-admin-api模块KamController里
- card-kam-admin-service逻辑实现在 RechargeOrderService rpc接口实现，在rpc里需要以批次号加redis分布式锁实现具体逻辑，具体逻辑放到它的Biz类里
  - biz逻辑 校验
    - 校验订单状态是否无效
    - 校验批次号是否重复
    - 校验合同号、销售单号、金额、卡号是和库里保存的单据信息及关联卡号（可以是部分卡号）是否一样
    - 卡号是否重复
  - biz逻辑 保存请求批次充值明细事务
    - 根据传来的卡号先过滤订单里待充值和充值失败的卡，将其批量设置为充值中状态，log记录充值人等，批次卡号明细保存
  - biz逻辑 保存事务成功后发送批次号消息到consumer做异步卡充值
- card-kam-consumer 创建充值Consumer类实现充值
  - 根据批次号查询批次卡号列表
  - 循环每张卡进行充值
    - 每张卡充值操作要以卡号加redis分布式锁
    - 卡充值前需要判断销售单卡信息状态，如果该卡充值成功，则不予充值，直接跳过
    - 调用卡系统卡充值rpc接口，为每张卡依次充值，现在这个rpc接口还没有，可以先模拟一个调用，
    - 卡充值成功后设置卡信息状态充值成功，如果rpc返回状态失败或调用异常则设置为充值失败，记录卡号对应失败信息更新到批次卡明细表里错误信息字段
  - 全部卡完成后，发送批次号消息到consumer通知财管系统充值结果
- card-kam-consumer 创建通知财管系统Consumer类实现通知财管系统充值结果逻辑
  - 根据批次号查询批次卡号列表
  - 查询关联销售单信息
  - 组合json请求体，调用财管系统API（目前还没有定义api，可简单模拟一个方法）实现数据推送（接口文档参考截图）
  - api调用成功，更新OrderLog表推送状态为推送成功，否则为推送失败