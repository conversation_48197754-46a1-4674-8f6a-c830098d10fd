#!/bin/bash

echo "=== 测试幂等验证改进功能 ==="

cd card-kam-admin-service/provider

echo "1. 编译测试..."
mvn test-compile -q

if [ $? -eq 0 ]; then
    echo "✅ 编译成功"
else
    echo "❌ 编译失败"
    exit 1
fi

echo "2. 运行幂等验证测试..."
mvn test -Dtest=RechargeOrderRechargeBizTest#testValidateIdempotent_WithRechargingCards_ShouldThrowException -q

echo "3. 运行成功状态测试..."
mvn test -Dtest=RechargeOrderRechargeBizTest#testValidateIdempotent_WithAllSuccessCards_ShouldSendNotifyMessage -q

echo "4. 运行失败状态测试..."
mvn test -Dtest=RechargeOrderRechargeBizTest#testValidateIdempotent_WithFailedCards_ShouldSendRechargeMessage -q

echo "=== 测试完成 ==="