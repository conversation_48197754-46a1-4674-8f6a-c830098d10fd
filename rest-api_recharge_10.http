### 充值订单充值接口测试 - 小数据集（10张卡）
POST http://localhost:8080/card-kam/ysht/recharge-order/recharge
Content-Type: application/json

{
  "batchNo": "BATCH202501070100",
  "rechargeOrderNo": "RO202501070031",
  "contractNo": "CNT20250107002",
  "everyCardRechargeAmount": 10000,
  "everyCardPresentAmount": 1000,
  "operator": "测试操作员",
  "cardNos": [
    "6225880000000001",
    "6225880000000002",
    "6225880000000003",
    "6225880000000004",
    "6225880000000005",
    "6225880000000006",
    "6225880000000007",
    "6225880000000008",
    "6225880000000009",
    "6225880000000010"
  ]
}

### 说明
# 小数据集充值测试，包含10张卡
# 每张卡充值100元，赠送10元
# 批次号：BATCH202501070001
# 充值订单号：RO202501070031（对应推送接口的订单）
# 适合快速测试充值功能

### 充值订单充值接口测试 - 单张卡测试
POST http://localhost:8080/card-kam/ysht/recharge-order/recharge
Content-Type: application/json

{
  "batchNo": "BATCH202501070002",
  "contractNo": "CNT20250107002",
  "rechargeOrderNo": "RO202501070032",
  "everyCardRechargeAmount": 5000,
  "everyCardPresentAmount": 500,
  "operator": "测试操作员",
  "cardNos": [
    "6225880000000001"
  ]
}

### 说明
# 单张卡充值测试
# 充值50元，赠送5元
# 适合单卡功能验证

### 充值订单充值接口测试 - 大额充值
POST http://localhost:8080/card-kam/ysht/recharge-order/recharge
Content-Type: application/json

{
  "batchNo": "BATCH202501070003",
  "contractNo": "CNT20250107002",
  "rechargeOrderNo": "RO202501070033",
  "everyCardRechargeAmount": 100000,
  "everyCardPresentAmount": 10000,
  "operator": "测试操作员",
  "cardNos": [
    "6225880000000001",
    "6225880000000002",
    "6225880000000003"
  ]
}

### 说明
# 大额充值测试，3张卡
# 每张卡充值1000元，赠送100元
# 适合大额充值场景测试