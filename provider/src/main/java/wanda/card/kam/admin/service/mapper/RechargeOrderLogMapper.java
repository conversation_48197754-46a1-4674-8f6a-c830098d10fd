package wanda.card.kam.admin.service.mapper;

import com.google.common.collect.Lists;
import org.springframework.util.CollectionUtils;
import wanda.card.kam.admin.contract.dto.RechargeOrderAdminDto;
import wanda.card.kam.common.provider.entity.RechargeOrderLog;

import java.util.List;

public class RechargeOrderLogMapper {

    public static List<RechargeOrderAdminDto.OrderLog> of(List<RechargeOrderLog> dbList){
        List<RechargeOrderAdminDto.OrderLog> list = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(dbList)){
            RechargeOrderAdminDto.OrderLog log;
            for (RechargeOrderLog item : dbList){
                log = new RechargeOrderAdminDto.OrderLog();
                log.setCreateTime(item.getCreateTime());
                log.setLogType(item.getOperationType());
                log.setOperationLog(item.getOperationLog());
                list.add(log);
            }
        }
        return list;
    }
}
