package wanda.card.kam.admin.service.biz;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import wanda.card.kam.admin.contract.dto.RechargeOrderAdminDto;
import wanda.card.kam.admin.contract.dto.RechargeOrderAdminDto.*;
import wanda.card.kam.admin.service.mapper.RechargeOrderCardInfoMapper;
import wanda.card.kam.admin.service.mapper.RechargeOrderLogMapper;
import wanda.card.kam.admin.service.mapper.RechargeOrderMapper;
import wanda.card.kam.common.contract.utils.DateUtil;
import wanda.card.kam.common.provider.dao.RechargeOrderCardInfoDao;
import wanda.card.kam.common.provider.dao.RechargeOrderDao;
import wanda.card.kam.common.provider.dao.RechargeOrderLogDao;
import wanda.card.kam.common.provider.entity.RechargeOrder;
import wanda.card.kam.common.provider.entity.RechargeOrderCardInfo;
import wanda.card.kam.common.provider.entity.RechargeOrderLog;
import wanda.stark.core.data.PageResult;

import java.time.LocalDateTime;
import java.util.List;

@Slf4j
@Service
@AllArgsConstructor
public class RechargeOrderAdminBiz {

    private final RechargeOrderDao rechargeOrderDao;
    private final RechargeOrderLogDao rechargeOrderLogDao;
    private final RechargeOrderCardInfoDao cardInfoDao;

    public PageResult<RechargeOrderAdminDto.RechargeOrder> list(ListRequest request) {
        LocalDateTime startTime = DateUtil.str2DateTime(request.getCreateTimeStart());
        LocalDateTime endTime = DateUtil.str2DateTime(request.getCreateTimeEnd());
        PageResult<RechargeOrder> dbResult = rechargeOrderDao.pageQuery(request.getContractNo(), request.getCustomerCode(),
                request.getCustomerName(), request.getSeller(), request.getRechargeOrderNo(), startTime,
                endTime, request.getStatus(), request.getAreaCode(), request.getCinemaInnerCode(), request.getPageInfo());
        PageResult<RechargeOrderAdminDto.RechargeOrder> result = new PageResult<>();
        result.setTotalCount(dbResult.getTotalCount());
        result.setItems(RechargeOrderMapper.buildOrder(dbResult.getItems()));
        return result;
    }

    public RechargeOrderDetail getRechargeOrderDetail(long id) {
        RechargeOrderDetail detail = new RechargeOrderDetail();
        RechargeOrder rechargeOrder = rechargeOrderDao.selectOne(id);
        BeanUtils.copyProperties(rechargeOrder, detail);
        List<RechargeOrderLog> logs = rechargeOrderLogDao.findByRechargeOrderNo(rechargeOrder.getRechargeOrderNo());
        detail.setLogs(RechargeOrderLogMapper.of(logs));
        return detail;
    }

    public PageResult<RechargeCardDetail> getRechargeOrderCardDetailList(CardDetailListRequest request) {
        PageResult<RechargeOrderCardInfo> dbResult = cardInfoDao.pageQuery(request.getRechargeOrderNo(), request.getCardNo(),
                request.getStartTime(), request.getEndTime(), request.getStatus(), request.getPageInfo());
        PageResult<RechargeCardDetail> result = PageResult.emptyPage();
        result.setTotalCount(dbResult.getTotalCount());
        result.setItems(RechargeOrderCardInfoMapper.of(dbResult.getItems()));
        return result;
    }
}
