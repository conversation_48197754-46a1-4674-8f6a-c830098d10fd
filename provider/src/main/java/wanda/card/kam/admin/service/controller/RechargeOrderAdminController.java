package wanda.card.kam.admin.service.controller;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;
import wanda.card.kam.admin.contract.RechargeOrderAdminService;
import wanda.card.kam.admin.contract.dto.RechargeOrderAdminDto.*;
import wanda.card.kam.admin.service.biz.RechargeOrderAdminBiz;
import wanda.stark.core.data.PageResult;
import wanda.stark.core.data.R;

@Slf4j
@RestController
@AllArgsConstructor
public class RechargeOrderAdminController implements RechargeOrderAdminService {

    private final RechargeOrderAdminBiz rechargeOrderAdminBiz;

    @Override
    public PageResult<RechargeOrder> list(ListRequest request) {
        return rechargeOrderAdminBiz.list(request);
    }

    @Override
    public R<RechargeOrderDetail> getRechargeOrderDetail(Long rechargeOrderId) {
        return R.success(rechargeOrderAdminBiz.getRechargeOrderDetail(rechargeOrderId));
    }

    @Override
    public PageResult<RechargeCardDetail> getRechargeOrderCardDetailList(CardDetailListRequest request) {
        return rechargeOrderAdminBiz.getRechargeOrderCardDetailList(request);
    }
}
