package wanda.card.kam.admin.service.biz;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import wanda.card.kam.admin.contract.dto.RechargeOrderPushDto.RechargeOrderCardPushInfo;
import wanda.card.kam.admin.contract.dto.RechargeOrderPushDto.RechargeOrderPushRequest;
import wanda.card.kam.common.contract.constant.BusinessException;
import wanda.card.kam.common.contract.constant.CardRechargeStatus;
import wanda.card.kam.common.contract.constant.OperationLogType;
import wanda.card.kam.common.contract.constant.RechargeOrderStatus;
import wanda.card.kam.common.provider.dao.RechargeOrderDao;
import wanda.card.kam.common.provider.entity.RechargeOrder;
import wanda.card.kam.common.provider.entity.RechargeOrderCardInfo;
import wanda.card.kam.common.provider.entity.RechargeOrderLog;
import wanda.card.kam.common.provider.util.MoneyUtil;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 充值订单推送服务实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@AllArgsConstructor
public class RechargeOrderPushServiceBiz {
    private final RechargeOrderDao rechargeOrderDao;

    public void pushRechargeOrder(RechargeOrderPushRequest request) {
        log.info("充值订单推送服务开始处理，订单号：{}", request.getRechargeOrderNo());
        check(request);
        LocalDateTime now = LocalDateTime.now();
        List<RechargeOrderCardInfo> cardInfos = buildRechargeOrderCardInfos(request, now);
        RechargeOrder rechargeOrder = buildRechargeOrder(request, now);
        RechargeOrderLog operationLog = buildOperationLog(request, now);
        rechargeOrderDao.batchInsert(tx -> {
            // 3. 保存充值订单基本信息
            tx.insert(rechargeOrder).result(false);
            // 4. 批量保存卡信息
            tx.insert(cardInfos).result(false);
            // 5. 记录操作日志
            tx.insert(operationLog).result(false);
        });
        log.info("保存充值订单基本信息成功，订单号：{}", request.getRechargeOrderNo());
        log.info("批量保存卡信息成功，订单号：{}，卡数量：{}", request.getRechargeOrderNo(), request.getCount());
        log.info("记录操作日志成功，订单号：{}", request.getRechargeOrderNo());
    }

    private void check(RechargeOrderPushRequest request) {
        RechargeOrder rechargeOrderNo = rechargeOrderDao.findByRechargeOrderNo(request.getRechargeOrderNo());
        if (rechargeOrderNo != null) {
            throw new BusinessException(String.format("充值订单号已存在：%s 卡总数:%d 总金额:%s元",
                    request.getRechargeOrderNo(), rechargeOrderNo.getCount(), MoneyUtil.fenToYuanStrExcludeDecimalPoint(rechargeOrderNo.getAmount())));
        }
        // 1. 检查request里count和卡列表数量一致
        int size = request.getCardInfos().size();
        if (!request.getCount().equals(size)) {
            throw new BusinessException("订单卡数量与卡信息列表数量不一致");
        }
        // 2. 检查request amount总金额和（每张卡金额+赠送额*总数）一致
        int calculatedAmount = (request.getEveryCardRechargeAmount() + request.getEveryCardPresentAmount()) * request.getCount();
        if (!request.getAmount().equals(calculatedAmount)) {
            throw new BusinessException("总金额与计算金额不一致");
        }
        // 3. 检查卡号是否重复并把重复卡号找出来抛异常
        List<String> duplicateCardNos = request.getCardInfos().stream()
                .collect(Collectors.groupingBy(RechargeOrderCardPushInfo::getCardNo))
                .entrySet().stream()
                .filter(entry -> entry.getValue().size() > 1)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
        if (!duplicateCardNos.isEmpty()) {
            throw new BusinessException("存在重复的卡号：" + duplicateCardNos);
        }
    }

    /**
     * 构建充值订单实体
     */
    RechargeOrder buildRechargeOrder(RechargeOrderPushRequest request, LocalDateTime now) {
        RechargeOrder rechargeOrder = new RechargeOrder();
        rechargeOrder.setContractNo(request.getContractNo());
        rechargeOrder.setRechargeOrderNo(request.getRechargeOrderNo());
        rechargeOrder.setCustomerCode(request.getCustomerCode());
        rechargeOrder.setCustomerName(request.getCustomerName());
        rechargeOrder.setSellerId(request.getSellerId());
        rechargeOrder.setSeller(request.getSeller());
        rechargeOrder.setAreaCode(request.getAreaCode());
        rechargeOrder.setAreaName(request.getAreaName());
        rechargeOrder.setCinemaInnerCode(request.getCinemaInnerCode());
        rechargeOrder.setCinemaName(request.getCinemaName());
        rechargeOrder.setEveryCardRechargeAmount(request.getEveryCardRechargeAmount());
        rechargeOrder.setEveryCardPresentAmount(request.getEveryCardPresentAmount());
        rechargeOrder.setCount(request.getCount());
        rechargeOrder.setAmount(request.getAmount());
        rechargeOrder.setStatus(RechargeOrderStatus.WAIT_RECHARGE);
        rechargeOrder.setCreateTime(now);
        rechargeOrder.setUpdateTime(now);
        return rechargeOrder;
    }

    /**
     * 构建充值订单卡信息列表
     */
    List<RechargeOrderCardInfo> buildRechargeOrderCardInfos(RechargeOrderPushRequest request, LocalDateTime now) {
        List<RechargeOrderCardInfo> cardInfos = new ArrayList<>();
        for (RechargeOrderCardPushInfo cardPushInfo : request.getCardInfos()) {
            RechargeOrderCardInfo cardInfo = new RechargeOrderCardInfo();
            cardInfo.setRechargeOrderNo(request.getRechargeOrderNo());
            cardInfo.setCardNo(cardPushInfo.getCardNo());
            cardInfo.setCardTypeCode(cardPushInfo.getCardTypeCode());
            cardInfo.setCardTypeName(cardPushInfo.getCardTypeName());
            cardInfo.setRechargeAmount(request.getEveryCardRechargeAmount());
            cardInfo.setPresentAmount(request.getEveryCardPresentAmount());
            cardInfo.setChangeBalance(request.getEveryCardRechargeAmount() + request.getEveryCardPresentAmount());
            cardInfo.setBeforeBalance(null); // 推送时暂时设为null，充值时会更新
            cardInfo.setAfterBalance(null);  // 推送时暂时设为null，充值时会更新
            cardInfo.setStatus(CardRechargeStatus.WAIT_RECHARGE);
            cardInfo.setCreateTime(now);
            cardInfo.setUpdateTime(now);
            cardInfos.add(cardInfo);
        }
        return cardInfos;
    }

    /**
     * 构建操作日志
     */
    RechargeOrderLog buildOperationLog(RechargeOrderPushRequest request, LocalDateTime now) {
        RechargeOrderLog log = new RechargeOrderLog();
        log.setRechargeOrderNo(request.getRechargeOrderNo());
        log.setBatchNo(null); // 创建订单时暂无批次号
        log.setPushStatus(null); // 订单时暂无推送状态
        log.setCreateTime(now);
        log.setUpdateTime(now);
        log.setOperationType(OperationLogType.CREATE_ORDER);
        log.setOperator(request.getOperator());
        log.setOperationLog(String.format("创建充值订单，卡数量：%d，总金额：%s元", request.getCount(), MoneyUtil.fenToYuanStrExcludeDecimalPoint(request.getAmount())));
        return log;
    }
}
