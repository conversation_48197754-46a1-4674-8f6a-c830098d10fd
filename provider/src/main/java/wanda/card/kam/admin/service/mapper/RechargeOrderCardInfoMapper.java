package wanda.card.kam.admin.service.mapper;

import com.google.common.collect.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import wanda.card.kam.admin.contract.dto.RechargeOrderAdminDto.RechargeCardDetail;
import wanda.card.kam.common.provider.entity.RechargeOrderCardInfo;

import java.util.List;

public class RechargeOrderCardInfoMapper {

    public static List<RechargeCardDetail> of(List<RechargeOrderCardInfo> dbList){
        List<RechargeCardDetail> list = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(dbList)){
            RechargeCardDetail detail;
            for (RechargeOrderCardInfo item : dbList){
                detail = new RechargeCardDetail();
                BeanUtils.copyProperties(item, detail);
                detail.setStatus(item.getStatus());
                list.add(detail);
            }
        }
        return list;
    }
}
