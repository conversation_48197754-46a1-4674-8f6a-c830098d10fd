package wanda.card.kam.admin.service.biz;

import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import wanda.card.kam.admin.contract.dto.RechargeOrderRechargeDto.RechargeOrderRechargeRequest;
import wanda.card.kam.common.contract.constant.*;
import wanda.card.kam.common.provider.dao.RechargeOrderCardInfoDao;
import wanda.card.kam.common.provider.dao.RechargeOrderDao;
import wanda.card.kam.common.provider.dao.RechargeOrderLogDao;
import wanda.card.kam.common.provider.entity.RechargeOrder;
import wanda.card.kam.common.provider.entity.RechargeOrderCardInfo;
import wanda.card.kam.common.provider.entity.RechargeOrderLog;
import wanda.card.kam.common.provider.util.PublisherUtil;

import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static wanda.stark.db.jsd.lang.FilterType.IN;
import static wanda.stark.db.jsd.lang.Shortcut.f;
import static wanda.stark.db.jsd.lang.Shortcut.uv;

/**
 * 充值订单充值业务逻辑
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@AllArgsConstructor
public class RechargeOrderRechargeBiz {

    private final RechargeOrderDao rechargeOrderDao;
    private final RechargeOrderLogDao rechargeOrderLogDao;
    private final RechargeOrderCardInfoDao rechargeOrderCardInfoDao;

    /**
     * 充值订单充值处理
     *
     * @param request 充值请求
     */
    public void rechargeOrder(RechargeOrderRechargeRequest request) {
        log.info("开始处理充值订单充值，批次号：{}", request.getBatchNo());

        // 1. 校验订单和批次号幂等性
        if (validateIdempotent(request)) {
            log.info("订单和批次号幂等性校验通过，直接返回，批次号：{}", request.getBatchNo());
            return;
        }

        // 2. 校验逻辑
        validateRechargeRequest(request);

        // 3. 保存请求批次充值明细事务
        saveRechargeBatchDetails(request);

        // 4. 发送批次号消息到consumer做异步卡充值
        sendRechargeMessage(request.getBatchNo());

        log.info("充值订单充值处理完成，批次号：{}", request.getBatchNo());
    }

    /**
     * 校验订单和批次号幂等性
     * 如果订单和批次存在，批次卡号有不同，报业务异常
     * 如果全部一样，就不执行后面的所有操作了，直接返回true
     *
     * @param request 充值请求
     * @return true-幂等校验通过，false-需要继续执行后续逻辑
     */
    private boolean validateIdempotent(RechargeOrderRechargeRequest request) {
        log.info("开始校验订单和批次号幂等性，订单号：{}，批次号：{}", request.getRechargeOrderNo(), request.getBatchNo());

        // 查询是否存在相同订单号和批次号的记录
        RechargeOrderLog existingLog = rechargeOrderLogDao.findByBatchNo(request.getBatchNo());

        if (existingLog == null) {
            log.info("批次号不存在，需要继续执行后续逻辑，批次号：{}", request.getBatchNo());
            return false;
        }

        // 检查批次号是否属于当前订单
        if (!existingLog.getRechargeOrderNo().equals(request.getRechargeOrderNo())) {
            throw new BusinessException("该批次号" + request.getBatchNo() + "在其他订单号" + existingLog.getRechargeOrderNo() + "已存在，不能重复使用");
        }

        // 查询批次下的所有卡号
        List<RechargeOrderCardInfo> existingCardInfos = rechargeOrderCardInfoDao.findByBatchNo(request.getBatchNo());

        if (existingCardInfos.isEmpty()) {
            log.info("批次号存在但无卡信息，需要继续执行后续逻辑，批次号：{}", request.getBatchNo());
            return false;
        }

        // 获取现有的卡号集合
        Set<String> existingCardNos = existingCardInfos.stream()
                .map(RechargeOrderCardInfo::getCardNo)
                .collect(Collectors.toSet());

        // 获取请求的卡号集合
        Set<String> requestCardNos = new HashSet<>(request.getCardNos());

        // 检查卡号是否完全一致
        if (!existingCardNos.equals(requestCardNos)) {
            log.error("批次号存在但卡号不一致，订单号：{}，批次号：{}，现有卡号：{}，请求卡号：{}",
                    request.getRechargeOrderNo(), request.getBatchNo(), existingCardNos, requestCardNos);
            throw new BusinessException("订单号" + request.getRechargeOrderNo() + "批次号" + request.getBatchNo() +
                    "已存在，但卡号信息不一致，不能重复提交");
        }

        // 幂等校验：检查卡状态并处理
        return handleIdempotentCardStatus(existingCardInfos, request.getBatchNo());
    }

    /**
     * 处理幂等卡状态检查
     *
     * @param cardInfos 卡信息列表
     * @param batchNo   批次号
     * @return true-幂等校验通过，false-需要继续执行后续逻辑
     */
    private boolean handleIdempotentCardStatus(List<RechargeOrderCardInfo> cardInfos, String batchNo) {
        log.info("开始检查卡状态进行幂等校验，批次号：{}，卡数量：{}", batchNo, cardInfos.size());

        // 统计各状态的卡数量
        long rechargingCount = cardInfos.stream()
                .filter(card -> card.getStatus() == CardRechargeStatus.RECHARGING)
                .count();
        long successCount = cardInfos.stream()
                .filter(card -> card.getStatus() == CardRechargeStatus.SUCCESS)
                .count();
        long failedCount = cardInfos.stream()
                .filter(card -> card.getStatus() == CardRechargeStatus.FAILED)
                .count();

        log.info("卡状态统计，批次号：{}，充值中：{}，充值成功：{}，充值失败：{}",
                batchNo, rechargingCount, successCount, failedCount);

        // 1. 如果请求卡状态只要有充值中，则抛业务异常告知该批次正在充值中
        if (rechargingCount > 0) {
            log.error("该批次正在充值中，不能重复提交，批次号：{}，充值中卡数量：{}", batchNo, rechargingCount);
            throw new BusinessException("该批次正在充值中，请稍后再试，批次号：" + batchNo);
        }

        // 2. 如果请求卡状态都是充值成功，发送RECHARGE_NOTIFY_TOPIC消息，然后返回正常响应，不做后面处理
        if (successCount == cardInfos.size()) {
            log.info("该批次所有卡都已充值成功，发送通知消息，批次号：{}", batchNo);
            PublisherUtil.send(Topic.RECHARGE_NOTIFY_TOPIC, batchNo);
            return true;
        }

        // 3. 如果请求卡状态有个别充值失败的，发送RECHARGE_CARD_TOPIC消息，然后返回正常响应，不做后面处理
        if (failedCount > 0) {
            log.info("该批次有充值失败的卡，发送重新充值消息，批次号：{}，失败卡数量：{}", batchNo, failedCount);
            PublisherUtil.send(Topic.RECHARGE_CARD_TOPIC, batchNo);
            return true;
        }

        log.info("幂等校验完成，批次号：{}，需要继续执行后续逻辑", batchNo);
        return true;
    }

    /**
     * 校验充值请求
     *
     * @param request 充值请求
     */
    private void validateRechargeRequest(RechargeOrderRechargeRequest request) {
        log.info("开始校验充值请求，批次号：{}", request.getBatchNo());

        // 校验卡号是否重复（单批次内）
        validateCardNosUnique(request.getCardNos());
        // 校验跨批次卡号重复
        validateCardNosAcrossBatches(request);
        // 校验订单状态是否有效
        RechargeOrder rechargeOrder = rechargeOrderDao.findByRechargeOrderNo(request.getRechargeOrderNo());
        if (rechargeOrder == null) {
            throw new BusinessException("充值订单不存在：" + request.getRechargeOrderNo());
        }
        validateOrderStatus(rechargeOrder);
        // 校验合同号、销售单号、金额、卡号是和库里保存的单据信息是否一样
        validateOrderConsistency(request, rechargeOrder);

        log.info("充值请求校验通过，批次号：{}", request.getBatchNo());
    }

    /**
     * 校验订单状态是否有效
     */
    private void validateOrderStatus(RechargeOrder rechargeOrder) {
        String rechargeOrderNo = rechargeOrder.getRechargeOrderNo();
        if (rechargeOrder.getStatus() == RechargeOrderStatus.INVALID) {
            throw new BusinessException("充值订单状态无效，不能进行充值操作：" + rechargeOrderNo);
        }
        if (rechargeOrder.getStatus() == RechargeOrderStatus.SUCCESS) {
            throw new BusinessException("充值订单状态全部完成，不能进行充值操作：" + rechargeOrderNo);
        }
        log.info("订单状态校验通过，订单号：{}，状态：{}", rechargeOrderNo, rechargeOrder.getStatus());
    }


    /**
     * 校验订单一致性
     *
     * @param request 充值请求
     */
    private void validateOrderConsistency(RechargeOrderRechargeRequest request, RechargeOrder rechargeOrder) {

        // 校验合同号
        if (!rechargeOrder.getContractNo().equals(request.getContractNo())) {
            throw new BusinessException("合同号不匹配，订单合同号：" + rechargeOrder.getContractNo() +
                    "，请求合同号：" + request.getContractNo());
        }

        // 校验每张卡充值金额
        if (!rechargeOrder.getEveryCardRechargeAmount().equals(request.getEveryCardRechargeAmount())) {
            throw new BusinessException("每张卡充值金额不匹配，订单金额：" + rechargeOrder.getEveryCardRechargeAmount() +
                    "，请求金额：" + request.getEveryCardRechargeAmount());
        }

        // 校验每张卡赠送金额
        if (!rechargeOrder.getEveryCardPresentAmount().equals(request.getEveryCardPresentAmount())) {
            throw new BusinessException("每张卡赠送金额不匹配，订单金额：" + rechargeOrder.getEveryCardPresentAmount() +
                    "，请求金额：" + request.getEveryCardPresentAmount());
        }

        // 校验卡号是否属于该订单（可以是部分卡号）
        List<RechargeOrderCardInfo> orderCardInfos = rechargeOrderCardInfoDao.findByRechargeOrderNoAndCardNo(
                request.getRechargeOrderNo(), request.getCardNos());

        if (orderCardInfos.size() != request.getCardNos().size()) {
            // 找出不属于该订单的卡号
            Set<String> validCardNos = orderCardInfos.stream()
                    .map(RechargeOrderCardInfo::getCardNo)
                    .collect(Collectors.toSet());
            List<String> invalidCardNos = request.getCardNos().stream()
                    .filter(cardNo -> !validCardNos.contains(cardNo))
                    .collect(Collectors.toList());

            log.error("部分卡号不属于该充值订单，订单号：{}，不属于的卡号：{}",
                    request.getRechargeOrderNo(), invalidCardNos);
            throw new BusinessException("部分卡号不属于该充值订单：" + request.getRechargeOrderNo() +
                    "，不属于的卡号：" + invalidCardNos);
        }

        log.info("订单一致性校验通过，合同号：{}，充值订单号：{}", request.getContractNo(), request.getRechargeOrderNo());
    }

    /**
     * 校验卡号唯一性（单批次内）
     *
     * @param cardNos 卡号列表
     */
    private void validateCardNosUnique(List<String> cardNos) {
        Set<String> uniqueCardNos = new HashSet<>(cardNos);
        if (uniqueCardNos.size() != cardNos.size()) {
            throw new BusinessException("卡号列表中存在重复卡号");
        }
        log.info("卡号唯一性校验通过，卡号数量：{}", cardNos.size());
    }

    /**
     * 校验跨批次卡号重复
     *
     * @param request 充值请求
     */
    private void validateCardNosAcrossBatches(RechargeOrderRechargeRequest request) {
        log.info("开始校验跨批次卡号重复，订单号：{}，批次号：{}", request.getRechargeOrderNo(), request.getBatchNo());

        // 查询该订单下除当前批次外已存在的卡号
        List<String> existingCardIds = rechargeOrderCardInfoDao.findExistingCardNosExcludeBatch(
                request.getRechargeOrderNo(), request.getBatchNo(), request.getCardNos());

        if (!existingCardIds.isEmpty()) {
            // 获取重复的卡号
            Set<String> duplicateCardNos = Sets.newHashSet(existingCardIds);

            log.error("发现跨批次重复卡号，订单号：{}，当前批次号：{}，重复卡号：{}",
                    request.getRechargeOrderNo(), request.getBatchNo(), duplicateCardNos);

            throw new BusinessException("订单号" + request.getRechargeOrderNo() +
                    "中存在跨批次重复的卡号：" + duplicateCardNos + "，每批次的卡号不能相同");
        }

        log.info("跨批次卡号重复校验通过，订单号：{}，批次号：{}", request.getRechargeOrderNo(), request.getBatchNo());
    }

    /**
     * 保存请求批次充值明细事务
     *
     * @param request 充值请求
     */
    private void saveRechargeBatchDetails(RechargeOrderRechargeRequest request) {
        log.info("开始保存批次充值明细，批次号：{}", request.getBatchNo());

        rechargeOrderCardInfoDao.transactionHandle(tx -> {
            // 1. 批量更新卡状态为充值中（只更新待充值和充值失败的卡）
            updateCardStatusToRecharging(tx, request);

            // 2. 记录充值操作日志
            recordRechargeOperator(tx, request);
        });

        log.info("批次充值明细保存完成，批次号：{}", request.getBatchNo());
    }

    /**
     * 批量更新卡状态为充值中
     *
     * @param tx      事务
     * @param request 充值请求
     */
    private void updateCardStatusToRecharging(wanda.stark.db.jsd.lang.Transaction tx, RechargeOrderRechargeRequest request) {
        // 查询待充值和充值失败的卡
        List<RechargeOrderCardInfo> cardInfos = rechargeOrderCardInfoDao.findByRechargeOrderNoAndCardNo(
                request.getRechargeOrderNo(), request.getCardNos());

        List<RechargeOrderCardInfo> cardsToUpdate = cardInfos.stream()
                .filter(card -> card.getStatus() == CardRechargeStatus.WAIT_RECHARGE ||
                        card.getStatus() == CardRechargeStatus.FAILED)
                .collect(Collectors.toList());

        if (cardsToUpdate.isEmpty()) {
            throw new BusinessException("没有可充值的卡，所有卡都不是待充值或充值失败状态");
        }
        LocalDateTime now = LocalDateTime.now();
        // 批量更新状态为充值中
        tx.update(RechargeOrderCardInfo.class)
                .set(uv("status", CardRechargeStatus.RECHARGING.value())
                        .add("batch_no", request.getBatchNo())
                        .add("update_time", now))
                .where(f("card_no", IN, cardsToUpdate.stream().map(RechargeOrderCardInfo::getCardNo).collect(Collectors.toList())))
                .result();
        log.info("批量更新卡状态为充值中，批次号：{}，更新卡数量：{}", request.getBatchNo(), cardsToUpdate.size());
    }

    /**
     * 记录充值操作日志
     *
     * @param tx      事务
     * @param request 充值请求
     */
    private void recordRechargeOperator(wanda.stark.db.jsd.lang.Transaction tx, RechargeOrderRechargeRequest request) {
        RechargeOrderLog operationLog = new RechargeOrderLog();
        operationLog.setRechargeOrderNo(request.getRechargeOrderNo());
        operationLog.setBatchNo(request.getBatchNo());
        operationLog.setOperator(request.getOperator());
        operationLog.setOperationLog("批次充值，批次号：" + request.getBatchNo() + "，卡数量：" + request.getCardNos().size());
        LocalDateTime now = LocalDateTime.now();
        operationLog.setCreateTime(now);
        operationLog.setUpdateTime(now);
        operationLog.setOperationType(OperationLogType.RECHARGE);
        operationLog.setPushStatus(PushStatus.WAIT_PUSH);
        tx.insert(operationLog).result(false);
        log.info("记录充值操作日志，批次号：{}，操作人：{}", request.getBatchNo(), request.getOperator());
    }

    /**
     * 发送批次号消息到消息队列
     *
     * @param batchNo 批次号
     */
    private void sendRechargeMessage(String batchNo) {
        PublisherUtil.send(Topic.RECHARGE_CARD_TOPIC, batchNo);
        log.info("发送批次号消息到充值消息队列，批次号：{}", batchNo);
    }
}