package wanda.card.kam.admin.service.mapper;

import com.google.common.collect.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import wanda.card.kam.admin.contract.dto.RechargeOrderAdminDto;
import wanda.card.kam.common.provider.entity.RechargeOrder;

import java.util.List;

public class RechargeOrderMapper {

    public static List<RechargeOrderAdminDto.RechargeOrder> buildOrder(List<RechargeOrder> items) {
        List<RechargeOrderAdminDto.RechargeOrder> list = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(items)){
            RechargeOrderAdminDto.RechargeOrder order;
            for (RechargeOrder dbOrder : items){
                order = new RechargeOrderAdminDto.RechargeOrder();
                BeanUtils.copyProperties(dbOrder, order);
                order.setStatus(dbOrder.getStatus());
                list.add(order);
            }
        }
        return list;
    }
}
