package wanda.card.kam.admin.service.controller;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;
import wanda.card.kam.admin.contract.RechargeOrderService;
import wanda.card.kam.admin.contract.dto.RechargeOrderDto;
import wanda.card.kam.admin.contract.dto.RechargeOrderPushDto.RechargeOrderPushRequest;
import wanda.card.kam.admin.contract.dto.RechargeOrderRechargeDto.RechargeOrderRechargeRequest;
import wanda.card.kam.admin.service.biz.RechargeOrderPushServiceBiz;
import wanda.card.kam.admin.service.biz.RechargeOrderUpdateServiceBiz;
import wanda.card.kam.admin.service.biz.RechargeOrderRechargeBiz;
import wanda.card.kam.common.provider.util.RedisLockSupport;
import wanda.stark.core.data.R;

/**
 * 充值订单RPC服务控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@AllArgsConstructor
public class RechargeOrderController implements RechargeOrderService {

    private final RedisLockSupport redisLockSupport;
    private final RechargeOrderPushServiceBiz rechargeOrderPushServiceBiz;
    private final RechargeOrderUpdateServiceBiz rechargeOrderUpdateServiceBiz;
    private final RechargeOrderRechargeBiz rechargeOrderRechargeBiz;

    @Override
    public R<Void> pushRechargeOrder(RechargeOrderPushRequest request) {
        log.info("RPC充值订单推送入参:{}", request);
        try {
            redisLockSupport.lockTemplate(request.getRechargeOrderNo(), () -> rechargeOrderPushServiceBiz.pushRechargeOrder(request));
            return R.success();
        } catch (Exception e) {
            log.error("RPC充值订单推送异常", e);
            return R.fail(e.getMessage());
        }
    }

    @Override
    public R<Void> updateRechargeOrder(RechargeOrderDto.UpdateRequest request) {
        log.info("充值订单变更开始处理:{}", request);
        try {
            redisLockSupport.lockTemplate(request.getRechargeOrderNo(), () ->
                    rechargeOrderUpdateServiceBiz.updateRechargeOrder(request));
            return R.success();
        } catch (Exception e) {
            log.error("充值订单变更异常", e);
            return R.fail(e.getMessage());
        }
    }

    @Override
    public R<Void> rechargeCard(RechargeOrderRechargeRequest request) {
        log.info("RPC充值订单充值入参:{}", request);
        try {
            // 使用批次号加锁
            redisLockSupport.lockTemplate(request.getBatchNo(), () -> rechargeOrderRechargeBiz.rechargeOrder(request));
            return R.success();
        } catch (Exception e) {
            log.error("RPC充值订单充值异常", e);
            return R.fail(e.getMessage());
        }
    }
}
