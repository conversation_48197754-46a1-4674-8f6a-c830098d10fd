package wanda.card.kam.admin.service.biz;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import wanda.card.kam.admin.contract.dto.RechargeOrderDto.UpdateRequest;
import wanda.card.kam.common.contract.constant.BusinessException;
import wanda.card.kam.common.contract.constant.OperationLogType;
import wanda.card.kam.common.contract.constant.RechargeOrderStatus;
import wanda.card.kam.common.provider.dao.RechargeOrderCardInfoDao;
import wanda.card.kam.common.provider.dao.RechargeOrderDao;
import wanda.card.kam.common.provider.entity.RechargeOrder;
import wanda.card.kam.common.provider.entity.RechargeOrderCardInfo;
import wanda.card.kam.common.provider.entity.RechargeOrderLog;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 充值订单变更
 */
@Slf4j
@Service
@AllArgsConstructor
public class RechargeOrderUpdateServiceBiz {
    private final RechargeOrderDao rechargeOrderDao;
    private final RechargeOrderCardInfoDao rechargeOrderCardInfoDao;

    public void updateRechargeOrder(UpdateRequest request) {
        log.info("充值订单变更开始处理，订单号：{}", request.getRechargeOrderNo());
        check(request);
        LocalDateTime now = LocalDateTime.now();
        rechargeOrderDao.getDB().begin(tx -> {
            // 1. 修改充值订单基本信息
            rechargeOrderDao.updateStatus(tx, request.getRechargeOrderNo());
            // 2. 批量修改卡信息
            int count = rechargeOrderCardInfoDao.updateStatus(tx, request.getRechargeOrderNo());
            // 3. 记录操作日志
            tx.insert(buildOperationLog(request, count, now)).result(false);
        });
    }

    private void check(UpdateRequest request) {
        RechargeOrder rechargeOrderNo = rechargeOrderDao.findByRechargeOrderNo(request.getRechargeOrderNo());
        if (rechargeOrderNo == null) {
            throw new BusinessException("充值订单号不存在");
        }
        if (rechargeOrderNo.getStatus() != RechargeOrderStatus.WAIT_RECHARGE) {
            throw new BusinessException("充值订单状态为" + rechargeOrderNo.getStatus().displayName() + ",不允许作废");
        }
        List<RechargeOrderCardInfo> list = rechargeOrderCardInfoDao.queryByUpdateStatus(request.getRechargeOrderNo());
        if (!CollectionUtils.isEmpty(list)) {
            throw new BusinessException("充值订单存在已充值的卡，不允许作废");
        }
    }

    /**
     * 构建操作日志
     */
    private RechargeOrderLog buildOperationLog(UpdateRequest request, int count, LocalDateTime now) {
        RechargeOrderLog log = new RechargeOrderLog();
        log.setRechargeOrderNo(request.getRechargeOrderNo());
        log.setCreateTime(now);
        log.setUpdateTime(now);
        log.setOperationType(OperationLogType.DISABLE_ORDER);
        log.setOperator(request.getOperator());
        log.setOperationLog("作废充值订单,卡数量：" + count);
        return log;
    }
}
