package wanda.card.kam.common.provider.entity;

import lombok.Getter;
import lombok.Setter;
import wanda.card.kam.common.contract.constant.CardRechargeStatus;
import wanda.stark.core.db.DbEntity;

import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDateTime;

@Getter
@Setter
@Table
public class RechargeOrderCardInfo implements DbEntity<Long> {

    @Id
    private Long id;

    /**
     * 充值订单号
     */
    private String rechargeOrderNo;
    /**
     * 卡号（脱敏展示）
     */
    private String cardNo;

    /**
     * 卡类型编码
     */
    private String cardTypeCode;

    /**
     * 卡类型名称
     */
    private String cardTypeName;

    /**
     * 充值金额（分）
     */
    private Integer rechargeAmount;

    /**
     * 赠送金额（分）
     */
    private Integer presentAmount;

    /**
     * 余额变动（充值金额+赠送金额）（分）
     */
    private Integer changeBalance;

    /**
     * 充值前余额（分）
     */
    private Integer beforeBalance;

    /**
     * 充值后余额（分）
     */
    private Integer afterBalance;

    /**
     * 充值状态 1:待充值 2:充值中 3:充值成功 4:充值失败
     */
    private CardRechargeStatus status;

    /**
     * 充值流水号（批次号）
     */
    private String batchNo;

    /**
     * 充值失败信息
     */
    private String errorMsg;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 充值时间
     */
    private LocalDateTime rechargeTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

}
