package wanda.card.kam.common.provider.dao;

import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Repository;
import wanda.card.kam.common.contract.constant.RechargeOrderStatus;
import wanda.card.kam.common.provider.entity.RechargeOrder;
import wanda.stark.core.data.PageInfo;
import wanda.stark.core.data.PageResult;
import wanda.stark.db.jsd.JsdReadWriteDao;
import wanda.stark.db.jsd.lang.BasicFilter;
import wanda.stark.db.jsd.lang.Database;
import wanda.stark.db.jsd.lang.FilterType;
import wanda.stark.db.jsd.lang.Transaction;

import java.time.LocalDateTime;
import java.util.function.Consumer;

import static wanda.card.kam.common.provider.dao.BaseDao.DB_NAME;
import static wanda.stark.db.jsd.lang.FilterType.GTE;
import static wanda.stark.db.jsd.lang.FilterType.LT;
import static wanda.stark.db.jsd.lang.Shortcut.f;
import static wanda.stark.db.jsd.lang.Shortcut.uv;

/**
 * 充值订单DAO
 *
 * <AUTHOR>
 */
@Repository
public class RechargeOrderDao extends JsdReadWriteDao<RechargeOrder, Long> {

    public RechargeOrderDao() {
        super(DB_NAME);
    }

    public Database getDB() {
        return super.openWrite();
    }

    /**
     * 事务处理
     *
     * @param consumer
     */
    public void batchInsert(Consumer<Transaction> consumer) {
        openWrite().begin(consumer);
    }

    public PageResult<RechargeOrder> pageQuery(String contractNo, String customerCode, String customerName,
                                               String seller, String rechargeOrderNo, LocalDateTime startTime,
                                               LocalDateTime endTime, int status, String areaCode,
                                               String cinemaInnerCode, PageInfo pageInfo) {
        BasicFilter f = f();
        if (!Strings.isEmpty(contractNo)){
            f.add("contract_no", contractNo);
        }
        if (!Strings.isEmpty(customerCode)){
            f.add("customer_code", customerCode);
        }
        if (!Strings.isEmpty(rechargeOrderNo)){
            f.add("recharge_order_no", rechargeOrderNo);
        }
        if(!Strings.isEmpty(customerName)){
            f.add("customer_name", FilterType.LK, customerName);
        }
        if(!Strings.isEmpty(seller)){
            f.add("seller", FilterType.LK, seller);
        }
        if (startTime != null){
            f.add("create_time", GTE, startTime);
        }
        if (endTime != null){
            f.add("create_time", LT, endTime);
        }
        if (status > 0){
            f.add("status", status);
        }
        if (!Strings.isEmpty(areaCode)){
            f.add("area_code", areaCode);
        }
        if (!Strings.isEmpty(cinemaInnerCode)){
            f.add("cinema_inner_code", cinemaInnerCode);
        }
        return selectPage(f, pageInfo);
    }

    /**
     * /**
     * 根据充值订单号查询订单
     *
     * @param rechargeOrderNo 充值订单号
     * @return 充值订单
     */
    public RechargeOrder findByRechargeOrderNo(String rechargeOrderNo) {
        return openWrite().select(RechargeOrder.class)
                .where(f("recharge_order_no", rechargeOrderNo))
                .result()
                .one(RechargeOrder.class);
    }

    /**
     * 更新销售单状态
     */
    public boolean updateStatus(Transaction tx, String rechargeOrderNo) {
        return tx.update(RechargeOrder.class)
                .set(uv("status", RechargeOrderStatus.INVALID))
                .where(f("recharge_order_no", rechargeOrderNo))
                .result().getAffectedRows() > 0;
    }

    /**
     * 已经获得对象，自定义更新列
     */
    public void updateByColumns(RechargeOrder rechargeOrder, String... columns) {
        openWrite().update(rechargeOrder, columns).result();
    }

    /**
     * 直接根据选中字段，更新自定列
     */
    public void updateStatus(String rechargeOrderNo, RechargeOrderStatus status) {
        openWrite().update(RechargeOrder.class)
                .set(uv("status", status))
                .where(f("recharge_order_no", rechargeOrderNo))
                .result();
    }
}
