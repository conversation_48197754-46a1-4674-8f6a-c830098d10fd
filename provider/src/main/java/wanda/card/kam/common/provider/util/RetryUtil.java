package wanda.card.kam.common.provider.util;

import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.concurrent.Callable;

/**
 * 重试工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class RetryUtil {
    private RetryUtil() {
    }

    public static String retry(Callable<String> function, int retryMaxCount, int sleepTime) {
        int retryCount = 0;
        while (true) {
            try {
                return function.call();
            } catch (IOException e) {
                if (++retryCount >= retryMaxCount) {
                    log.info(">>> 重试次数超限，不在进行重试操作");
                    throw new RuntimeException(e);
                }
                log.error(">>> io异常, 开始重试{}次", retryCount, e);
                try {
                    //出现异常后，休眠一段时间后
                    Thread.sleep(sleepTime);
                } catch (InterruptedException e1) {
                    log.error(">>> 重试错误", e1);
                    Thread.currentThread().interrupt();
                }
            } catch (Exception e) {
                log.error(">>> 重试错误", e);
                throw new RuntimeException(e);
            }
        }
    }

    public static void retry(Runnable runnable, int retryMaxCount, int sleepTime) {
        retry(() -> {
            runnable.run();
            return "ok";
        }, retryMaxCount, sleepTime);
    }

}