package wanda.card.kam.common.provider.util;

import lombok.extern.slf4j.Slf4j;
import wanda.stark.core.codec.JsonUtils;
import wanda.stark.core.msg.Publisher;

/**
 * 消息发布工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class PublisherUtil {
    private PublisherUtil() {
    }

    /**
     * 发送消息
     *
     * @param topic 主题
     * @param body  消息体
     */
    public static void send(String topic, Object body) {
        log.info(">>>发送消息, topic:{}, body:{}", topic, JsonUtils.encode(body));
        try {
            Publisher.get().publish(topic, body);
        } catch (Exception e) {
            log.error(">>>发送消息异常", e);
        }
    }
}