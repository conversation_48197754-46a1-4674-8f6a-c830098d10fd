package wanda.card.kam.common.provider.dao;

import org.springframework.stereotype.Repository;
import wanda.card.kam.common.contract.constant.PushStatus;
import wanda.card.kam.common.provider.entity.RechargeOrderLog;
import wanda.stark.db.jsd.JsdReadWriteDao;
import wanda.stark.db.jsd.lang.Sorters;

import java.time.LocalDateTime;
import java.util.List;

import static wanda.card.kam.common.provider.dao.BaseDao.DB_NAME;
import static wanda.stark.db.jsd.lang.Shortcut.f;
import static wanda.stark.db.jsd.lang.Shortcut.uv;
import static wanda.stark.db.jsd.lang.SortType.DESC;

/**
 * 充值订单日志DAO
 *
 * <AUTHOR>
 */
@Repository
public class RechargeOrderLogDao extends JsdReadWriteDao<RechargeOrderLog, Long> {

    public RechargeOrderLogDao() {
        super(DB_NAME);
    }

    /**
     * 查询推送异常的充值订单日志
     *
     * @return 日志列表
     */
    public List<RechargeOrderLog> findByPushStatus(PushStatus pushStatus) {
        return openWrite().select(RechargeOrderLog.class)
                .where(f("push_status", pushStatus))
                .result()
                .all(RechargeOrderLog.class);
    }

    /**
     * 根据充值订单号查询日志列表
     *
     * @param rechargeOrderNo 充值订单号
     * @return 日志列表
     */
    public List<RechargeOrderLog> findByRechargeOrderNo(String rechargeOrderNo) {
        return openWrite().select(RechargeOrderLog.class)
                .where(f("recharge_order_no", rechargeOrderNo))
                .orderBy(Sorters.create().add(DESC, "create_time"))
                .result()
                .all(RechargeOrderLog.class);
    }

    /**
     * 根据批次号查询充值订单日志
     *
     * @param batchNo 批次号
     * @return 充值订单日志
     */
    public RechargeOrderLog findByBatchNo(String batchNo) {
        return openWrite().select(RechargeOrderLog.class)
                .where(f("batch_no", batchNo))
                .result()
                .one(RechargeOrderLog.class);
    }

    public void updatePushStatus(String batchNo, PushStatus pushStatus) {
        openWrite().update(RechargeOrderLog.class)
                .set(uv("push_status", pushStatus).add("update_time", LocalDateTime.now())
                )
                .where(f("batch_no", batchNo))
                .result();
    }
}
