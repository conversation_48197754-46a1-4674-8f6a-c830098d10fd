package wanda.card.kam.common.provider.dao;

import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Repository;
import wanda.card.kam.common.contract.constant.CardRechargeStatus;
import wanda.card.kam.common.provider.entity.RechargeOrderCardInfo;
import wanda.stark.core.data.PageInfo;
import wanda.stark.core.data.PageResult;
import wanda.stark.db.jsd.JsdReadWriteDao;
import wanda.stark.db.jsd.lang.BasicFilter;
import wanda.stark.db.jsd.lang.FilterType;
import wanda.stark.db.jsd.lang.Transaction;
import wanda.stark.db.jsd.lang.UpdateValues;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static wanda.card.kam.common.provider.dao.BaseDao.DB_NAME;
import static wanda.stark.db.jsd.lang.FilterType.IN;
import static wanda.stark.db.jsd.lang.FilterType.NE;
import static wanda.stark.db.jsd.lang.Shortcut.f;
import static wanda.stark.db.jsd.lang.Shortcut.uv;

/**
 * 充值订单卡信息DAO
 *
 * <AUTHOR>
 */
@Repository
public class RechargeOrderCardInfoDao extends JsdReadWriteDao<RechargeOrderCardInfo, Long> {

    public RechargeOrderCardInfoDao() {
        super(DB_NAME);
    }

    private static final String RECHARGE_ORDER_NO = "recharge_order_no";
    private static final String CARD_NO = "card_no";
    private static final String CREATE_TIME = "create_time";

    /**
     * 事务处理
     *
     * @param consumer
     */
    public void transactionHandle(Consumer<Transaction> consumer) {
        openWrite().begin(consumer);
    }

    /**
     * 分页查询卡信息列表
     */
    public PageResult<RechargeOrderCardInfo> pageQuery(String rechargeOrderNo, String cardNo, LocalDateTime startTime,
                                                       LocalDateTime endTime, CardRechargeStatus status, PageInfo pageInfo) {
        BasicFilter f = f();
        if (Strings.isNotEmpty(cardNo)) {
            f.add(CARD_NO, cardNo);
        }
        if (startTime != null) {
            f.add(CREATE_TIME, FilterType.GTE, startTime);
        }
        if (endTime != null) {
            f.add(CREATE_TIME, FilterType.LT, endTime);
        }
        if (status != null) {
            f.add("status", status);
        }
        return openWrite().select(RechargeOrderCardInfo.class)
                .where(f(RECHARGE_ORDER_NO, rechargeOrderNo))
                .result().page(RechargeOrderCardInfo.class);
    }

    /**
     * 根据充值订单号删除卡信息
     *
     * @param rechargeOrderNo 充值订单号
     * @return 删除的记录数
     */
    public void deleteByRechargeOrderNo(String rechargeOrderNo) {
        openWrite().delete(RechargeOrderCardInfo.class)
                .where(f(RECHARGE_ORDER_NO, rechargeOrderNo))
                .result();
    }

    /**
     * 更新充值订单时查询卡信息
     */
    public List<RechargeOrderCardInfo> queryByUpdateStatus(String rechargeOrderNo) {
        return openWrite().select(RechargeOrderCardInfo.class)
                .where(f(RECHARGE_ORDER_NO, rechargeOrderNo)
                        .add("status", NE, CardRechargeStatus.WAIT_RECHARGE))
                .result()
                .all(RechargeOrderCardInfo.class);
    }

    public int updateStatus(Transaction tx, String rechargeOrderNo) {
        return tx.update(RechargeOrderCardInfo.class)
                .set(uv("status", CardRechargeStatus.INVALID))
                .where(f(RECHARGE_ORDER_NO, rechargeOrderNo))
                .result().getAffectedRows();
    }

    public void update(long id, UpdateValues uv) {
        openWrite().update(RechargeOrderCardInfo.class).set(uv).where(f("id", id)).result();
    }

    /**
     * 查询指定订单下排除当前批次的所有卡号
     *
     * @param rechargeOrderNo 充值订单号
     * @param excludeBatchNo  排除的批次号
     * @return 卡号列表
     * <AUTHOR>
     */
    public List<String> findCardNosByOrderNoExcludeBatch(String rechargeOrderNo, String excludeBatchNo) {
        return openWrite().select(RechargeOrderCardInfo.class)
                .where(f("recharge_order_no", rechargeOrderNo).add("batch_no", NE, excludeBatchNo))
                .result()
                .all(RechargeOrderCardInfo.class)
                .stream()
                .map(RechargeOrderCardInfo::getCardNo)
                .collect(Collectors.toList());
    }

    /**
     * 查询指定订单下排除当前批次的已存在卡号（与新卡号列表的交集）
     *
     * @param rechargeOrderNo 充值订单号
     * @param excludeBatchNo  排除的批次号
     * @param newCardNos      新卡号列表
     * @return 已存在的卡号列表
     * <AUTHOR>
     */
    public List<String> findExistingCardNosExcludeBatch(String rechargeOrderNo, String excludeBatchNo, List<String> newCardNos) {
        if (newCardNos == null || newCardNos.isEmpty()) {
            return new ArrayList<>();
        }
        List<String> existingCardNos = findCardNosByOrderNoExcludeBatch(rechargeOrderNo, excludeBatchNo);
        return newCardNos.stream()
                .filter(existingCardNos::contains)
                .collect(Collectors.toList());
    }

    public RechargeOrderCardInfo findByRechargeOrderNoAndCardNo(String rechargeOrderNo, String cardNo) {
        return openWrite().select(RechargeOrderCardInfo.class)
                .where(f(RECHARGE_ORDER_NO, rechargeOrderNo).add(CARD_NO, cardNo))
                .result()
                .one(RechargeOrderCardInfo.class);
    }

    public List<RechargeOrderCardInfo> findByBatchNo(String batchNo) {
        return openWrite().select(RechargeOrderCardInfo.class)
                .where(f("batch_no", batchNo))
                .result()
                .all(RechargeOrderCardInfo.class);
    }

    /**
     * 根据充值订单号和卡号查询充值订单卡信息
     *
     * @param rechargeOrderNo 充值订单号
     * @return 充值订单卡信息
     */
    public List<RechargeOrderCardInfo> findByRechargeOrderNoAndCardNo(String rechargeOrderNo, List<String> cardNos) {
        return openWrite().select(RechargeOrderCardInfo.class)
                .where(f(RECHARGE_ORDER_NO, rechargeOrderNo).add(CARD_NO, IN, cardNos))
                .result()
                .all(RechargeOrderCardInfo.class);
    }

    /**
     * 查询长时间处于充值中状态的卡信息
     *
     * @param timeThreshold 时间阈值，超过此时间仍处于充值中状态的卡信息
     * @return 长时间处于充值中状态的卡信息列表
     */
    public List<RechargeOrderCardInfo> findLongTimeRechargingCards(LocalDateTime timeThreshold) {
        return openWrite().select(RechargeOrderCardInfo.class)
                .where(f("status", CardRechargeStatus.RECHARGING)
                        .add("recharge_time", FilterType.LTE, timeThreshold))
                .result()
                .all(RechargeOrderCardInfo.class);
    }

}
