package wanda.card.kam.common.provider.entity;

import lombok.Getter;
import lombok.Setter;
import wanda.card.kam.common.contract.constant.RechargeOrderStatus;
import wanda.stark.core.db.DbEntity;

import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
@Table
public class RechargeOrder implements DbEntity<Long> {
    /**
     * id
     */
    @Id
    private Long id;

    /**
     * 充值订单号
     */
    private String rechargeOrderNo;

    /**
     * 合同编码
     */
    private String contractNo;

    /**
     * 客户编码
     */
    private String customerCode;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 销售员万信号
     */
    private String sellerId;

    /**
     * 销售员名
     */
    private String seller;

    /**
     * 区域编码
     */
    private String areaCode;

    /**
     * 区域名称
     */
    private String areaName;

    /**
     * 影城内码
     */
    private String cinemaInnerCode;

    /**
     * 影城名称
     */
    private String cinemaName;

    /**
     * 每张卡充值金额（分）
     */
    private Integer everyCardRechargeAmount;

    /**
     * 每张卡赠送金额（分）
     */
    private Integer everyCardPresentAmount;
    /**
     * 卡数量
     */
    private Integer count;

    /**
     * 总充值金额（含赠送金额）分
     */
    private Integer amount;

    /**
     * 状态
     */
    private RechargeOrderStatus status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    @Transient
    List<RechargeOrderCardInfo> cardInfos;

    @Transient
    String batchNo;
}