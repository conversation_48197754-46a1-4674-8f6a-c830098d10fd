package wanda.card.kam.admin.service.biz;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import wanda.card.kam.admin.contract.dto.RechargeOrderPushDto.RechargeOrderCardPushInfo;
import wanda.card.kam.admin.contract.dto.RechargeOrderPushDto.RechargeOrderPushRequest;
import wanda.card.kam.common.contract.constant.BusinessException;
import wanda.card.kam.common.contract.constant.CardRechargeStatus;
import wanda.card.kam.common.contract.constant.OperationLogType;
import wanda.card.kam.common.contract.constant.RechargeOrderStatus;
import wanda.card.kam.common.provider.dao.RechargeOrderDao;
import wanda.card.kam.common.provider.entity.RechargeOrder;
import wanda.card.kam.common.provider.entity.RechargeOrderCardInfo;
import wanda.card.kam.common.provider.entity.RechargeOrderLog;
import wanda.card.kam.common.provider.util.MoneyUtil;
import wanda.stark.db.jsd.lang.Transaction;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.function.Consumer;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 充值订单推送业务逻辑测试
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class RechargeOrderPushServiceBizTest {

    @Mock
    private RechargeOrderDao rechargeOrderDao;

    @InjectMocks
    private RechargeOrderPushServiceBiz rechargeOrderPushServiceBiz;

    private RechargeOrderPushRequest pushRequest;

    @BeforeEach
    void setUp() {
        pushRequest = new RechargeOrderPushRequest();
        pushRequest.setContractNo("CONTRACT001");
        pushRequest.setRechargeOrderNo("RO202401010001");
        pushRequest.setCustomerCode("CUST001");
        pushRequest.setCustomerName("测试客户");
        pushRequest.setSellerId("SELLER001");
        pushRequest.setSeller("销售员");
        pushRequest.setAreaCode("AREA001");
        pushRequest.setAreaName("测试区域");
        pushRequest.setCinemaInnerCode("CINEMA001");
        pushRequest.setCinemaName("测试影院");
        pushRequest.setEveryCardRechargeAmount(5000); // 50元
        pushRequest.setEveryCardPresentAmount(1000);  // 10元
        pushRequest.setCount(2);
        pushRequest.setAmount(12000); // 120元 = (50+10)*2
        pushRequest.setOperator("操作员");

        // 构建卡信息列表
        RechargeOrderCardPushInfo card1 = new RechargeOrderCardPushInfo();
        card1.setCardNo("CARD001");
        card1.setCardTypeCode("TYPE001");
        card1.setCardTypeName("普通卡");

        RechargeOrderCardPushInfo card2 = new RechargeOrderCardPushInfo();
        card2.setCardNo("CARD002");
        card2.setCardTypeCode("TYPE001");
        card2.setCardTypeName("普通卡");

        pushRequest.setCardInfos(Arrays.asList(card1, card2));
    }

    @Test
    @DisplayName("推送充值订单成功")
    void testPushRechargeOrderSuccess() {
        // Given
        when(rechargeOrderDao.findByRechargeOrderNo(pushRequest.getRechargeOrderNo())).thenReturn(null);
        doNothing().when(rechargeOrderDao).batchInsert(any());

        // When
        rechargeOrderPushServiceBiz.pushRechargeOrder(pushRequest);

        // Then
        verify(rechargeOrderDao).findByRechargeOrderNo(pushRequest.getRechargeOrderNo());
        verify(rechargeOrderDao).batchInsert(any());
    }

    @Test
    @DisplayName("推送充值订单失败-订单已存在")
    void testPushRechargeOrderFailWhenOrderExists() {
        // Given
        RechargeOrder existingOrder = new RechargeOrder();
        existingOrder.setRechargeOrderNo(pushRequest.getRechargeOrderNo());
        existingOrder.setCount(2);
        existingOrder.setAmount(12000);
        when(rechargeOrderDao.findByRechargeOrderNo(pushRequest.getRechargeOrderNo())).thenReturn(existingOrder);

        // When & Then
        assertThatThrownBy(() -> rechargeOrderPushServiceBiz.pushRechargeOrder(pushRequest))
                .isInstanceOf(BusinessException.class)
                .hasMessageContaining("充值订单号已存在");

        verify(rechargeOrderDao).findByRechargeOrderNo(pushRequest.getRechargeOrderNo());
        verify(rechargeOrderDao, never()).batchInsert(any());
    }

    @Test
    @DisplayName("推送充值订单失败-卡数量与卡信息列表数量不一致")
    void testPushRechargeOrderFailWhenCountMismatch() {
        // Given
        pushRequest.setCount(3); // 设置为3，但卡信息列表只有2个
        when(rechargeOrderDao.findByRechargeOrderNo(pushRequest.getRechargeOrderNo())).thenReturn(null);

        // When & Then
        assertThatThrownBy(() -> rechargeOrderPushServiceBiz.pushRechargeOrder(pushRequest))
                .isInstanceOf(BusinessException.class)
                .hasMessage("订单卡数量与卡信息列表数量不一致");

        verify(rechargeOrderDao).findByRechargeOrderNo(pushRequest.getRechargeOrderNo());
        verify(rechargeOrderDao, never()).batchInsert(any());
    }

    @Test
    @DisplayName("推送充值订单失败-总金额与计算金额不一致")
    void testPushRechargeOrderFailWhenAmountMismatch() {
        // Given
        pushRequest.setAmount(10000); // 设置错误的总金额
        when(rechargeOrderDao.findByRechargeOrderNo(pushRequest.getRechargeOrderNo())).thenReturn(null);

        // When & Then
        assertThatThrownBy(() -> rechargeOrderPushServiceBiz.pushRechargeOrder(pushRequest))
                .isInstanceOf(BusinessException.class)
                .hasMessage("总金额与计算金额不一致");

        verify(rechargeOrderDao).findByRechargeOrderNo(pushRequest.getRechargeOrderNo());
        verify(rechargeOrderDao, never()).batchInsert(any());
    }

    @Test
    @DisplayName("推送充值订单失败-存在重复卡号")
    void testPushRechargeOrderFailWhenDuplicateCardNos() {
        // Given
        RechargeOrderCardPushInfo card1 = new RechargeOrderCardPushInfo();
        card1.setCardNo("CARD001");
        card1.setCardTypeCode("TYPE001");
        card1.setCardTypeName("普通卡");

        RechargeOrderCardPushInfo card2 = new RechargeOrderCardPushInfo();
        card2.setCardNo("CARD001"); // 重复的卡号
        card2.setCardTypeCode("TYPE001");
        card2.setCardTypeName("普通卡");

        pushRequest.setCardInfos(Arrays.asList(card1, card2));
        when(rechargeOrderDao.findByRechargeOrderNo(pushRequest.getRechargeOrderNo())).thenReturn(null);

        // When & Then
        assertThatThrownBy(() -> rechargeOrderPushServiceBiz.pushRechargeOrder(pushRequest))
                .isInstanceOf(BusinessException.class)
                .hasMessageContaining("存在重复的卡号");

        verify(rechargeOrderDao).findByRechargeOrderNo(pushRequest.getRechargeOrderNo());
        verify(rechargeOrderDao, never()).batchInsert(any());
    }

    @Test
    @DisplayName("构建充值订单对象")
    void testBuildRechargeOrder() {
        // Given
        LocalDateTime now = LocalDateTime.now();

        // When
        RechargeOrder result = rechargeOrderPushServiceBiz.buildRechargeOrder(pushRequest, now);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getContractNo()).isEqualTo(pushRequest.getContractNo());
        assertThat(result.getRechargeOrderNo()).isEqualTo(pushRequest.getRechargeOrderNo());
        assertThat(result.getCustomerCode()).isEqualTo(pushRequest.getCustomerCode());
        assertThat(result.getCustomerName()).isEqualTo(pushRequest.getCustomerName());
        assertThat(result.getSellerId()).isEqualTo(pushRequest.getSellerId());
        assertThat(result.getSeller()).isEqualTo(pushRequest.getSeller());
        assertThat(result.getAreaCode()).isEqualTo(pushRequest.getAreaCode());
        assertThat(result.getAreaName()).isEqualTo(pushRequest.getAreaName());
        assertThat(result.getCinemaInnerCode()).isEqualTo(pushRequest.getCinemaInnerCode());
        assertThat(result.getCinemaName()).isEqualTo(pushRequest.getCinemaName());
        assertThat(result.getEveryCardRechargeAmount()).isEqualTo(pushRequest.getEveryCardRechargeAmount());
        assertThat(result.getEveryCardPresentAmount()).isEqualTo(pushRequest.getEveryCardPresentAmount());
        assertThat(result.getCount()).isEqualTo(pushRequest.getCount());
        assertThat(result.getAmount()).isEqualTo(pushRequest.getAmount());
        assertThat(result.getStatus()).isEqualTo(RechargeOrderStatus.WAIT_RECHARGE);
        assertThat(result.getCreateTime()).isEqualTo(now);
        assertThat(result.getUpdateTime()).isEqualTo(now);
    }

    @Test
    @DisplayName("构建充值订单卡信息列表")
    void testBuildRechargeOrderCardInfos() {
        // Given
        LocalDateTime now = LocalDateTime.now();

        // When
        List<RechargeOrderCardInfo> result = rechargeOrderPushServiceBiz.buildRechargeOrderCardInfos(pushRequest, now);

        // Then
        assertThat(result).hasSize(2);
        
        RechargeOrderCardInfo card1 = result.get(0);
        assertThat(card1.getRechargeOrderNo()).isEqualTo(pushRequest.getRechargeOrderNo());
        assertThat(card1.getCardNo()).isEqualTo("CARD001");
        assertThat(card1.getCardTypeCode()).isEqualTo("TYPE001");
        assertThat(card1.getCardTypeName()).isEqualTo("普通卡");
        assertThat(card1.getRechargeAmount()).isEqualTo(pushRequest.getEveryCardRechargeAmount());
        assertThat(card1.getPresentAmount()).isEqualTo(pushRequest.getEveryCardPresentAmount());
        assertThat(card1.getChangeBalance()).isEqualTo(pushRequest.getEveryCardRechargeAmount() + pushRequest.getEveryCardPresentAmount());
        assertThat(card1.getBeforeBalance()).isNull();
        assertThat(card1.getAfterBalance()).isNull();
        assertThat(card1.getStatus()).isEqualTo(CardRechargeStatus.WAIT_RECHARGE);
        assertThat(card1.getCreateTime()).isEqualTo(now);
        assertThat(card1.getUpdateTime()).isEqualTo(now);

        RechargeOrderCardInfo card2 = result.get(1);
        assertThat(card2.getRechargeOrderNo()).isEqualTo(pushRequest.getRechargeOrderNo());
        assertThat(card2.getCardNo()).isEqualTo("CARD002");
        assertThat(card2.getCardTypeCode()).isEqualTo("TYPE001");
        assertThat(card2.getCardTypeName()).isEqualTo("普通卡");
        assertThat(card2.getRechargeAmount()).isEqualTo(pushRequest.getEveryCardRechargeAmount());
        assertThat(card2.getPresentAmount()).isEqualTo(pushRequest.getEveryCardPresentAmount());
        assertThat(card2.getChangeBalance()).isEqualTo(pushRequest.getEveryCardRechargeAmount() + pushRequest.getEveryCardPresentAmount());
        assertThat(card2.getBeforeBalance()).isNull();
        assertThat(card2.getAfterBalance()).isNull();
        assertThat(card2.getStatus()).isEqualTo(CardRechargeStatus.WAIT_RECHARGE);
        assertThat(card2.getCreateTime()).isEqualTo(now);
        assertThat(card2.getUpdateTime()).isEqualTo(now);
    }

    @Test
    @DisplayName("构建操作日志")
    void testBuildOperationLog() {
        // Given
        LocalDateTime now = LocalDateTime.now();

        // When
        RechargeOrderLog result = rechargeOrderPushServiceBiz.buildOperationLog(pushRequest, now);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getRechargeOrderNo()).isEqualTo(pushRequest.getRechargeOrderNo());
        assertThat(result.getBatchNo()).isNull();
        assertThat(result.getPushStatus()).isNull();
        assertThat(result.getOperationType()).isEqualTo(OperationLogType.CREATE_ORDER);
        assertThat(result.getOperator()).isEqualTo(pushRequest.getOperator());
        assertThat(result.getOperationLog()).contains("创建充值订单");
        assertThat(result.getOperationLog()).contains("卡数量：2");
        assertThat(result.getOperationLog()).contains(MoneyUtil.fenToYuanStrExcludeDecimalPoint(pushRequest.getAmount()));
        assertThat(result.getCreateTime()).isEqualTo(now);
        assertThat(result.getUpdateTime()).isEqualTo(now);
    }

    @Test
    @DisplayName("事务处理验证")
    void testTransactionHandling() {
        // Given
        when(rechargeOrderDao.findByRechargeOrderNo(pushRequest.getRechargeOrderNo())).thenReturn(null);
        doNothing().when(rechargeOrderDao).batchInsert(any());

        // When
        rechargeOrderPushServiceBiz.pushRechargeOrder(pushRequest);

        // Then
        verify(rechargeOrderDao).findByRechargeOrderNo(pushRequest.getRechargeOrderNo());
        verify(rechargeOrderDao).batchInsert(any());
    }

    @Test
    @DisplayName("检查方法-订单已存在异常消息验证")
    void testCheckMethodExceptionMessage() {
        // Given
        RechargeOrder existingOrder = new RechargeOrder();
        existingOrder.setRechargeOrderNo(pushRequest.getRechargeOrderNo());
        existingOrder.setCount(5);
        existingOrder.setAmount(25000); // 250元
        when(rechargeOrderDao.findByRechargeOrderNo(pushRequest.getRechargeOrderNo())).thenReturn(existingOrder);

        // When & Then
        assertThatThrownBy(() -> rechargeOrderPushServiceBiz.pushRechargeOrder(pushRequest))
                .isInstanceOf(BusinessException.class)
                .hasMessageContaining("充值订单号已存在：" + pushRequest.getRechargeOrderNo())
                .hasMessageContaining("卡总数:5")
                .hasMessageContaining("总金额:250元");
    }

    @Test
    @DisplayName("私有方法测试-通过反射调用check方法")
    void testCheckMethodDirectly() throws Exception {
        // Given
        when(rechargeOrderDao.findByRechargeOrderNo(pushRequest.getRechargeOrderNo())).thenReturn(null);

        // When & Then - 正常情况不应该抛出异常
        java.lang.reflect.Method checkMethod = RechargeOrderPushServiceBiz.class.getDeclaredMethod("check", RechargeOrderPushRequest.class);
        checkMethod.setAccessible(true);
        
        // 不应该抛出异常
        checkMethod.invoke(rechargeOrderPushServiceBiz, pushRequest);
        
        verify(rechargeOrderDao).findByRechargeOrderNo(pushRequest.getRechargeOrderNo());
    }

    @Test
    @DisplayName("私有方法测试-通过反射调用buildRechargeOrder方法")
    void testBuildRechargeOrderMethodDirectly() throws Exception {
        // Given
        LocalDateTime now = LocalDateTime.now();

        // When
        java.lang.reflect.Method buildMethod = RechargeOrderPushServiceBiz.class.getDeclaredMethod("buildRechargeOrder", RechargeOrderPushRequest.class, LocalDateTime.class);
        buildMethod.setAccessible(true);
        RechargeOrder result = (RechargeOrder) buildMethod.invoke(rechargeOrderPushServiceBiz, pushRequest, now);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getRechargeOrderNo()).isEqualTo(pushRequest.getRechargeOrderNo());
        assertThat(result.getStatus()).isEqualTo(RechargeOrderStatus.WAIT_RECHARGE);
    }

    @Test
    @DisplayName("私有方法测试-通过反射调用buildRechargeOrderCardInfos方法")
    void testBuildRechargeOrderCardInfosMethodDirectly() throws Exception {
        // Given
        LocalDateTime now = LocalDateTime.now();

        // When
        java.lang.reflect.Method buildMethod = RechargeOrderPushServiceBiz.class.getDeclaredMethod("buildRechargeOrderCardInfos", RechargeOrderPushRequest.class, LocalDateTime.class);
        buildMethod.setAccessible(true);
        @SuppressWarnings("unchecked")
        List<RechargeOrderCardInfo> result = (List<RechargeOrderCardInfo>) buildMethod.invoke(rechargeOrderPushServiceBiz, pushRequest, now);

        // Then
        assertThat(result).hasSize(2);
        assertThat(result.get(0).getCardNo()).isEqualTo("CARD001");
        assertThat(result.get(1).getCardNo()).isEqualTo("CARD002");
    }

    @Test
    @DisplayName("私有方法测试-通过反射调用buildOperationLog方法")
    void testBuildOperationLogMethodDirectly() throws Exception {
        // Given
        LocalDateTime now = LocalDateTime.now();

        // When
        java.lang.reflect.Method buildMethod = RechargeOrderPushServiceBiz.class.getDeclaredMethod("buildOperationLog", RechargeOrderPushRequest.class, LocalDateTime.class);
        buildMethod.setAccessible(true);
        RechargeOrderLog result = (RechargeOrderLog) buildMethod.invoke(rechargeOrderPushServiceBiz, pushRequest, now);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getOperationType()).isEqualTo(OperationLogType.CREATE_ORDER);
        assertThat(result.getOperationLog()).contains("创建充值订单");
    }
}