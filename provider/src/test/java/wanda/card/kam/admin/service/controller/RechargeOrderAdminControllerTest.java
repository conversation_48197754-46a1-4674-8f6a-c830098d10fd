package wanda.card.kam.admin.service.controller;

import com.alibaba.fastjson.JSON;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import wanda.card.kam.admin.contract.dto.RechargeOrderAdminDto;
import wanda.card.kam.admin.service.ServiceApplication;
import wanda.stark.core.data.PageInfo;
import wanda.stark.core.data.PageResult;
import wanda.stark.core.data.R;

@SpringBootTest(classes = {ServiceApplication.class}, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class RechargeOrderAdminControllerTest {

    @Autowired
    private RechargeOrderAdminController adminController;

    @Test
    public void testList() {
        RechargeOrderAdminDto.ListRequest request = new RechargeOrderAdminDto.ListRequest();
        request.setPageInfo(new PageInfo(1,10));
        PageResult<RechargeOrderAdminDto.RechargeOrder> result = adminController.list(request);
        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public void testGetRechargeOrderDetail() {
        R<RechargeOrderAdminDto.RechargeOrderDetail> result = adminController.getRechargeOrderDetail(1L);
        System.out.println(JSON.toJSONString(result));
    }
}
