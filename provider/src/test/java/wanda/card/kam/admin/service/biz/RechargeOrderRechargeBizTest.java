package wanda.card.kam.admin.service.biz;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import wanda.card.kam.admin.contract.dto.RechargeOrderRechargeDto.RechargeOrderRechargeRequest;
import wanda.card.kam.common.contract.constant.BusinessException;
import wanda.card.kam.common.contract.constant.CardRechargeStatus;
import wanda.card.kam.common.contract.constant.RechargeOrderStatus;
import wanda.card.kam.common.provider.dao.RechargeOrderCardInfoDao;
import wanda.card.kam.common.provider.dao.RechargeOrderDao;
import wanda.card.kam.common.provider.dao.RechargeOrderLogDao;
import wanda.card.kam.common.provider.entity.RechargeOrder;
import wanda.card.kam.common.provider.entity.RechargeOrderCardInfo;
import wanda.card.kam.common.provider.entity.RechargeOrderLog;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 充值订单充值业务逻辑测试
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class RechargeOrderRechargeBizTest {

    @Mock
    private RechargeOrderDao rechargeOrderDao;

    @Mock
    private RechargeOrderLogDao rechargeOrderLogDao;

    @Mock
    private RechargeOrderCardInfoDao rechargeOrderCardInfoDao;

    @InjectMocks
    private RechargeOrderRechargeBiz rechargeOrderRechargeBiz;

    private RechargeOrderRechargeRequest validRequest;
    private RechargeOrder validOrder;

    @BeforeEach
    void setUp() {
        // 创建有效的充值请求
        validRequest = new RechargeOrderRechargeRequest();
        validRequest.setBatchNo("BATCH001");
        validRequest.setContractNo("CONTRACT001");
        validRequest.setRechargeOrderNo("ORDER001");
        validRequest.setEveryCardRechargeAmount(10000);
        validRequest.setEveryCardPresentAmount(0);
        validRequest.setOperator("admin");
        validRequest.setCardNos(Arrays.asList("CARD001", "CARD002"));

        // 创建有效的充值订单
        validOrder = new RechargeOrder();
        validOrder.setRechargeOrderNo("ORDER001");
        validOrder.setContractNo("CONTRACT001");
        validOrder.setEveryCardRechargeAmount(10000);
        validOrder.setEveryCardPresentAmount(0);
        validOrder.setStatus(RechargeOrderStatus.WAIT_RECHARGE);
    }

    @Test
    @DisplayName("测试单批次内卡号重复校验-正常情况")
    void testValidateCardNosUnique_Success() {
        // Given
        when(rechargeOrderLogDao.findByBatchNo(anyString())).thenReturn(null);
        when(rechargeOrderCardInfoDao.findExistingCardNosExcludeBatch(anyString(), anyString(), anyList()))
                .thenReturn(Collections.emptyList());
        when(rechargeOrderDao.findByRechargeOrderNo(anyString())).thenReturn(validOrder);
        when(rechargeOrderCardInfoDao.findByRechargeOrderNoAndCardNo(anyString(), anyList()))
                .thenReturn(createValidCardInfos());

        // When & Then
        assertDoesNotThrow(() -> rechargeOrderRechargeBiz.rechargeOrder(validRequest));
    }

    @Test
    @DisplayName("测试单批次内卡号重复校验-存在重复卡号")
    void testValidateCardNosUnique_DuplicateCards() {
        // Given
        RechargeOrderRechargeRequest request = new RechargeOrderRechargeRequest();
        request.setBatchNo("BATCH001");
        request.setContractNo("CONTRACT001");
        request.setRechargeOrderNo("ORDER001");
        request.setEveryCardRechargeAmount(10000);
        request.setEveryCardPresentAmount(0);
        request.setOperator("admin");
        request.setCardNos(Arrays.asList("CARD001", "CARD002", "CARD001")); // 重复卡号

        when(rechargeOrderLogDao.findByBatchNo(anyString())).thenReturn(null);

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class,
                () -> rechargeOrderRechargeBiz.rechargeOrder(request));
        assertEquals("卡号列表中存在重复卡号", exception.getMessage());
    }

    @Test
    @DisplayName("测试跨批次卡号重复校验-正常情况")
    void testValidateCardNosAcrossBatches_Success() {
        // Given
        when(rechargeOrderLogDao.findByBatchNo(anyString())).thenReturn(null);
        when(rechargeOrderCardInfoDao.findExistingCardNosExcludeBatch(anyString(), anyString(), anyList()))
                .thenReturn(Collections.emptyList()); // 没有重复卡号
        when(rechargeOrderDao.findByRechargeOrderNo(anyString())).thenReturn(validOrder);
        when(rechargeOrderCardInfoDao.findByRechargeOrderNoAndCardNo(anyString(), anyList()))
                .thenReturn(createValidCardInfos());

        // When & Then
        assertDoesNotThrow(() -> rechargeOrderRechargeBiz.rechargeOrder(validRequest));
    }

    @Test
    @DisplayName("测试跨批次卡号重复校验-存在重复卡号")
    void testValidateCardNosAcrossBatches_DuplicateCards() {
        // Given
        when(rechargeOrderLogDao.findByBatchNo(anyString())).thenReturn(null);
        // 模拟存在跨批次重复的卡号
        when(rechargeOrderCardInfoDao.findExistingCardNosExcludeBatch(anyString(), anyString(), anyList()))
                .thenReturn(Arrays.asList("CARD001", "CARD002")); // 返回重复的卡号

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class,
                () -> rechargeOrderRechargeBiz.rechargeOrder(validRequest));
        assertTrue(exception.getMessage().contains("中存在跨批次重复的卡号"));
        assertTrue(exception.getMessage().contains("CARD001"));
        assertTrue(exception.getMessage().contains("CARD002"));
        assertTrue(exception.getMessage().contains("每批次的卡号不能相同"));
    }

    @Test
    @DisplayName("测试跨批次卡号重复校验-部分卡号重复")
    void testValidateCardNosAcrossBatches_PartialDuplicateCards() {
        // Given
        RechargeOrderRechargeRequest request = new RechargeOrderRechargeRequest();
        request.setBatchNo("BATCH002");
        request.setContractNo("CONTRACT001");
        request.setRechargeOrderNo("ORDER001");
        request.setEveryCardRechargeAmount(10000);
        request.setEveryCardPresentAmount(0);
        request.setOperator("admin");
        request.setCardNos(Arrays.asList("CARD001", "CARD003", "CARD004")); // CARD001重复，CARD003、CARD004不重复

        when(rechargeOrderLogDao.findByBatchNo(anyString())).thenReturn(null);
        // 模拟只有CARD001在其他批次中存在
        when(rechargeOrderCardInfoDao.findExistingCardNosExcludeBatch(anyString(), anyString(), anyList()))
                .thenReturn(Arrays.asList("CARD001"));

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class,
                () -> rechargeOrderRechargeBiz.rechargeOrder(request));
        assertTrue(exception.getMessage().contains("中存在跨批次重复的卡号"));
        assertTrue(exception.getMessage().contains("CARD001"));
        assertFalse(exception.getMessage().contains("CARD003"));
        assertFalse(exception.getMessage().contains("CARD004"));
    }

    @Test
    @DisplayName("测试跨批次卡号重复校验-不同订单号")
    void testValidateCardNosAcrossBatches_DifferentOrderNo() {
        // Given
        RechargeOrderRechargeRequest request = new RechargeOrderRechargeRequest();
        request.setBatchNo("BATCH001");
        request.setContractNo("CONTRACT001");
        request.setRechargeOrderNo("ORDER002"); // 不同的订单号
        request.setEveryCardRechargeAmount(10000);
        request.setEveryCardPresentAmount(0);
        request.setOperator("admin");
        request.setCardNos(Arrays.asList("CARD001", "CARD002"));

        when(rechargeOrderLogDao.findByBatchNo(anyString())).thenReturn(null);
        // 对于不同订单号，应该查询该订单下的跨批次重复情况
        when(rechargeOrderCardInfoDao.findExistingCardNosExcludeBatch(eq("ORDER002"), anyString(), anyList()))
                .thenReturn(Collections.emptyList()); // 该订单下没有重复卡号

        RechargeOrder order2 = new RechargeOrder();
        order2.setRechargeOrderNo("ORDER002");
        order2.setContractNo("CONTRACT001");
        order2.setEveryCardRechargeAmount(10000);
        order2.setEveryCardPresentAmount(0);
        order2.setStatus(RechargeOrderStatus.WAIT_RECHARGE);

        when(rechargeOrderDao.findByRechargeOrderNo("ORDER002")).thenReturn(order2);
        when(rechargeOrderCardInfoDao.findByRechargeOrderNoAndCardNo(eq("ORDER002"), anyList()))
                .thenReturn(createValidCardInfosForOrder("ORDER002"));

        // When & Then
        assertDoesNotThrow(() -> rechargeOrderRechargeBiz.rechargeOrder(request));

        // 验证调用了正确的订单号进行查询
        verify(rechargeOrderCardInfoDao).findExistingCardNosExcludeBatch(eq("ORDER002"), anyString(), anyList());
    }


    @Test
    @DisplayName("测试幂等性校验-批次号不存在")
    void testValidateIdempotent_BatchNotExists() {
        // Given
        when(rechargeOrderLogDao.findByBatchNo(anyString())).thenReturn(null);
        when(rechargeOrderCardInfoDao.findExistingCardNosExcludeBatch(anyString(), anyString(), anyList()))
                .thenReturn(Collections.emptyList());
        when(rechargeOrderDao.findByRechargeOrderNo(anyString())).thenReturn(validOrder);
        when(rechargeOrderCardInfoDao.findByRechargeOrderNoAndCardNo(anyString(), anyList()))
                .thenReturn(createValidCardInfos());

        // When & Then
        assertDoesNotThrow(() -> rechargeOrderRechargeBiz.rechargeOrder(validRequest));

        // 验证后续逻辑被执行
        verify(rechargeOrderDao).findByRechargeOrderNo(anyString());
    }

    @Test
    @DisplayName("测试幂等性校验-批次号属于其他订单")
    void testValidateIdempotent_BatchBelongsToOtherOrder() {
        // Given
        RechargeOrderLog existingLog = new RechargeOrderLog();
        existingLog.setBatchNo("BATCH001");
        existingLog.setRechargeOrderNo("OTHER_ORDER"); // 不同的订单号

        when(rechargeOrderLogDao.findByBatchNo(anyString())).thenReturn(existingLog);

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class,
                () -> rechargeOrderRechargeBiz.rechargeOrder(validRequest));
        assertTrue(exception.getMessage().contains("该批次号BATCH001在其他订单号OTHER_ORDER已存在"));
    }

    @Test
    @DisplayName("测试幂等性校验-批次号存在且卡号一致")
    void testValidateIdempotent_BatchExistsWithSameCards() {
        // Given
        RechargeOrderLog existingLog = new RechargeOrderLog();
        existingLog.setBatchNo("BATCH001");
        existingLog.setRechargeOrderNo("ORDER001"); // 相同订单号

        RechargeOrderCardInfo card1 = new RechargeOrderCardInfo();
        card1.setCardNo("CARD001");
        RechargeOrderCardInfo card2 = new RechargeOrderCardInfo();
        card2.setCardNo("CARD002");

        when(rechargeOrderLogDao.findByBatchNo(anyString())).thenReturn(existingLog);
        when(rechargeOrderCardInfoDao.findByBatchNo(anyString()))
                .thenReturn(Arrays.asList(card1, card2));

        // When & Then
        assertDoesNotThrow(() -> rechargeOrderRechargeBiz.rechargeOrder(validRequest));

        // 验证后续逻辑不被执行（幂等性通过）
        verify(rechargeOrderDao, never()).findByRechargeOrderNo(anyString());
    }

    @Test
    @DisplayName("测试幂等性校验-批次号存在但卡号不一致")
    void testValidateIdempotent_BatchExistsWithDifferentCards() {
        // Given
        RechargeOrderLog existingLog = new RechargeOrderLog();
        existingLog.setBatchNo("BATCH001");
        existingLog.setRechargeOrderNo("ORDER001");

        RechargeOrderCardInfo card1 = new RechargeOrderCardInfo();
        card1.setCardNo("CARD003"); // 不同的卡号
        RechargeOrderCardInfo card2 = new RechargeOrderCardInfo();
        card2.setCardNo("CARD004"); // 不同的卡号

        when(rechargeOrderLogDao.findByBatchNo(anyString())).thenReturn(existingLog);
        when(rechargeOrderCardInfoDao.findByBatchNo(anyString()))
                .thenReturn(Arrays.asList(card1, card2));

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class,
                () -> rechargeOrderRechargeBiz.rechargeOrder(validRequest));
        assertTrue(exception.getMessage().contains("已存在，但卡号信息不一致"));
    }

    @Test
    @DisplayName("测试订单状态校验-订单不存在")
    void testValidateOrderStatus_OrderNotExists() {
        // Given
        when(rechargeOrderLogDao.findByBatchNo(anyString())).thenReturn(null);
        when(rechargeOrderCardInfoDao.findExistingCardNosExcludeBatch(anyString(), anyString(), anyList()))
                .thenReturn(Collections.emptyList());
        when(rechargeOrderDao.findByRechargeOrderNo(anyString())).thenReturn(null);

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class,
                () -> rechargeOrderRechargeBiz.rechargeOrder(validRequest));
        assertTrue(exception.getMessage().contains("充值订单不存在"));
    }

    @Test
    @DisplayName("测试订单状态校验-订单状态无效")
    void testValidateOrderStatus_OrderInvalid() {
        // Given
        RechargeOrder invalidOrder = new RechargeOrder();
        invalidOrder.setRechargeOrderNo("ORDER001");
        invalidOrder.setStatus(RechargeOrderStatus.INVALID);

        when(rechargeOrderLogDao.findByBatchNo(anyString())).thenReturn(null);
        when(rechargeOrderCardInfoDao.findExistingCardNosExcludeBatch(anyString(), anyString(), anyList()))
                .thenReturn(Collections.emptyList());
        when(rechargeOrderDao.findByRechargeOrderNo(anyString())).thenReturn(invalidOrder);

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class,
                () -> rechargeOrderRechargeBiz.rechargeOrder(validRequest));
        assertTrue(exception.getMessage().contains("充值订单状态无效"));
    }

    @Test
    @DisplayName("测试订单状态校验-订单已完成")
    void testValidateOrderStatus_OrderSuccess() {
        // Given
        RechargeOrder successOrder = new RechargeOrder();
        successOrder.setRechargeOrderNo("ORDER001");
        successOrder.setStatus(RechargeOrderStatus.SUCCESS);

        when(rechargeOrderLogDao.findByBatchNo(anyString())).thenReturn(null);
        when(rechargeOrderCardInfoDao.findExistingCardNosExcludeBatch(anyString(), anyString(), anyList()))
                .thenReturn(Collections.emptyList());
        when(rechargeOrderDao.findByRechargeOrderNo(anyString())).thenReturn(successOrder);

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class,
                () -> rechargeOrderRechargeBiz.rechargeOrder(validRequest));
        assertTrue(exception.getMessage().contains("充值订单状态全部完成"));
    }

    /**
     * 创建有效的卡信息列表
     */
    private List<RechargeOrderCardInfo> createValidCardInfos() {
        return createValidCardInfosForOrder("ORDER001");
    }

    /**
     * 创建指定订单号的有效卡信息列表
     */
    private List<RechargeOrderCardInfo> createValidCardInfosForOrder(String orderNo) {
        RechargeOrderCardInfo card1 = new RechargeOrderCardInfo();
        card1.setCardNo("CARD001");
        card1.setRechargeOrderNo(orderNo);
        card1.setStatus(CardRechargeStatus.WAIT_RECHARGE);
        card1.setCreateTime(LocalDateTime.now());
        card1.setUpdateTime(LocalDateTime.now());

        RechargeOrderCardInfo card2 = new RechargeOrderCardInfo();
        card2.setCardNo("CARD002");
        card2.setRechargeOrderNo(orderNo);
        card2.setStatus(CardRechargeStatus.WAIT_RECHARGE);
        card2.setCreateTime(LocalDateTime.now());
        card2.setUpdateTime(LocalDateTime.now());

        return Arrays.asList(card1, card2);
    }

    // ==================== 幂等校验卡状态测试用例 ====================

    @Test
    @DisplayName("幂等校验-存在充值中状态的卡，抛出业务异常")
    void testHandleIdempotentCardStatus_HasRechargingCards() {
        // Given
        RechargeOrderLog existingLog = new RechargeOrderLog();
        existingLog.setBatchNo("BATCH001");
        existingLog.setRechargeOrderNo("ORDER001");

        // 创建包含充值中状态的卡信息
        List<RechargeOrderCardInfo> cardInfos = Arrays.asList(
                createCardInfo("CARD001", CardRechargeStatus.RECHARGING),
                createCardInfo("CARD002", CardRechargeStatus.WAIT_RECHARGE)
        );

        when(rechargeOrderLogDao.findByBatchNo("BATCH001")).thenReturn(existingLog);
        when(rechargeOrderCardInfoDao.findByBatchNo("BATCH001")).thenReturn(cardInfos);

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class,
                () -> rechargeOrderRechargeBiz.rechargeOrder(validRequest));

        assertTrue(exception.getMessage().contains("该批次正在充值中"));
        assertTrue(exception.getMessage().contains("BATCH001"));

        // 验证不会执行后续逻辑
        verify(rechargeOrderDao, never()).findByRechargeOrderNo(anyString());
    }

    @Test
    @DisplayName("幂等校验-所有卡都是充值成功状态，发送通知消息")
    void testHandleIdempotentCardStatus_AllCardsSuccess() {
        // Given
        RechargeOrderLog existingLog = new RechargeOrderLog();
        existingLog.setBatchNo("BATCH001");
        existingLog.setRechargeOrderNo("ORDER001");

        // 创建所有卡都是充值成功状态的卡信息
        List<RechargeOrderCardInfo> cardInfos = Arrays.asList(
                createCardInfo("CARD001", CardRechargeStatus.SUCCESS),
                createCardInfo("CARD002", CardRechargeStatus.SUCCESS)
        );

        when(rechargeOrderLogDao.findByBatchNo("BATCH001")).thenReturn(existingLog);
        when(rechargeOrderCardInfoDao.findByBatchNo("BATCH001")).thenReturn(cardInfos);

        // When
        assertDoesNotThrow(() -> rechargeOrderRechargeBiz.rechargeOrder(validRequest));

        // Then
        // 验证发送了通知消息（这里需要mock PublisherUtil，但由于是静态方法，实际项目中可能需要重构）
        // 验证不会执行后续逻辑
        verify(rechargeOrderDao, never()).findByRechargeOrderNo(anyString());
    }

    @Test
    @DisplayName("幂等校验-存在充值失败的卡，发送重新充值消息")
    void testHandleIdempotentCardStatus_HasFailedCards() {
        // Given
        RechargeOrderLog existingLog = new RechargeOrderLog();
        existingLog.setBatchNo("BATCH001");
        existingLog.setRechargeOrderNo("ORDER001");

        // 创建包含充值失败状态的卡信息
        List<RechargeOrderCardInfo> cardInfos = Arrays.asList(
                createCardInfo("CARD001", CardRechargeStatus.SUCCESS),
                createCardInfo("CARD002", CardRechargeStatus.FAILED)
        );

        when(rechargeOrderLogDao.findByBatchNo("BATCH001")).thenReturn(existingLog);
        when(rechargeOrderCardInfoDao.findByBatchNo("BATCH001")).thenReturn(cardInfos);

        // When
        assertDoesNotThrow(() -> rechargeOrderRechargeBiz.rechargeOrder(validRequest));

        // Then
        // 验证发送了重新充值消息
        // 验证不会执行后续逻辑
        verify(rechargeOrderDao, never()).findByRechargeOrderNo(anyString());
    }

    @Test
    @DisplayName("幂等校验-混合状态但无充值中，有失败卡优先处理")
    void testHandleIdempotentCardStatus_MixedStatusWithFailedPriority() {
        // Given
        RechargeOrderLog existingLog = new RechargeOrderLog();
        existingLog.setBatchNo("BATCH001");
        existingLog.setRechargeOrderNo("ORDER001");

        // 创建混合状态的卡信息：成功+失败+待充值
        List<RechargeOrderCardInfo> cardInfos = Arrays.asList(
                createCardInfo("CARD001", CardRechargeStatus.SUCCESS),
                createCardInfo("CARD002", CardRechargeStatus.FAILED),
                createCardInfo("CARD003", CardRechargeStatus.WAIT_RECHARGE)
        );

        RechargeOrderRechargeRequest request = new RechargeOrderRechargeRequest();
        request.setBatchNo("BATCH001");
        request.setRechargeOrderNo("ORDER001");
        request.setCardNos(Arrays.asList("CARD001", "CARD002", "CARD003"));

        when(rechargeOrderLogDao.findByBatchNo("BATCH001")).thenReturn(existingLog);
        when(rechargeOrderCardInfoDao.findByBatchNo("BATCH001")).thenReturn(cardInfos);

        // When
        assertDoesNotThrow(() -> rechargeOrderRechargeBiz.rechargeOrder(request));

        // Then
        // 验证发送了重新充值消息（因为有失败的卡）
        verify(rechargeOrderDao, never()).findByRechargeOrderNo(anyString());
    }

    @Test
    @DisplayName("幂等校验-边界条件：单张卡充值中")
    void testHandleIdempotentCardStatus_SingleCardRecharging() {
        // Given
        RechargeOrderLog existingLog = new RechargeOrderLog();
        existingLog.setBatchNo("BATCH001");
        existingLog.setRechargeOrderNo("ORDER001");

        List<RechargeOrderCardInfo> cardInfos = Arrays.asList(
                createCardInfo("CARD001", CardRechargeStatus.RECHARGING)
        );

        RechargeOrderRechargeRequest request = new RechargeOrderRechargeRequest();
        request.setBatchNo("BATCH001");
        request.setRechargeOrderNo("ORDER001");
        request.setCardNos(Arrays.asList("CARD001"));

        when(rechargeOrderLogDao.findByBatchNo("BATCH001")).thenReturn(existingLog);
        when(rechargeOrderCardInfoDao.findByBatchNo("BATCH001")).thenReturn(cardInfos);

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class,
                () -> rechargeOrderRechargeBiz.rechargeOrder(request));

        assertTrue(exception.getMessage().contains("该批次正在充值中"));
    }

    @Test
    @DisplayName("幂等校验-边界条件：单张卡充值成功")
    void testHandleIdempotentCardStatus_SingleCardSuccess() {
        // Given
        RechargeOrderLog existingLog = new RechargeOrderLog();
        existingLog.setBatchNo("BATCH001");
        existingLog.setRechargeOrderNo("ORDER001");

        List<RechargeOrderCardInfo> cardInfos = Arrays.asList(
                createCardInfo("CARD001", CardRechargeStatus.SUCCESS)
        );

        RechargeOrderRechargeRequest request = new RechargeOrderRechargeRequest();
        request.setBatchNo("BATCH001");
        request.setRechargeOrderNo("ORDER001");
        request.setCardNos(Arrays.asList("CARD001"));

        when(rechargeOrderLogDao.findByBatchNo("BATCH001")).thenReturn(existingLog);
        when(rechargeOrderCardInfoDao.findByBatchNo("BATCH001")).thenReturn(cardInfos);

        // When
        assertDoesNotThrow(() -> rechargeOrderRechargeBiz.rechargeOrder(request));

        // Then
        verify(rechargeOrderDao, never()).findByRechargeOrderNo(anyString());
    }

    @Test
    @DisplayName("幂等校验-性能测试：大量卡状态检查")
    void testHandleIdempotentCardStatus_PerformanceTest() {
        // Given
        RechargeOrderLog existingLog = new RechargeOrderLog();
        existingLog.setBatchNo("BATCH001");
        existingLog.setRechargeOrderNo("ORDER001");

        // 创建1000张卡的状态信息
        List<RechargeOrderCardInfo> cardInfos = new java.util.ArrayList<>();
        List<String> cardNos = new java.util.ArrayList<>();

        for (int i = 1; i <= 1000; i++) {
            String cardNo = String.format("CARD%04d", i);
            cardNos.add(cardNo);

            // 模拟不同状态分布：70%成功，20%待充值，9%失败，1%充值中
            CardRechargeStatus status;
            if (i <= 700) {
                status = CardRechargeStatus.SUCCESS;
            } else if (i <= 900) {
                status = CardRechargeStatus.WAIT_RECHARGE;
            } else if (i <= 990) {
                status = CardRechargeStatus.FAILED;
            } else {
                status = CardRechargeStatus.RECHARGING;
            }

            cardInfos.add(createCardInfo(cardNo, status));
        }

        RechargeOrderRechargeRequest request = new RechargeOrderRechargeRequest();
        request.setBatchNo("BATCH001");
        request.setRechargeOrderNo("ORDER001");
        request.setCardNos(cardNos);

        when(rechargeOrderLogDao.findByBatchNo("BATCH001")).thenReturn(existingLog);
        when(rechargeOrderCardInfoDao.findByBatchNo("BATCH001")).thenReturn(cardInfos);

        // When & Then
        long startTime = System.currentTimeMillis();

        BusinessException exception = assertThrows(BusinessException.class,
                () -> rechargeOrderRechargeBiz.rechargeOrder(request));

        long endTime = System.currentTimeMillis();
        long executionTime = endTime - startTime;

        // 验证性能：处理1000张卡应该在合理时间内完成（比如100ms内）
        assertTrue(executionTime < 100, "处理1000张卡的时间应该在100ms内，实际用时：" + executionTime + "ms");

        // 验证逻辑正确性：由于有充值中的卡，应该抛出异常
        assertTrue(exception.getMessage().contains("该批次正在充值中"));
    }

    @Test
    @DisplayName("幂等校验-异常输入：null卡信息列表")
    void testHandleIdempotentCardStatus_NullCardInfos() {
        // Given
        RechargeOrderLog existingLog = new RechargeOrderLog();
        existingLog.setBatchNo("BATCH001");
        existingLog.setRechargeOrderNo("ORDER001");

        when(rechargeOrderLogDao.findByBatchNo("BATCH001")).thenReturn(existingLog);
        when(rechargeOrderCardInfoDao.findByBatchNo("BATCH001")).thenReturn(null);

        // When & Then
        // 应该能够处理null情况而不抛出NullPointerException
        assertThrows(Exception.class, () -> rechargeOrderRechargeBiz.rechargeOrder(validRequest));
    }

    @Test
    @DisplayName("幂等校验-状态统计准确性验证")
    void testHandleIdempotentCardStatus_StatusCountAccuracy() {
        // Given
        RechargeOrderLog existingLog = new RechargeOrderLog();
        existingLog.setBatchNo("BATCH001");
        existingLog.setRechargeOrderNo("ORDER001");

        // 精确控制各状态数量：2个充值中，3个成功，1个失败，4个待充值
        List<RechargeOrderCardInfo> cardInfos = Arrays.asList(
                createCardInfo("CARD001", CardRechargeStatus.RECHARGING),
                createCardInfo("CARD002", CardRechargeStatus.RECHARGING),
                createCardInfo("CARD003", CardRechargeStatus.SUCCESS),
                createCardInfo("CARD004", CardRechargeStatus.SUCCESS),
                createCardInfo("CARD005", CardRechargeStatus.SUCCESS),
                createCardInfo("CARD006", CardRechargeStatus.FAILED),
                createCardInfo("CARD007", CardRechargeStatus.WAIT_RECHARGE),
                createCardInfo("CARD008", CardRechargeStatus.WAIT_RECHARGE),
                createCardInfo("CARD009", CardRechargeStatus.WAIT_RECHARGE),
                createCardInfo("CARD010", CardRechargeStatus.WAIT_RECHARGE)
        );

        RechargeOrderRechargeRequest request = new RechargeOrderRechargeRequest();
        request.setBatchNo("BATCH001");
        request.setRechargeOrderNo("ORDER001");
        request.setCardNos(Arrays.asList("CARD001", "CARD002", "CARD003", "CARD004", "CARD005",
                "CARD006", "CARD007", "CARD008", "CARD009", "CARD010"));

        when(rechargeOrderLogDao.findByBatchNo("BATCH001")).thenReturn(existingLog);
        when(rechargeOrderCardInfoDao.findByBatchNo("BATCH001")).thenReturn(cardInfos);

        // When & Then
        // 由于有充值中的卡，应该抛出异常
        BusinessException exception = assertThrows(BusinessException.class,
                () -> rechargeOrderRechargeBiz.rechargeOrder(request));

        assertTrue(exception.getMessage().contains("该批次正在充值中"));
    }

    /**
     * 创建指定状态的卡信息
     */
    private RechargeOrderCardInfo createCardInfo(String cardNo, CardRechargeStatus status) {
        RechargeOrderCardInfo cardInfo = new RechargeOrderCardInfo();
        cardInfo.setCardNo(cardNo);
        cardInfo.setStatus(status);
        cardInfo.setRechargeOrderNo("ORDER001");
        cardInfo.setBatchNo("BATCH001");
        cardInfo.setCreateTime(LocalDateTime.now());
        cardInfo.setUpdateTime(LocalDateTime.now());
        return cardInfo;
    }
}
