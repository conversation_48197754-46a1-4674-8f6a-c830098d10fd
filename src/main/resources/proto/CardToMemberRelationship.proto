
// Generated by the mtime code generator.  DO NOT EDIT!
// Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.

option java_package = "cmc.card.basic.admin.service.dto";
option java_outer_classname = "CardToMemberRelationshipProto";

// 卡绑定信息
message	CardMemberBindInfo {
	// 卡号	
	optional string cardId = 1;
	// 会员编码	
	optional string memberCode = 2;
	// 创建时间	
	optional int64 createTime = 3;
	// 卡类型编码	
	optional string cardTypeCode = 4;
	// 卡类别	
	optional int32 cardSort = 5;
	// 卡形式	
	optional int32 cardForm = 6;
	// 卡状态	
	optional int32 cardState = 7;
	// 卡余额	
	optional int32 balance = 8;
	// 卡到期时间	
	optional int64 effectiveEnd = 9;

}

// 会员卡信息
message	MemberCardInfo {
	// 卡号	
	optional string cardNumber = 1;
	// 手机号	
	optional string phoneNumber = 2;
	// 会员编码	
	optional string memberCode = 3;
	// 卡形式	
	optional int32 cardForm = 4;
	// 卡类别	
	optional int32 cardSort = 5;
	// 卡余额(单位分)	
	optional int32 balance = 6;
	// 卡类型id	
	optional int64 cardTypeNo = 7;
	// 有效期截止日期	
	optional int64 expiryDate = 8;
	// 绑定时间	
	optional int64 bindTime = 9;
	// 卡密文密码	
	optional string enCardPwd = 10;

}

// 
message	QueryCardInfoRequest {
	// 卡号
  	required string cardNumber = 1;
	// 手机号
  	required string phoneNumber = 2;
	// 会员编号
  	required string memberCode = 3;
	// 页码
  	required int32 pageIdx = 4;
	// 总页数
  	required int32 pageSize = 5;

}

// 卡查询返回结果 
message	QueryCardInfoResponse {
	// 卡列表	
	repeated MemberCardInfo cardList = 1;
	// 总记录数	
	required int32 total = 2;

}

// 
message	QueryCardMemberBindRequest {
	// 创建时间
  	required int64 startTime = 1;
	// 结束时间
  	required int64 endTime = 2;
	// 页码
  	required int32 pageIdx = 3;
	// 总页数
  	required int32 pageSize = 4;
	// 数据库节点名
  	required string dbName = 5;

}

// 卡绑定关系查询返回结果 
message	QueryCardMemberBindResponse {
	// 卡绑定信息集合	
	repeated CardMemberBindInfo cardMemberBindInfoList = 1;

}

// 
message	CountCardMemberBindRequest {
	// 创建时间
  	required int64 startTime = 1;
	// 结束时间
  	required int64 endTime = 2;
	// 数据库节点名
  	required string dbName = 3;

}

// 卡绑定关系查询返回结果 
message	CountCardMemberBindResponse {
	// 总数	
	optional int64 count = 1;

}

// 
message	QueryMemberCodeRequest {
	// 卡号
  	required string cardNumber = 1;

}

// 卡查询返回结果 
message	QueryMemberCodeResponse {
	// 会员编码	
	optional string memberCode = 1;

}

// 
message	QueryMemberPhoneRequest {
	// 卡号
  	required string cardNumber = 1;

}

// 卡查询返回结果 
message	QueryMemberPhoneResponse {
	// 会员手机号	
	optional string memberPhone = 1;

}
