
// Generated by the mtime code generator.  DO NOT EDIT!
// Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.

option java_package = "cmc.card.basic.admin.service.dto";
option java_outer_classname = "UploadFileRecordProto";

// 
message	UploadFileRecord {
	// 	
	optional int64 id = 1;
	// 	
	optional int32 operatorId = 2;
	// 	
	optional int32 businessType = 3;
	// 	
	optional string fileId = 4;
	// 	
	optional int64 createTime = 5;

}

// 
message	SaveRequest {
	// 
  	required int32 operatorId = 1;
	// 
  	required int32 businessType = 2;
	// 
  	required string fileId = 3;

}

// 查询有变更卡的请求
message	DeleteRequest {
	// id
  	required int64 id = 1;

}

// 
message	GetRequest {
	// 操作人id
  	required int32 operatorId = 1;
	// 业务类型
  	required int32 businessType = 2;

}

//  
message	GetResponse {
	// 	
	optional UploadFileRecord updateFileRecord = 1;

}

// 
message	QueryRequest {
	// 文件id
  	required string fileId = 1;
	// 业务类型
  	required int32 businessType = 2;

}

//  
message	QueryResponse {
	// 	
	optional UploadFileRecord updateFileRecord = 1;

}
