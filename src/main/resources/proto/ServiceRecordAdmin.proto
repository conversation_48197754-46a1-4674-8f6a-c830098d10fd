
// Generated by the mtime code generator.  DO NOT EDIT!
// Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.

option java_package = "cmc.card.basic.admin.service.dto";
option java_outer_classname = "ServiceRecordAdminProto";

// 服务记录信息
message	ServiceRecord {
	// 主键	
	optional int64 id = 1;
	// 卡号	
	optional string cardId = 2;
	// 服务时间	
	optional int64 serviceTime = 3;
	// 支付方式	
	optional int32 payType = 4;
	// 服务类型	
	optional int32 serviceType = 5;
	// 支付号	
	optional string payNo = 6;
	// 支付金额	
	optional int32 payMoney = 7;
	// 授权卡卡号	
	optional string authorizedId = 8;
	// 操作员	
	optional string operator = 9;
	// 工本费	
	optional int32 cost = 10;
	// 影院内码	
	optional string cinemaInnerCode = 11;
	// 服务影院名称	
	optional string cinemaName = 12;
	// 新卡卡号	
	optional string newCardId = 13;
	// 服务前余额	
	optional int32 beforeServiceBalance = 14;
	// 服务后余额	
	optional int32 afterServiceBalance = 15;
	// 卡余额变化	
	optional int32 changeBalance = 16;
	// 支付方式名称	
	optional string payTypeName = 17;
	// 服务渠道	
	optional string serviceChannel = 18;
	// 营业员ID	
	optional string conductId = 19;
	// 操作员工作组ID	
	optional string opGroupId = 20;
	// 操作员工作站ID	
	optional string opStationId = 21;
	// 卡类型名称	
	optional string cardType = 22;
	// 卡类别名称	
	optional string cardSort = 23;
	// 延期费	
	optional int32 delayMoney = 24;
	// 有效期截止日期(到期日期)	
	optional int64 effectiveEnd = 25;
	// 发卡影院内码	
	optional string openCinemaInnerCode = 26;
	// 发卡影院名称	
	optional string openCinemaName = 27;
	// 卡服务关联的订单号	
	optional string orderId = 28;
	// 备注	
	optional string remarks = 29;
	// 用户名	
	optional string userName = 30;
	// 虚拟卡开卡订单号	
	optional string reOrderId = 31;
	// 操作员编号	
	optional int64 operatorId = 32;
	// 卡形式	
	optional string ifEntity = 33;
	// 服务类型名称	
	optional string serviceTypeName = 34;
	// 新卡类型名称	
	optional string newCardType = 35;
	// 旧卡卡号	
	optional string oldCardId = 36;
	// 卡类型编码	
	optional string cardTypeCode = 37;
	// 渠道编码	
	optional string channelCode = 38;
	// 服务前卡次数	
	optional int32 beforeServiceNum = 39;
	// 服务后卡次数	
	optional int32 afterServiceNum = 40;
	// 卡次数变化	
	optional int32 changeNum = 41;
	// 发票唯一标识	
	optional string invoiceId = 42;
	// 支付方式编码	
	optional string payTypeCode = 43;
	// 赠送金额	
	optional int32 presentEveryAmount = 44;
	// 赠送总额	
	optional int32 presentAmount = 45;

}

// 按条件查询服务记录请求
message	QueryListForTransferRequest {
	// 数据库分片名称
  	required string dbName = 1;
	// 卡类型编码
  	required string cardTypeCode = 2;
	// 卡类型名称
  	required string cardTypeName = 3;
	// 一次查询的记录数
  	required int32 querySize = 4;
	// 发卡影院内码
  	repeated string cinemaInnerCodeList = 5;

}

//  
message	QueryListForTransferResponse {
	// 服务记录列表	
	repeated ServiceRecord serviceRecordList = 1;

}

// 按条件更新服务记录
message	UpdateServiceRecordForTransferRequest {
	// 数据库分片名称
  	required string dbName = 1;
	// 新的卡类型编码
  	required string newCardTypeCode = 2;
	// 新的卡类型名称
  	required string newCardTypeName = 3;
	// 待更新的服务记录ID列表
  	repeated int64 idList = 4;

}

// 查询条件
message	QueryListByConditionRequest {
	// 卡号
  	required string cardId = 1;
	// 服务类型
  	repeated int32 serviceTypes = 2;
	// 开始时间
  	required int64 startTime = 3;
	// 结束时间
  	required int64 endTime = 4;

}

//  
message	QueryListByConditionResponse {
	// 服务记录列表	
	repeated ServiceRecord serviceRecordList = 1;

}

// 查询条件
message	QueryListByCardRequest {
	// 卡号
  	required string cardId = 1;
	// 服务类型
  	repeated int32 serviceTypes = 2;

}

//  
message	QueryListByCardResponse {
	// 服务记录列表	
	repeated ServiceRecord serviceRecordList = 1;

}

// 更新工本费入参
message	UpdateCostRequest {
	// id
  	required int64 id = 1;
	// 卡号
  	required string cardId = 2;
	// 工本费
  	required string cost = 3;
	// oa编码
  	required string oaCode = 4;
	// 操作人
  	required string operator = 5;

}

//  
message	UpdateCostResponse {
	// 原工本费	
	optional string originCost = 1;
	// 新修改的工本费	
	optional string newCost = 2;

}

// 修改渠道编码入参
message	UpdateChannelCodeRequest {
	// 卡号
  	required string cardId = 1;
	// 服务类型
  	required int32 serviceType = 2;
	// 渠道编码
  	required string channelCode = 3;
	// 备注
  	required string remark = 4;

}

// 查询条件
message	QueryPageByCardRequest {
	// 卡号
  	required string cardId = 1;
	// 页码
  	optional int32 pageIndex = 2;
	// 每页记录数
  	optional int32 pageSize = 3;

}

//  
message	QueryPageByCardResponse {
	// 服务记录列表	
	repeated ServiceRecord serviceRecordList = 1;
	// 总记录数	
	required int32 total = 2;

}

// 修改支付方式入参
message	UpdatePayTypeRequest {
	// 卡号
  	required string cardId = 1;
	// 服务类型
  	required int32 serviceType = 2;
	// 支付方式编码
  	required string payTypeCode = 3;
	// 支付方式名称
  	required string payTypeName = 4;
	// 备注
  	optional string remark = 5;

}
