
// Generated by the mtime code generator.  DO NOT EDIT!
// Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.

option java_package = "cmc.card.basic.admin.service.dto";
option java_outer_classname = "CardQueryAdminProto";

// 
message	NoActiveCardSellOrder {
	// 批量发卡销售单	
	required string sellOrderId = 1;
	// 未激活卡数量	
	required int32 noActiveCardNum = 2;
	// 附加前缀销售单	
	optional string sellOrderIdAttachPrefix = 3;

}

// 未激活卡信息
message	NoActiveCardInfo {
	// 卡号	
	required string cardId = 1;
	// 卡类型名	
	required string cardTypeName = 2;
	// 单张卡负充金额	
	required int32 cardBalance = 3;
	// 是否选中	
	required int32 isSelect = 4;

}

// 销售单
message	SellOrder {
	// 销售单id	
	optional string sellOrderId = 1;
	// 未激活卡总数	
	optional int32 noActiveCardNum = 2;
	// 未激活卡总价格	
	optional int32 noActiveCardTotalPrice = 3;

}

// 卡查询参数
message	QueryParam {
	// 卡ID	
	optional string cardId = 1;
	// 手机号	
	optional string phoneNo = 2;
	// 用户名	
	optional string userName = 3;
	// 卡状态	
	optional int32 cardStateValue = 4;
	// 是否是实体卡	
	optional int32 ifEntity = 5;
	// 证件类型值	
	optional int32 certificatesType = 6;
	// 证件号	
	optional string certificatesNo = 7;
	// 是否是刮刮卡	
	optional string yesOrNoGuaGuaCard = 8;
	// QQ号	
	optional string qq = 9;
	// 影院内码	
	optional string cinemaInnerCode = 10;
	// 区域ID	
	optional string areaId = 11;

}

// 简单卡查询信息对象
message	SimpleCardInfo {
	// 卡ID	
	optional string cardId = 1;
	// 卡类型编码	
	optional string cardTypeCode = 2;
	// 卡类型名称	
	optional string cardTypeName = 3;
	// 卡状态	
	optional int32 cardState = 4;

}

// 卡查询信息对象
message	QueryCardInfo {
	// 卡ID	
	optional string cardId = 1;
	// 手机号	
	optional string phoneNo = 2;
	// 用户名	
	optional string userName = 3;
	// 密码	
	optional string passWord = 4;
	// 区域ID	
	optional string areaId = 6;
	// 影院内码	
	optional string cinemaInnerCode = 7;
	// 影院名称	
	optional string cinemaName = 8;
	// 卡状态	
	optional int32 cardState = 9;
	// 卡类别	
	optional int32 cardSort = 11;
	// 是否是实体卡	
	optional int32 ifEntity = 12;
	// 卡余额	
	optional int32 balance = 13;
	// 卡余次	
	optional int32 balNum = 14;
	// 有效期截止日期	
	optional int64 effectiveEnd = 15;
	// 卡类型id	
	optional int64 cardTypeNo = 16;
	// 卡类型名称	
	optional string cardTypeName = 18;
	// 卡类型状态	
	optional string cardTypeState = 19;
	// 证件类型值	
	optional int32 certificatesType = 20;
	// 证件类型名称	
	optional string certificatesTypeName = 21;
	// 证件号	
	optional string certificatesNo = 22;
	// 邮箱	
	optional string email = 23;
	// QQ号	
	optional string qq = 24;
	// 是否是刮刮卡	
	optional string isScratchCard = 25;
	// 邮编	
	optional string postCode = 26;

}

// 卡查询信息对象
message	CardBaseInfo {
	// 卡ID	
	optional string cardId = 1;
	// 手机号	
	optional string phoneNo = 2;
	// 用户名	
	optional string userName = 3;
	// 卡类型	
	optional int32 cardType = 4;
	// 是否是实体卡	
	optional int32 ifEntity = 5;
	// 区域ID	
	optional string areaId = 6;
	// 影院内码	
	optional string cinemaInnerCode = 7;
	// 卡状态名称	
	optional int32 cardState = 8;
	// 有效期截止日期	
	optional int64 effectiveEnd = 9;
	// 卡类型id	
	optional int64 cardTypeNo = 10;
	// 卡类别	
	optional int32 cardSort = 11;
	// 卡余额	
	optional int32 balance = 12;
	// 密码	
	optional string passWord = 13;
	// 明文密码	
	optional string passWordClear = 14;
	// 卡面编码	
	optional string coverCode = 15;

}

// 
message	QueryNoActiveAndHaveBalanceCardsRequest {
	// 批量发卡销售单
  	required string sellOrderId = 1;

}

//  
message	QueryNoActiveAndHaveBalanceCardsResponse {
	// 订单汇总信息	
	required SellOrder order = 1;
	// 未激活卡列表	
	repeated NoActiveCardInfo cardInfoList = 2;

}

// 
message	QueryNoActiveHaveBalanceCardAndSellOrderIdRequest {
	// 批量发卡销售单
  	required string cinemaInnerCode = 1;

}

//  
message	QueryNoActiveHaveBalanceCardAndSellOrderIdResponse {
	// 未激活卡销售单	
	repeated NoActiveCardSellOrder noActiveCardSellOrder = 1;

}

// 
message	GetNotNoActiveCardListRequest {
	// 根据卡id查询
  	repeated string cardId = 1;

}

//  
message	GetNotNoActiveCardListResponse {
	// 不是未激活的卡号	
	repeated string cardId = 1;

}

// 
message	CardInfoQueryRequest {
	// 卡查询参数
  	optional QueryParam queryParam = 1;
	// 页码
  	required int32 pageIndex = 2;
	// 每页记录数
  	required int32 pageSize = 3;
	// 排序条件
  	optional int32 sortCriteria = 4;
	// 正序倒序
  	optional int32 positiveAndNegative = 5;
	// 卡批量处理的卡号list
  	repeated string cardIds = 6;

}

// 卡查询返回结果 
message	CardInfoQueryResponse {
	// 返回卡信息列表	
	repeated QueryCardInfo queryCardInfo = 1;
	// 总记录数	
	required int32 total = 2;

}

// 
message	GetCardCoverNameByCardIdRequest {
	// 卡号
  	required string cardId = 1;

}

// 返回结果 
message	GetCardCoverNameByCardIdResponse {
	// 卡号	
	required string cardId = 1;
	// 卡面编码	
	optional string coverCode = 2;
	// 卡面名称	
	optional string coverName = 3;

}

// 
message	CardInfoQueryByDBRequest {
	// 卡号list
  	repeated string cardIds = 1;

}

// 卡查询返回结果 
message	CardInfoQueryByDBResponse {
	// 返回卡信息列表	
	repeated SimpleCardInfo simpleCardInfo = 1;

}
