
// Generated by the mtime code generator.  DO NOT EDIT!
// Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.

option java_package = "cmc.card.basic.admin.service.dto";
option java_outer_classname = "CardDataProto";

// 卡附加信息
message	CardInformation {
	// 卡常用信息	
	required CardInfo cardInfo = 1;
	// 卡扩展信息	
	optional CardDateInfo cardDateInfo = 2;

}

// 卡附加信息
message	CardDateInfo {
	// 卡ID	
	optional string cardId = 1;
	// 手机号码	
	optional string phoneNo = 2;
	// 卡密码	
	optional string passWord = 3;
	// 卡密码	
	optional int32 certificatesType = 4;
	// 证件号码	
	optional string certificatesNo = 5;
	// 性别	
	optional int32 sex = 6;
	// Emal地址	
	optional string email = 7;
	// 生日	
	optional int64 birthday = 8;
	// QQ号码	
	optional string qq = 9;
	// 联系地址	
	optional string address = 10;
	// 最后更新时间	
	optional int64 lastUpdateTime = 11;
	// 激活时间	
	optional int64 activationTime = 12;
	// 出库单ID	
	optional string issueOrderId = 13;
	// 是否是刮刮卡	
	optional int32 isScratchCard = 14;
	// 最后更新生日的时间	
	optional int64 lastUpdateBirthday = 15;
	// 退卡备注	
	optional string refundCardRemark = 16;
	// 卡序列号	
	optional int64 serialNumber = 17;
	// 是否是刮刮卡	
	optional int32 isSectorCard = 18;
	// 密文卡号	
	optional string enCardNumber = 19;
	// 邮编	
	optional string postCode = 20;

}

// 卡常用信息
message	CardInfo {
	// 卡ID	
	optional string cardId = 1;
	// 手机号	
	optional string phoneNo = 2;
	// 密码	
	optional string passWord = 3;
	// 用户名	
	optional string userName = 4;
	// 卡类型	
	optional int32 cardType = 5;
	// 卡类别	
	optional int32 cardSort = 6;
	// 是否是实体卡	
	optional int32 ifEntity = 7;
	// 区域ID	
	optional string areaId = 8;
	// 影院内码	
	optional string cinemaInnerCode = 9;
	// 卡余额	
	optional int32 balance = 10;
	// 是否允许网购	
	optional int32 canuseOnLine = 11;
	// 卡状态	
	optional int32 cardState = 12;
	// 有效期截止日期	
	optional int64 effectiveEnd = 13;
	// 卡类型编号	
	optional int64 cardTypeNo = 14;
	// 消费次数	
	optional int32 spendTimes = 15;
	// 影院名称	
	optional string cinemaName = 16;
	// 是否是刮刮卡初始密码	
	optional string useOriginPwd = 17;
	// 销售单id	
	optional string sellOrderId = 18;
	// 发卡时间	
	optional int64 issueTime = 19;
	// 最后更新时间	
	optional int64 lastTime = 20;
	// 卡的明文卡号	
	optional string passWordClear = 21;

}

// 卡所有的信息
message	CardAllInfo {
	// 卡ID	
	optional string cardId = 1;
	// 手机号	
	optional string phoneNo = 2;
	// 密码	
	optional string passWord = 3;
	// 用户名	
	optional string userName = 4;
	// 卡类型	
	optional int32 cardType = 5;
	// 卡类别	
	optional int32 cardSort = 6;
	// 是否是实体卡	
	optional int32 ifEntity = 7;
	// 区域ID	
	optional string areaId = 8;
	// 影院内码	
	optional string cinemaInnerCode = 9;
	// 卡余额	
	optional string balance = 10;
	// 是否允许网购	
	optional int32 canuseOnLine = 11;
	// 卡状态	
	optional int32 cardState = 12;
	// 有效期截止日期	
	optional int64 effectiveEnd = 13;
	// 卡类型编号	
	optional int64 cardTypeNo = 14;
	// 消费次数	
	optional int32 spendTimes = 15;
	// 影院名称	
	optional string cinemaName = 16;
	// 是否是刮刮卡初始密码	
	optional string useOriginPwd = 17;
	// 销售单id	
	optional string sellOrderId = 18;
	// 发卡时间	
	optional int64 issueTime = 19;
	// 最后更新时间	
	optional int64 lastTime = 20;
	// 卡的明文卡号	
	optional string passWordClear = 21;
	// 卡序列号	
	optional string serialNumber = 22;
	// 性别	
	optional string sex = 23;
	// Emal地址	
	optional string email = 24;
	// 生日	
	optional int64 birthday = 25;
	// 邮编	
	optional string postCode = 26;

}

// 保存卡信息的请求数据
message	SaveCardInfoRequest {
	// 卡记录列表
  	repeated CardInformation cardInformations = 1;

}

//  
message	SaveCardInfoResponse {
	// 操作结果	
	optional bool result = 1;
	// 异常信息	
	optional string msg = 2;

}

// 保存卡信息的请求数据
message	SaveCardInfoToEsRequest {
	// 卡记录列表
  	repeated CardInfo cardInfos = 1;

}

//  
message	SaveCardInfoToEsResponse {
	// 操作结果	
	optional bool result = 1;
	// 异常信息	
	optional string msg = 2;

}

// 保存卡信息的请求数据
message	QueryCardDataInfoByPageInfoRequest {
	// 分页索引
  	optional int32 pageIndex = 1;
	// 区域id
  	optional string areaId = 2;
	// 影院id
  	optional string cinemaId = 3;
	// 卡类型编码
  	optional string cardTypeCode = 4;
	// 前缀号码
  	optional string extracardid = 5;
	// 页数
  	optional int32 pageSize = 6;

}

//  
message	QueryCardDataInfoByPageInfoResponse {
	// 操作结果	
	repeated CardAllInfo cardInfo = 1;
	// 	
	optional int32 totalCount = 2;

}

// 保存卡信息的请求数据
message	ExportCardDataInfoRequest {
	// 分页索引
  	optional int32 pageIndex = 1;
	// 区域id
  	optional string areaId = 2;
	// 影院id
  	optional string cinemaId = 3;
	// 卡类型编码
  	optional string cardTypeCode = 4;
	// 前缀号码
  	optional string extracardid = 5;
	// 分页id
  	optional string scrollId = 6;
	// 页数
  	optional int32 pageSize = 7;

}

//  
message	ExportCardDataInfoResponse {
	// 操作结果	
	repeated CardAllInfo cardInfo = 1;
	// 	
	optional int32 totalCount = 2;
	// 分页id	
	optional string scrollId = 3;

}
