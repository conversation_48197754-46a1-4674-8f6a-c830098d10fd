
// Generated by the mtime code generator.  DO NOT EDIT!
// Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.

option java_package = "cmc.card.basic.admin.service.dto";
option java_outer_classname = "CardInfoAdminProto";

// 卡常用信息
message	CardInfo {
	// 卡ID	
	optional string cardId = 1;
	// 手机号	
	optional string phoneNo = 2;
	// 密码	
	optional string passWord = 3;
	// 卡的明文卡号	
	optional string passWordClear = 4;
	// 用户名	
	optional string userName = 5;
	// 卡类型	
	optional int32 cardType = 6;
	// 卡类别	
	optional int32 cardSort = 7;
	// 是否是实体卡	
	optional int32 ifEntity = 8;
	// 区域ID	
	optional string areaId = 9;
	// 影院内码	
	optional string cinemaInnerCode = 10;
	// 卡余额	
	optional int32 balance = 11;
	// 是否允许网购	
	optional int32 canuseOnLine = 12;
	// 卡状态	
	optional int32 cardState = 13;
	// 有效期截止日期	
	optional int64 effectiveEnd = 14;
	// 卡类型ID	
	optional int64 cardTypeNo = 15;
	// 消费次数	
	optional int32 spendTimes = 16;
	// 影院名称	
	optional string cinemaName = 17;
	// 是否是刮刮卡初始密码	
	optional string useOriginPwd = 18;
	// 销售单id	
	optional string sellOrderId = 19;
	// 预冻总金额	
	optional int32 txTransBal = 20;
	// 发卡时间	
	optional int64 issueTime = 21;
	// 最后更新时间	
	optional int64 lastTime = 22;
	// 版本号	
	optional int32 version = 23;

}

// 按条件查询服务记录请求数据
message	QueryListForTransferRequest {
	// 数据库分片名称
  	required string dbName = 1;
	// 卡类型ID
  	required int64 cardTypeNo = 2;
	// 一次查询的记录数
  	required int32 querySize = 3;
	// 区域ID
  	required string areaId = 4;
	// 发卡影院内码
  	repeated string cinemaInnerCodeList = 5;

}

//  
message	QueryListForTransferResponse {
	// 卡信息列表	
	repeated CardInfo cardInfoList = 1;

}

// 更新卡信息请求数据
message	UpdateCardInfoForTransferRequest {
	// 数据库分片名称
  	required string dbName = 1;
	// 新的区域ID
  	required string newAreaId = 2;
	// 新的卡类型ID
  	required int64 newCardTypeNo = 3;
	// 待更新的卡类型
  	repeated string cardIdList = 4;

}

// 获取指定条件下的卡数量请求数据
message	GetCardTotalCountForTransferRequest {
	// 数据库分片名称
  	required string dbName = 1;
	// 卡类型ID
  	required int64 cardTypeNo = 2;
	// 区域ID
  	required string areaId = 3;
	// 发卡影院内码
  	repeated string cinemaInnerCodeList = 4;

}

//  
message	GetCardTotalCountForTransferResponse {
	// 返回的卡数量	
	required int64 count = 1;

}

// 请求数据
message	QueryChainCardListForTransferRequest {
	// 数据库分片名称
  	required string dbName = 1;
	// 一次查询的记录数
  	required int32 querySize = 2;
	// 区域ID
  	required string areaId = 3;
	// 卡类型ID
  	required int64 cardTypeNo = 4;
	// 发卡影院内码
  	repeated string cinemaInnerCodeList = 5;

}

//  
message	QueryChainCardListForTransferResponse {
	// 卡信息列表	
	repeated CardInfo cardInfoList = 1;

}

// 更新卡信息请求数据
message	UpdateChainCardInfoForTransferRequest {
	// 数据库分片名称
  	required string dbName = 1;
	// 新的区域ID
  	required string newAreaId = 2;
	// 待更新的院线卡ID列表
  	repeated string cardIdList = 3;

}

// 请求数据
message	QueryChainCardTypeNoForTransferRequest {
	// 数据库分片名称
  	required string dbName = 1;
	// 区域ID
  	required string areaId = 2;
	// 发卡影院内码
  	repeated string cinemaInnerCodeList = 3;

}

//  
message	QueryChainCardTypeNoForTransferResponse {
	// 院线卡类型ID列表	
	repeated int64 chainCardNoList = 1;

}

// 获取应收取年费卡列表请求参数
message	FindCardOfAnnualFeeRequest {
	// 卡类型ID
  	required int64 cardTypeNo = 1;
	// 区域ID
  	optional string areaId = 2;
	// 发卡时间
  	required int64 issueTime = 3;
	// 分页信息
  	required PageInfo pageInfo = 4;

}

// 获取应收取年费卡列表请求响应 
message	FindCardOfAnnualFeeResponse {
	// 卡列表	
	repeated CardInfo cardInfoList = 1;

}

// 按条件查询服务记录请求数据
message	FindCardByCardTypeIdRequest {
	// 卡类型ID
  	required int64 cardTypeNo = 1;
	// 卡状态
  	optional int32 cardState = 2;
	// 区域ID
  	optional string areaId = 3;
	// 分页信息
  	optional PageInfo pageInfo = 4;

}

//  
message	FindCardByCardTypeIdResponse {
	// 卡信息列表	
	repeated CardInfo cardInfoList = 1;

}

// 获取收取年费之前的余额请求参数
message	getCardBalanceBeforeAnnualFeeRequest {
	// 卡号
  	required string cardId = 1;
	// 扣减年度
  	required string deductYear = 2;

}

// 获取收取年费之前的余额请求响应 
message	getCardBalanceBeforeAnnualFeeResponse {
	// 卡收取年费之前的余额	
	required int32 balance = 1;

}

// 按条件查询服务记录请求数据
message	FindCardInfoListRequest {
	// 发卡时间
  	optional int64 issueTime = 1;
	// 卡状态
  	optional int32 cardState = 2;
	// 卡类别
  	optional int32 cardSort = 3;
	// 分页信息
  	optional PageInfo pageInfo = 4;

}

//  
message	FindCardInfoListResponse {
	// 卡信息列表	
	repeated CardInfo cardInfoList = 1;

}
