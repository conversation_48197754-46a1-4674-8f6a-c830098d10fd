
// Generated by the mtime code generator.  DO NOT EDIT!
// Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.

option java_package = "cmc.card.basic.admin.service.dto";
option java_outer_classname = "AnnualFeeNotificationProto";

// 年费通知配置
message	NotificationConfig {
	// 主键	
	optional int64 id = 1;
	// 短信通知开关	
	optional int32 smsSwitch = 2;
	// 短信模板	
	optional string smsTemplate = 3;
	// push通知开关	
	optional int32 pushSwitch = 4;
	// push模板	
	optional string pushTemplate = 5;
	// 跳转页面	
	optional int32 jumpPage = 6;
	// 奖品开关	
	optional int32 prizeSwitch = 7;
	// 奖池ID	
	optional int32 prizeGroupId = 8;
	// 活动ID	
	optional int32 activityId = 9;
	// 创建时间	
	optional int64 createTime = 10;
	// 修改时间	
	optional int64 updateTime = 11;
	// 操作员ID	
	optional int32 userId = 12;
	// 操作员名称	
	optional string userName = 13;

}

//  
message	QueryNotificationConfigResponse {
	// 通知配置数据	
	optional NotificationConfig notificationConfig = 1;

}

// 
message	saveNotificationConfigRequest {
	// 通知配置
  	required NotificationConfig notificationConfig = 1;

}

//  
message	saveNotificationConfigResponse {
	// 	
	required bool knowledge = 1;

}
