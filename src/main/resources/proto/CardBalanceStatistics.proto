
// Generated by the mtime code generator.  DO NOT EDIT!
// Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.

option java_package = "cmc.card.basic.admin.service.dto";
option java_outer_classname = "CardBalanceStatisticsProto";

// 卡余额统计结果
message	CardBalanceStatisticalResult {
	// 区域ID	
	optional string areaId = 1;
	// 影院内码	
	optional string cinemaInnerCode = 2;
	// 影院名称	
	optional string cinemaName = 3;
	// 余额	
	optional int64 balance = 4;
	// 统计数量	
	optional int64 totalCount = 5;
	// 卡类别	
	optional int32 cardSort = 6;
	// 赠送余额	
	optional int64 presentBalance = 7;

}

// 卡余额历史
message	CardBalanceHistory {
	// ID	
	required int32 id = 1;
	// 区域ID	
	required string areaId = 2;
	// 影院内码	
	required string cinemaInnerCode = 3;
	// 影院名称	
	optional string cinemaName = 4;
	// 余额	
	required int64 balance = 5;
	// 统计数量	
	required int64 totalCount = 6;
	// 统计日期	
	required int64 statisticalDate = 7;
	// 赠送余额	
	optional int64 presentBalance = 8;

}

// 
message	GetCardBalanceHistoryListRequest {
	// 区域ID
  	optional string areaId = 1;
	// 影院内码
  	optional string cinemaInnerCode = 2;
	// 统计日期
  	required int64 statisticalDate = 3;
	// 卡类别
  	optional string cardSort = 4;

}

//  
message	GetCardBalanceHistoryListResponse {
	// 卡余额列表	
	repeated CardBalanceStatisticalResult cardBalanceStatisticalResults = 1;

}

// 
message	GetCardBalanceListRequest {
	// 区域ID
  	optional string areaId = 1;
	// 影院内码
  	optional string cinemaInnerCode = 2;
	// 卡类别
  	optional string cardSort = 3;

}

//  
message	GetCardBalanceListResponse {
	// 卡余额列表	
	repeated CardBalanceStatisticalResult cardBalanceStatisticalResults = 1;

}
