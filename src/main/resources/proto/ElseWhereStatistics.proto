
// Generated by the mtime code generator.  DO NOT EDIT!
// Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.

option java_package = "cmc.card.basic.admin.service.dto";
option java_outer_classname = "ElseWhereStatisticsProto";

// 异地卡统计视图类
message	CardElseWhereStatisticsView {
	// 应付充值	
	required string paymentRecharge = 1;
	// 应付消费	
	required string paymentConsume = 2;
	// 应付合计	
	required string paymentSum = 3;
	// 应收充值	
	required string receiveRecharge = 4;
	// 应收消费	
	required string receiveConsume = 5;
	// 应收合计	
	required string receiveSum = 6;
	// 异地消费法人名称	
	optional string elseLegalPersonName = 7;
	// 异地消费影院名称	
	optional string elseCinemaName = 8;
	// 开卡影城人名称	
	optional string legalPersonName = 9;

}

// 用于select组件的key value组件的传值
message	KeyAndValue {
	// 键	
	optional string key = 1;
	// 值	
	optional string value = 2;

}

// 异地卡统计视图类
message	CardElseWhereStatisticsViewDto {
	// 应付充值	
	required int64 paymentRecharge = 1;
	// 应付消费	
	required int64 paymentConsume = 2;
	// 应付合计	
	required int64 paymentSum = 3;
	// 应收充值	
	required int64 receiveRecharge = 4;
	// 应收消费	
	required int64 receiveConsume = 5;
	// 应收合计	
	required int64 receiveSum = 6;
	// 法人名称	
	optional string legalPersonName = 7;
	// 影院名称	
	optional string cinemaName = 8;
	// 法人ID	
	optional string legalPersonId = 9;
	// 影院id	
	optional string cinemaId = 10;
	// 区域id	
	optional string areaId = 11;
	// 统计时间	
	optional int64 statisticalTime = 12;
	// 记录创建时间	
	optional int64 createTime = 13;
	// 关联的影院id	
	optional string elseCinemaId = 14;
	// 关联影院名称	
	optional string elseCinemaName = 15;
	// 关联法人id	
	optional string elseLegalPersonId = 16;
	// 关联法人名称	
	optional string elseLegalPersonName = 17;

}

//  
message	FindAllAreaInStatisticsResponse {
	// 区域列表	
	repeated KeyAndValue areaList = 1;

}

// 
message	FindCinemasInStatisticsRequest {
	// 区域id
  	optional string areaId = 1;

}

//  
message	FindCinemasInStatisticsResponse {
	// 影院列表	
	repeated KeyAndValue cinemaList = 1;

}

// 
message	FindStatisticsRequest {
	// 明细分类
  	required int32 detailType = 1;
	// 区域id
  	optional string areaId = 2;
	// 影院id
  	optional string cinemaId = 3;
	// 开始时间
  	optional int64 startTime = 4;
	// 结束时间
  	optional int64 endTime = 5;
	// 法人名称
  	optional string legalPersonName = 6;

}

//  
message	FindStatisticsResponse {
	// 统计报表列表	
	repeated CardElseWhereStatisticsView statisticsList = 1;

}

// 
message	DeleteStatisticsDataRequest {
	// 开始时间
  	optional int64 startTime = 1;
	// 结束时间
  	optional int64 endTime = 2;

}

//  
message	DeleteStatisticsDataResponse {
	// 返回结果数目	
	optional int32 result = 1;

}

// 
message	FindStatisticsSummaryRequest {
	// 开始时间
  	optional int64 startTime = 1;
	// 结束时间
  	optional int64 endTime = 2;
	// 法人名称
  	optional string legalPersonName = 3;

}

//  
message	FindStatisticsSummaryResponse {
	// 统计报表列表	
	repeated CardElseWhereStatisticsView statisticsList = 1;

}

// 
message	SaveStatisticsSummaryRequest {
	// 统计报表信息
  	repeated CardElseWhereStatisticsViewDto statisticsList = 1;

}

//  
message	SaveStatisticsSummaryResponse {
	// 返回结果数目	
	optional int32 result = 1;

}

// 
message	QueryMigrateAreaCinemaInfoRequest {
	// 
  	repeated string cinemaInnerCode = 1;
	// 
  	require string areaId = 2;

}

//  
message	QueryMigrateAreaCinemaInfoResponse {
	// 	
	repeated int64 result = 1;

}

// 
message	UpdateMigrateCinemaAreaRequest {
	// 
  	require int64 id = 1;
	// 
  	require string areaId = 2;

}

// 
message	IsMayRollBackRequest {
	// uuid
  	required string Uuid = 1;
	// version
  	required int32 Version = 2;

}

//  
message	IsMayRollBackResponse {
	// 返回是否成功,成功返回true,否则返回false	
	required bool success = 1;

}
