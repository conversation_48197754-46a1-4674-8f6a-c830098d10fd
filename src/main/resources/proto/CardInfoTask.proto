
// Generated by the mtime code generator.  DO NOT EDIT!
// Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.

option java_package = "cmc.card.basic.admin.service.dto";
option java_outer_classname = "CardInfoTaskProto";

// 卡余额历史报表请求数据
message	CardBalanceHistoryRequest {
	// 时间
  	optional string date = 1;

}

// 卡余额历史报表请求数据
message	CardSpendTimesTaskRequest {
	// 数据库分片名称
  	optional string dbName = 1;
	// 影院内码
  	repeated string cinemaInnerCodes = 2;

}

// 请求数据
message	FixCardBalanceAndConsumeInfoRequest {
	// 起始时间
  	optional string startDateTime = 1;
	// 卡号列表
  	repeated string cardIds = 2;
	// 影院内码列表
  	repeated string cinemaInnerCodes = 3;

}

// 请求数据
message	FixCardStateRequest {
	// 数据库分片名称
  	required string dbName = 1;
	// 影院内码
  	repeated string cinemaInnerCodes = 2;
	// 是否全部卡(false只更新状态为1,3,12的卡)
  	required bool isAll = 3;

}

// 请求数据
message	ExportCorpseCardRequest {
	// 数据库分片名称
  	required string dbName = 1;

}

// 请求数据
message	FixCardCinemaRequest {
	// 数据库分片名称
  	required string dbName = 1;
	// 原始影院内码
  	required string originalCinemaInnerCode = 2;
	// 目标影院内码
  	required string targetCinemaInnerCode = 3;

}

// 请求数据
message	RepairIssuedAndUnsoldRequest {
	// 卡号
  	optional string cardId = 1;

}
