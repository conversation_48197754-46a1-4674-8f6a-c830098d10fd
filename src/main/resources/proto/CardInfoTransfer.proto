
// Generated by the mtime code generator.  DO NOT EDIT!
// Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.

option java_package = "cmc.card.basic.admin.service.dto";
option java_outer_classname = "CardInfoTransferProto";

// 卡类型卡数量信息
message	CardCount {
	// 卡类型ID	
	required int64 cardTypeNo = 1;
	// 迁移前的卡数量	
	optional int64 cardCountBefore = 2;
	// 未迁移的卡数量	
	optional int64 cardCountToTransfer = 3;
	// 迁移完成的卡数量	
	optional int64 cardCountFinished = 4;

}

// 卡类型卡数量信息
message	CardTypeInfo {
	// 卡类型ID	
	required int64 cardTypeNo = 1;
	// 卡类型编码	
	required string cardTypeCode = 2;
	// 卡类型名称	
	required string cardTypeName = 3;
	// 卡类别名称	
	required string cardSortName = 4;
	// 迁移前的卡数量	
	optional int64 cardCountBefore = 5;
	// 未迁移的卡数量	
	optional int64 cardCountToTransfer = 6;
	// 迁移完成的卡数量	
	optional int64 cardCountFinished = 7;

}

// 按条件查询迁移的卡类型卡数量列表
message	QueryCardCountListRequest {
	// 迁移key
  	optional string transferKey = 1;
	// 版本号
  	optional int32 vesion = 2;
	// 迁移状态
  	required int32 migrationState = 3;
	// 卡类型ID
  	repeated int64 cardTypeNoList = 4;
	// 原区域ID
  	optional string sourceRegion = 5;
	// 影院内码
  	optional string cinemaInnerCode = 6;

}

// 返回的卡类型卡数量列表 
message	QueryCardCountListResponse {
	// 返回卡类型列表	
	repeated CardCount cardCountList = 1;

}

// 按条件查询迁移的卡类型卡数量列表
message	QueryCardTypeCountListRequest {
	// 迁移key
  	optional string transferKey = 1;
	// 版本号
  	optional int32 version = 2;
	// 迁移状态
  	optional int32 migrationState = 3;
	// 原区域ID
  	optional string sourceRegion = 4;
	// 影院内码
  	optional string cinemaInnerCode = 5;

}

// 返回的卡类型卡数量列表 
message	QueryCardTypeCountListResponse {
	// 返回卡类型卡数量列表	
	repeated CardTypeInfo cardTypeInfoList = 1;

}

// 查询卡信息是否可以回滚请求参数
message	CheckCardInfoRollbackRequest {
	// 迁移key
  	required string transferKey = 1;
	// 版本号
  	required int32 version = 2;
	// 影院编码列表
  	repeated string cinemaCodes = 3;

}

// 返回结果 
message	CheckCardInfoRollbackResponse {
	// 是否可以迁移	
	required bool result = 1;

}
