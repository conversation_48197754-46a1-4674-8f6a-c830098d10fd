
// Generated by the mtime code generator.  DO NOT EDIT!
// Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.

option java_package = "cmc.card.basic.admin.service.dto";
option java_outer_classname = "CardProto";

// 服务记录(不包含服务前余额,服务后余额,卡余额变化和服务卡号)
message	BalanceServiceRecord {
	// 服务流水号	
	optional int64 id = 1;
	// 卡号	
	optional string cardId = 2;
	// 服务时间	
	optional int64 serviceTime = 3;
	// 支付方式	
	optional int32 PayType = 4;
	// 服务类型	
	optional int32 serviceType = 5;
	// 支付号	
	optional string PayNo = 6;
	// 支付金额	
	optional int32 PayMoney = 7;
	// 授权卡卡号	
	optional string authorizedId = 8;
	// 操作员id	
	optional int32 OperatorId = 9;
	// 操作员姓名	
	optional string Operator = 10;
	// 工本费	
	optional int32 cost = 11;
	// 影院内码	
	optional string cinemaInnerCode = 12;
	// 影院名称	
	optional string cinemaName = 13;
	// 新卡卡号	
	optional string newCardId = 14;
	// 服务前金额	
	optional int32 beforeServiceBalance = 15;
	// 服务后金额	
	optional int32 afterServiceBalance = 16;
	// 余额变化	
	optional int32 changeBalance = 17;
	// 支付方式名称	
	optional string PayTypeName = 18;
	// 服务渠道	
	optional string serviceChannel = 19;
	// 营业员ID	
	optional string conductorId = 20;
	// 营业员groupId	
	optional string opGroupId = 21;
	// 营业员工作站ID	
	optional string opStationId = 22;
	// 卡类型	
	optional string cardType = 23;
	// 卡储值类型	
	optional int32 cardSort = 24;
	// 延期费用	
	optional int32 delayMoney = 25;
	// 有效期	
	optional int64 effectiveEnd = 26;
	// 影院内码	
	optional string openCinemaInnerCode = 27;
	// 影院名称	
	optional string openCinemaName = 28;
	// 订单号	
	optional string oldCardId = 29;
	// 备注	
	optional string remarks = 30;
	// 用户名	
	optional string userName = 31;
	// 虚拟卡开卡订单号	
	optional string reOrderId = 32;
	// 卡形式实体卡或者虚拟卡	
	optional string ifEntity = 33;
	// 服务类型名称	
	optional string serviceTypeName = 34;
	// 新卡卡类型编号	
	optional string newCardType = 35;
	// 卡服务关联的订单号	
	optional string orderId = 36;

}

// 
message	QueryChangeBalanceCardServiceRequest {
	// 开始时间
  	optional int64 startDate = 1;
	// 开始时间
  	optional int64 endDate = 2;
	// 页数
  	required int32 pageIndex = 3;
	// 每页的数量
  	required int32 pageNumber = 4;
	// 数据库分片信息
  	required string dbName = 5;

}

//  
message	QueryChangeBalanceCardServiceResponse {
	// 服务记录	
	repeated BalanceServiceRecord ServiceRecord = 1;

}

// 
message	UpdateChangeBalanceCardServiceRequest {
	// 服务记录
  	required BalanceServiceRecord ServiceRecord = 1;
	// 数据库分片信息
  	required string dbName = 2;

}

//  
message	UpdateChangeBalanceCardServiceResponse {
	// 更新的行数	
	required int32 result = 1;

}

// 
message	QueryChangeBalanceCountCardServiceRequest {
	// 开始时间
  	optional int64 startDate = 1;
	// 开始时间
  	optional int64 endDate = 2;
	// 数据库分片信息
  	required string dbName = 5;

}

//  
message	QueryChangeBalanceCountCardServiceResponse {
	// 服务记录数量	
	required int64 result = 1;

}
