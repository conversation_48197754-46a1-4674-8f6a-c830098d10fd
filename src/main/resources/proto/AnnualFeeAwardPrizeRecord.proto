
// Generated by the mtime code generator.  DO NOT EDIT!
// Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.

option java_package = "cmc.card.basic.admin.service.dto";
option java_outer_classname = "AnnualFeeAwardPrizeRecordProto";

// 发奖记录
message	PrizeRecord {
	// 主键	
	optional int64 id = 1;
	// 卡号	
	optional string cardId = 2;
	// 扣减年度	
	optional string deductYear = 3;
	// 手机号	
	optional string phoneNo = 4;
	// 操作员id	
	optional int32 userId = 5;
	// 奖池ID	
	optional int32 prizeGroupId = 6;
	// 奖品信息	
	optional string prizeInfo = 7;
	// 状态	
	optional int32 status = 8;
	// 扣年费时间	
	optional int64 deductFeeTime = 9;
	// 发奖时间	
	optional int64 awardPrizeTime = 10;
	// 会员编码	
	optional string memberCode = 11;
	// 发放流水号	
	optional string awardPrizeSerialNo = 12;
	// 创建时间	
	optional int64 createTime = 13;
	// 修改时间	
	optional int64 updateTime = 14;

}

// 
message	QueryPrizeRecordPageRequest {
	// 扣年费开始时间
  	optional int64 deductFeeStartDate = 1;
	// 扣年费结束时间
  	optional int64 deductFeeEndDate = 2;
	// 发奖开始时间
  	optional int64 awardPrizeStartDate = 3;
	// 发奖结束时间
  	optional int64 awardPrizeEndDate = 4;
	// 手机号
  	optional string phoneNo = 5;
	// 状态
  	optional int32 status = 6;
	// 会员编码
  	optional string memberCode = 7;
	// 页数
  	required int32 pageIndex = 8;
	// 每页的数量
  	required int32 pageSize = 9;

}

//  
message	QueryPrizeRecordPageResponse {
	// 发奖记录列表	
	repeated PrizeRecord prizeRecordList = 1;
	// 总记录数	
	required int32 total = 2;

}

// 
message	SavePrizeRecordRequest {
	// 发奖记录
  	required PrizeRecord prizeRecord = 1;

}

//  
message	SavePrizeRecordResponse {
	// 记录ID	
	required int64 id = 1;

}

// 
message	UpdateSerialNoRequest {
	// 卡号
  	required string cardId = 1;
	// ID
  	required int64 id = 2;
	// 流水号
  	required string awardPrizeSerialNo = 3;

}

//  
message	UpdateSerialNoResponse {
	// 	
	required bool knowledge = 1;

}

// 
message	UpdateStateRequest {
	// 卡号
  	required string cardId = 1;
	// ID
  	required int64 id = 2;
	// 发奖状态
  	required int32 awardPrizeStatus = 3;
	// 奖品信息
  	optional string prizeInfo = 4;

}

//  
message	UpdateStateResponse {
	// 	
	required bool knowledge = 1;

}

// 
message	FindAwardPrizeRecordByIdsRequest {
	// 卡号
  	required string cardId = 1;
	// ID
  	repeated int64 idList = 2;

}

//  
message	FindAwardPrizeRecordByIdsResponse {
	// 发奖记录列表	
	repeated PrizeRecord prizeRecordList = 1;

}

// 
message	FindRecordByCardIdRequest {
	// 卡号
  	required string cardId = 1;

}

//  
message	FindRecordByCardIdResponse {
	// 返回结果	
	repeated PrizeRecord prizeRecordList = 1;

}

// 
message	FindRecordByPageRequest {
	// 开始时间
  	optional int64 createTimeStartDate = 1;
	// 结束时间
  	optional int64 createTimeEndDate = 2;
	// 页数
  	required int32 pageIndex = 3;
	// 每页的数量
  	required int32 pageSize = 4;

}

//  
message	FindRecordByPageResponse {
	// 返回结果	
	repeated PrizeRecord prizeRecordList = 1;

}
