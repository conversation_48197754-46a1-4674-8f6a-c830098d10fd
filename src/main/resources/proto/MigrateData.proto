
// Generated by the mtime code generator.  DO NOT EDIT!
// Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.

option java_package = "cmc.card.basic.admin.service.dto";
option java_outer_classname = "MigrateDataProto";

// 迁移数据记录
message	MigrateData {
	// 主键ID	
	required int64 id = 1;
	// 表名	
	required string tableName = 2;
	// 原卡类型ID	
	required int64 fromCardTypeNo = 3;
	// 目标卡类型ID	
	required int64 toCardTypeNo = 4;
	// 原区域ID	
	required string fromAreaId = 5;
	// 目标区域ID	
	required string toAreaId = 6;
	// 迁移影院内码	
	optional string cinemaInnerCode = 7;
	// 迁移KEY	
	required string transferKey = 8;
	// 迁移类型	
	required int32 transferType = 9;
	// oldInfo	
	optional string oldInfo = 10;
	// newInfo	
	optional string newInfo = 11;
	// 迁移状态	
	required int32 state = 12;
	// 版本号	
	required int32 version = 13;
	// 迁移创建时间	
	optional int64 createTime = 14;
	// 更新时间	
	optional int64 updateTime = 15;

}

// 按条件查询迁移数据记录列表
message	QueryListRequest {
	// 表名
  	required string tableName = 1;
	// 迁移key
  	required string transferKey = 2;
	// 版本
  	required int32 version = 3;
	// 状态
  	optional int32 state = 4;

}

// 返回的迁移数据记录列表 
message	QueryListResponse {
	// 返回迁移数据记录	
	repeated MigrateData migrateDataList = 1;

}

// 按条件更新迁移记录的状态
message	UpdateStateRequest {
	// 表名
  	required string tableName = 1;
	// 迁移key
  	required string transferKey = 2;
	// 版本
  	required int32 version = 3;
	// 迁移状态(1迁移中,2迁移完成,3回滚中,4回滚完成)
  	required int32 state = 4;
	// 原卡类型ID
  	required int64 fromCardTypeNo = 5;

}

// 按条件查询迁移数据记录
message	QueryMigrateDataRequest {
	// 表名
  	required string tableName = 1;
	// 迁移key
  	required string transferKey = 2;
	// 版本
  	required int32 version = 3;
	// 原卡类型ID
  	required int64 fromCardTypeNo = 4;

}

// 返回的迁移数据记录 
message	QueryMigrateDataResponse {
	// 返回迁移数据记录	
	optional MigrateData migrateData = 1;

}

// 按主键ID更新迁移数据记录状态和newInfo
message	UpdateStateAndNewInfoByIdRequest {
	// 主键ID
  	required int64 id = 1;
	// 迁移状态
  	required int32 state = 2;
	// newInfo
  	required string newInfo = 3;

}

// 保存迁移数据记录
message	SaveMigrateDataRequest {
	// 迁移数据记录
  	required MigrateData migrateData = 1;

}

//  
message	SaveMigrateDataResponse {
	// 操作结果	
	required bool success = 1;
	// 返回的主键	
	optional int64 id = 2;
	// 异常信息	
	optional string msg = 3;

}
