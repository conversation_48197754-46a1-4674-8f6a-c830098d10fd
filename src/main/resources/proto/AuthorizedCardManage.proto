
// Generated by the mtime code generator.  DO NOT EDIT!
// Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.

option java_package = "cmc.card.basic.admin.service.dto";
option java_outer_classname = "AuthorizedCardManageProto";

// 授权卡实体
message	AuthorizedCardManage {
	// 卡ID	
	required string cardId = 1;
	// 授权卡审核记录	
	optional int32 authApprovalRecord = 2;
	// 出库单号	
	optional string issueOrderId = 3;
	// 所属区域号	
	required string areaId = 4;
	// 所属影院内码	
	required string cinemaInnerCode = 5;
	// 授权卡状态	
	optional int32 authorizedCardState = 6;
	// 审批状态	
	optional int32 auditStatus = 7;
	// 是否为永久卡	
	required int32 isPermanent = 8;
	// 永久有效卡的影院内码	
	required string permanentCinemaInnerCode = 9;
	// 有效期截止日期	
	optional int64 effectiveEnd = 10;
	// 有效期时长	
	optional string effectiveLong = 11;
	// 备注	
	optional string remarks = 12;
	// 创建时间	
	optional int64 creatTime = 13;
	// 创建人	
	optional string creater = 14;
	// 最后修改时间	
	optional int64 updateTime = 15;
	// 最后修改人	
	optional string updater = 16;
	// 创建人级别	
	optional int32 createrLevel = 17;
	// 影院名称	
	optional string cinemaName = 18;
	// 区域名称	
	optional string areaName = 19;
	// 是否删除	
	optional int32 isDelete = 20;
	// 创建人id	
	optional int32 createrId = 21;
	// 更新人id	
	optional int32 updaterId = 22;
	// 审核人id	
	optional int32 auditPerId = 23;
	// 审核人	
	optional string auditPer = 24;
	// 审核时间	
	optional int64 auditTime = 25;
	// 提交人id	
	optional int32 submitPerId = 26;
	// 提交人	
	optional string submitPer = 27;
	// 提交时间	
	optional int64 submitTime = 28;
	// 主键	
	optional int32 id = 29;

}

// 授权卡服务记录实体
message	AuthorizedCardService {
	// 授权流水号	
	optional int32 id = 1;
	// 授权卡ID	
	optional string authorizedId = 2;
	// 被授权卡ID	
	optional string serviceId = 3;
	// 所属区域号	
	optional string areaId = 4;
	// 所属影院号内码	
	optional string cinemaInnerCode = 5;
	// 服务类型	
	optional int32 authorizedServiceType = 6;
	// 操作时间	
	optional int64 createTime = 7;
	// 操作人	
	optional string creater = 8;
	// 影院名称	
	optional string cinemaName = 9;
	// 区域名称	
	optional string areaName = 10;

}

// 
message	SaveAuthorizedCardManageRequest {
	// 授权卡对象
  	repeated AuthorizedCardManage authorizedcardManages = 1;

}

//  
message	SaveAuthorizedCardManageResponse {
	// 返回执行结果	
	required bool success = 1;
	// 授权卡审核记录	
	optional int32 authApprovalRecord = 2;

}

// 
message	UpdateAuthorizedCardManageRequest {
	// 授权卡审核记录
  	required int32 authApprovalRecord = 1;
	// 有效期时长
  	optional string effectiveLong = 2;
	// 最后修改人
  	optional string updater = 3;
	// 更新人id
  	required int32 updaterId = 4;
	// 备注
  	optional string remarks = 5;

}

//  
message	UpdateAuthorizedCardManageResponse {
	// 返回执行结果	
	required bool success = 1;

}

// 
message	DeleteAuthorizedCardManageRequest {
	// 主键id
  	required int32 id = 1;

}

//  
message	DeleteAuthorizedCardManageResponse {
	// 返回执行结果	
	required bool success = 1;

}

// 查询授权卡信息列表页的请求条件
message	QueryAuthorizedCardManageListRequest {
	// 分页信息
  	optional PageInfo PageInfo = 1;
	// 授权卡号
  	optional string cardNos = 2;
	// 区域ID
  	optional string areaId = 3;
	// 所属影院内码
  	optional string cinemaInnerCode = 4;
	// 授权卡状态
  	optional int32 authorizedCardState = 5;
	// 审核状态
  	optional int32 auditStatus = 6;
	// 排序字段
  	optional string sort = 7;

}

// 授权卡信息列表页返回结果 
message	QueryAuthorizedCardManageListResponse {
	// 返回授权卡列表	
	repeated AuthorizedCardManage AuthorizedCardManage = 1;
	// 总记录数	
	required int32 total = 2;
	// 页码	
	required int32 pageIndex = 3;
	// 每页记录数	
	required int32 pageSize = 4;

}

// 查询授权卡审批信息页的请求条件
message	QueryAuthorizedCardApprovalListRequest {
	// 分页信息
  	optional PageInfo PageInfo = 1;
	// 授权卡号
  	optional string cardNos = 2;
	// 区域ID
  	optional string areaId = 3;
	// 所属影院内码
  	optional string cinemaInnerCode = 4;
	// 审批类型
  	required int32 auditType = 5;
	// 排序字段
  	optional string sort = 6;
	// 授权卡审核记录
  	optional int32 authApprovalRecord = 7;

}

// 授权卡审批信息页返回结果 
message	QueryAuthorizedCardApprovalListResponse {
	// 返回授权卡列表	
	repeated AuthorizedCardManage AuthorizedCardManage = 1;
	// 总记录数	
	required int32 total = 2;
	// 页码	
	required int32 pageIndex = 3;
	// 每页记录数	
	required int32 pageSize = 4;

}

// 授权卡记录查询的请求条件
message	QueryAuthorizedCardLogListRequest {
	// 分页信息
  	optional PageInfo PageInfo = 1;
	// 授权卡号
  	optional string cardNos = 2;
	// 区域ID
  	optional string areaId = 3;
	// 所属影院内码
  	optional string cinemaInnerCode = 4;
	// 服务类型
  	optional int32 authorizedServiceType = 5;

}

// 授权卡信息列表页返回结果 
message	QueryAuthorizedCardLogListResponse {
	// 返回授权卡列表	
	repeated AuthorizedCardService AuthorizedCardService = 1;
	// 总记录数	
	required int32 total = 2;
	// 页码	
	required int32 pageIndex = 3;
	// 每页记录数	
	required int32 pageSize = 4;

}

// 获取授权卡信息的请求条件
message	GetAuthorizedCardByIdRequest {
	// 主键
  	required int32 id = 1;

}

// 获取授权卡信息的返回结果 
message	GetAuthorizedCardByIdResponse {
	// 是否成功	
	required bool success = 1;
	// 授权卡信息	
	optional AuthorizedCardManage authorizedCardManage = 2;

}

// 获取单条审批记录信息请求条件
message	GetAuthApprovalRecordRequest {
	// 授权卡审核记录
  	required int32 authApprovalRecord = 1;

}

// 获取单条审批记录信息的返回结果 
message	GetAuthApprovalRecordResponse {
	// 是否成功	
	required bool success = 1;
	// 授权卡信息	
	optional AuthorizedCardManage authorizedCardManage = 2;

}

// 更新授权卡业务状态请求
message	UpdateBizStatusRequest {
	// 主键
  	required int32 id = 1;
	// 授权卡状态
  	required int32 authorizedCardState = 2;
	// 最后修改时间
  	optional int64 updateTime = 3;
	// 最后修改人
  	optional string updater = 4;
	// 更新人id
  	optional int32 updaterId = 5;

}

//  
message	UpdateBizStatusResponse {
	// 返回新授权卡业务状态响应结果	
	required bool success = 1;

}

// 提交授权卡申请请求条件
message	SubmitRequest {
	// 授权卡审核记录
  	required int32 authApprovalRecord = 1;
	// 提交人id
  	required int32 submitPerId = 2;
	// 提交人
  	optional string submitPer = 3;
	// 提交时间
  	required int64 submitTime = 4;
	// 是否提交到审批流
  	required bool isSubmitApproval = 5;

}

//  
message	SubmitResponse {
	// 返回执行结果	
	required bool success = 1;

}

// 撤回请求条件
message	RecallRequest {
	// 主键
  	required int32 id = 1;
	// 最后修改时间
  	optional int64 updateTime = 2;
	// 最后修改人
  	optional string updater = 3;
	// 更新人id
  	optional int32 updaterId = 4;

}

//  
message	RecallResponse {
	// 返回执行结果	
	required bool success = 1;

}

// 验证卡号是否存在请求条件
message	CheckExistCardIdsRequest {
	// 授权卡号
  	repeated string cardIds = 1;

}

//  
message	CheckExistCardIdsResponse {
	// 返回执行结果	
	required bool success = 1;

}

// 修改审批状态请求
message	UpdateAuditStatusRequest {
	// 授权卡审核记录
  	required int32 authApprovalRecord = 1;
	// 审批状态
  	required int32 auditStatus = 2;
	// 审批人userId
  	optional int32 approvalUserId = 3;
	// 审批时间
  	optional int64 auditTime = 4;
	// 审批人姓名
  	optional string userName = 5;
	// 有效日期
  	optional int64 effectiveEnd = 6;

}

//  
message	UpdateAuditStatusResponse {
	// 返回修改审批状态响应结果	
	required bool success = 1;

}

// 更新请求
message	UpdateAuthorizedCardForTransferRequest {
	// 原区域ID
  	required string sourceRegionId = 1;
	// 原区域名称
  	required string sourceRegionName = 2;
	// 目标区域ID
  	required string targetRegionId = 3;
	// 目标区域名称
  	required string targetRegionName = 4;
	// 影院内码
  	optional string cinemaInnerCode = 5;

}
