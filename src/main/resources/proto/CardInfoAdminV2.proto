
// Generated by the mtime code generator.  DO NOT EDIT!
// Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.

option java_package = "cmc.card.basic.admin.service.dto";
option java_outer_classname = "CardInfoAdminV2Proto";

// 卡常用信息
message	CardInfo {
	// 卡ID	
	optional string cardId = 1;
	// 手机号	
	optional string phoneNo = 2;
	// 密码	
	optional string passWord = 3;
	// 卡的密码	
	optional string passWordClear = 4;
	// 用户名	
	optional string userName = 5;
	// 卡类型	
	optional int32 cardType = 6;
	// 卡类别	
	optional int32 cardSort = 7;
	// 是否是实体卡	
	optional int32 ifEntity = 8;
	// 区域ID	
	optional string areaId = 9;
	// 影院内码	
	optional string cinemaInnerCode = 10;
	// 卡余额	
	optional int32 balance = 11;
	// 是否允许网购	
	optional int32 canuseOnLine = 12;
	// 卡状态	
	optional int32 cardState = 13;
	// 有效期截止日期	
	optional int64 effectiveEnd = 14;
	// 卡类型ID	
	optional int64 cardTypeNo = 15;
	// 消费次数	
	optional int32 spendTimes = 16;
	// 影院名称	
	optional string cinemaName = 17;
	// 是否是刮刮卡初始密码	
	optional string useOriginPwd = 18;
	// 销售单id	
	optional string sellOrderId = 19;
	// 预冻总金额	
	optional int32 txTransBal = 20;
	// 发卡时间	
	optional int64 issueTime = 21;
	// 最后更新时间	
	optional int64 lastTime = 22;
	// 版本号	
	optional int32 version = 23;
	// 赠送总金额	
	optional int32 presentAmount = 24;
	// 赠送余额	
	optional int32 presentBalance = 25;

}

// 卡类型卡面信息
message	CardTypeInfo {
	// 卡号	
	optional string cardId = 1;
	// 卡类型编码	
	optional string cardTypeCode = 2;
	// 卡面编码	
	optional string coverCode = 3;

}

// 卡充值订单组合信息
message	CardRechargeOrderInfo {
	// 卡号	
	optional string cardId = 1;
	// 手机号	
	optional string phoneNo = 2;
	// 余额	
	optional int32 balance = 3;
	// 状态	
	optional string status = 4;
	// 订单号	
	optional string orderId = 5;
	// 充值金额	
	optional int32 rechargeAmount = 6;
	// 操作人	
	optional string operator = 7;
	// 操作时间	
	optional string operationTime = 8;
	// 备注	
	optional string remark = 9;

}

// 
message	GetCardInfoRequest {
	// 卡号
  	required string cardId = 1;

}

// 卡查询返回结果 
message	GetCardInfoResponse {
	// 卡基本信息	
	optional CardInfo cardInfo = 1;

}

// 
message	UpdateCardStateRequest {
	// 卡号
  	required string cardId = 1;
	// 备注
  	optional string remark = 2;
	// 要修改的卡状态值
  	required int32 cardState = 3;
	// 操作员
  	optional string userName = 4;
	// 操作员Id
  	optional int64 userId = 5;
	// 登录人的影城
  	required string userCinemaInnerCode = 6;
	// 登录人的影城名称
  	required string userCinemaName = 7;
	// 渠道编码
  	required string channelCode = 8;
	// 渠道编码名称
  	required string channelCodeName = 9;

}

//  
message	UpdateCardStateResponse {
	// 返回执行结果	
	optional bool success = 1;
	// 失败原因	
	optional string failReason = 2;

}

// 
message	UpdateCardStateInBatchesRequest {
	// 卡号
  	repeated string cardIds = 1;
	// 备注
  	optional string remark = 2;
	// 要修改的卡状态值
  	required int32 cardState = 3;
	// 操作员
  	optional string userName = 4;
	// 操作员Id
  	optional int64 userId = 5;

}

//  
message	UpdateCardStateInBatchesResponse {
	// 返回执行结果	
	optional bool success = 1;
	// 失败原因	
	optional string failReason = 2;

}

// 
message	UpdatePresentBalanceRequest {
	// 卡号
  	required string cardId = 1;
	// 赠送余额
  	required int32 presentBalance = 2;
	// 版本号
  	required int32 version = 3;

}

//  
message	UpdatePresentBalanceResponse {
	// 返回执行结果	
	optional bool success = 1;
	// 失败原因	
	optional string failReason = 2;

}

// 获取应收取年费卡列表请求参数
message	FindCardByTimeWithPageRequest {
	// 开始时间
  	required int64 startTime = 1;
	// 结束时间
  	required int64 endTime = 2;
	// 页码
  	required int32 pageNo = 3;
	// 页数
  	required int32 pageSize = 4;

}

// 获取应收取年费卡列表请求响应 
message	FindCardByTimeWithPageResponse {
	// 卡列表	
	repeated CardInfo cardInfoList = 1;

}

// 
message	CalculatePresentBalanceRequest {
	// 卡号
  	required string cardId = 1;
	// 卡类型编码
  	optional string cardTypeCode = 2;

}

//  
message	CalculatePresentBalanceResponse {
	// 卡赠送余额	
	optional int32 cardPresentBalance = 1;

}

// 根据卡号查询卡类型和卡面编码
message	FindCardTypeInfoListRequest {
	// 卡号
  	repeated string cardIdList = 1;

}

//  
message	FindCardTypeInfoListResponse {
	// 卡类型信息列表	
	repeated CardTypeInfo cardTypeInfoList = 1;

}

// 
message	CalculatePresentBalanceSnapshootRequest {
	// 卡号
  	required string cardId = 1;
	// 卡类型编码
  	required string cardTypeCode = 2;
	// 某时间卡余额
  	required int32 cardBalanceSnapshoot = 3;
	// 结束时间
  	required int64 endTime = 4;

}

//  
message	CalculatePresentBalanceSnapshootResponse {
	// 卡赠送余额	
	optional int32 cardPresentBalance = 1;

}

// 
message	NegativeRechargeRequest {
	// 卡号
  	required string cardId = 1;
	// 负充金额
  	required int32 negativeNum = 2;
	// 赠送负充金额
  	optional int32 negativePresentNum = 3;
	// 备注
  	optional string remark = 4;
	// 操作人
  	optional string operator = 5;
	// 操作人id
  	optional int32 operatorId = 6;

}

//  
message	NegativeRechargeResponse {
	// 返回执行结果	
	optional bool success = 1;
	// 失败原因	
	optional string failReason = 2;

}

// 
message	FindCardRechargeOrderInfoRequest {
	// 订单号
  	repeated string orderIds = 1;

}

// 返回结果 
message	FindCardRechargeOrderInfoResponse {
	// 卡充值订单信息	
	repeated CardRechargeOrderInfo cardRechargeOrderInfoList = 1;

}
