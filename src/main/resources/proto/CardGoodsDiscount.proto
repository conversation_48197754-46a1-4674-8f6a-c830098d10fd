
// Generated by the mtime code generator.  DO NOT EDIT!
// Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.

option java_package = "cmc.card.basic.admin.service.dto";
option java_outer_classname = "CardGoodsDiscountProto";

// 卖品折扣率
message	GoodsDiscount {
	// 卖品折扣率主键	
	required int64 id = 1;
	// 卡类型ID	
	required int64 cardTypeId = 2;
	// 卡类型名称	
	required string cardName = 3;
	// 区域ID	
	required string regionId = 4;
	// 区域名称	
	required string regionName = 5;
	// 影院内码	
	required string innerCode = 6;
	// 影院名称	
	required string cinemaName = 7;
	// 卡类别的值	
	required int32 cardSort = 8;
	// 卖品折扣率	
	required int32 discountRate = 9;
	// 最后更新时间	
	required int64 lastTime = 10;
	// 8位影院专资编码	
	optional string govCode = 11;

}

// 同步任务信息
message	SyncInfo {
	// 区域ID	
	required string regionId = 1;
	// 影院内码	
	required string cinemaInnerCode = 2;
	// 卡类别的值	
	required int32 cardSort = 3;
	// 同步状态	
	required string status = 4;
	// 同步请求时间	
	required int64 createTime = 5;
	// 同步开始时间	
	optional int64 startTime = 6;
	// 同步结束	
	optional int64 endTime = 7;
	// 同步结束	
	repeated string syncLogs = 8;

}

// 同步任务键组成的对象
message	SyncInfoKey {
	// 区域ID	
	required string regionId = 1;
	// 影院内码	
	required string cinemaInnerCode = 2;
	// 卡类别的值	
	required int32 cardSort = 3;

}

// 
message	GetGoodsDiscountListRequest {
	// 分页信息
  	optional PageInfo PageInfo = 1;
	// 区域id
  	optional string regionId = 2;
	// 影院内码
  	optional string cinemaInnerCode = 3;
	// 卡类别的值
  	optional int32 cardSort = 4;
	// 卡类型名称
  	optional string cardName = 5;

}

//  
message	GetGoodsDiscountListResponse {
	// 卖品折扣率列表	
	repeated GoodsDiscount goodsDiscounts = 1;
	// 总记录数	
	required int32 total = 2;
	// 页码	
	required int32 pageIndex = 3;
	// 每页记录数	
	required int32 pageSize = 4;

}

// 
message	DataSyncRequest {
	// 区域id
  	optional string regionId = 1;
	// 影院内码
  	optional string cinemaInnerCode = 2;
	// 卡类别的值
  	optional int32 cardSort = 3;

}

//  
message	DataSyncResponse {
	// 是否发送同步任务成功	
	optional bool success = 1;
	// 提示信息	
	optional string msg = 2;

}

// 
message	GetSyncInfoRequest {
	// 区域id
  	optional string regionId = 1;
	// 影院内码
  	optional string cinemaInnerCode = 2;
	// 卡类别的值
  	optional int32 cardSort = 3;

}

//  
message	GetSyncInfoResponse {
	// 同步数据的任务信息	
	optional SyncInfo syncInfo = 1;

}

//  
message	GetSyncListResponse {
	// 同步数据的任务信息	
	repeated SyncInfoKey syncInfoKeys = 1;

}

// 
message	UpdateDiscountRateRequest {
	// 买频率设置的id
  	required int64 id = 1;
	// 折扣率
  	required int32 discountRate = 2;

}

//  
message	UpdateDiscountRateResponse {
	// 买频率是否更新成功	
	required bool success = 1;
	// 错误描述	
	optional string msg = 2;

}

// 
message	UpdateGoodsDiscountRequest {
	// 更新卖品折扣率信息
  	required GoodsDiscount goodsDiscount = 1;

}

//  
message	UpdateGoodsDiscountResponse {
	// 卖品折扣率信息是否更新成功	
	required bool success = 1;
	// 错误描述	
	optional string msg = 2;

}

// 
message	DeleteByIdRequest {
	// 主键
  	required int64 id = 1;

}

//  
message	DeleteByIdResponse {
	// 按主键删除是否成功	
	required bool success = 1;
	// 错误描述	
	optional string msg = 2;

}

// 
message	SaveGoodsDiscountRequest {
	// 卖品折扣率
  	required GoodsDiscount goodsDiscount = 1;

}

//  
message	SaveGoodsDiscountResponse {
	// 保存卖品折扣率是否成功	
	required bool success = 1;
	// 错误描述	
	optional string msg = 2;

}

// 
message	GetGoodsDiscountByCinemaInnerCodeRequest {
	// 影院内码
  	required string cinemaInnerCode = 1;

}

//  
message	GetGoodsDiscountByCinemaInnerCodeResponse {
	// 卖品折扣率列表	
	repeated GoodsDiscount goodsDiscounts = 1;

}
