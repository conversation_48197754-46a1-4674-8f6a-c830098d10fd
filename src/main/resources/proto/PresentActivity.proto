
// Generated by the mtime code generator.  DO NOT EDIT!
// Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.

option java_package = "cmc.card.basic.admin.service.dto";
option java_outer_classname = "PresentActivityProto";

// 
message	PresentActivity {
	// 	
	optional int32 id = 1;
	// 	
	optional string code = 2;
	// 	
	optional string name = 3;
	// 	
	optional string operater = 4;
	// 	
	optional int64 operateTime = 5;
	// 	
	optional string fileName = 6;
	// 	
	optional string fileId = 7;
	// 	
	optional int32 state = 8;

}

// 
message	PresentActivityDetail {
	// 	
	optional int32 id = 1;
	// 	
	optional int32 presentActivityId = 2;
	// 	
	optional string cardId = 3;
	// 	
	optional int32 cardState = 4;
	// 	
	optional string cardTypeCode = 5;
	// 	
	optional string cardTypeName = 6;
	// 	
	optional int32 presentState = 7;
	// 	
	optional string failRemark = 8;
	// 	
	optional int32 presentEveryAmount = 9;

}

// 查询请求
message	ListRequest {
	// 编码
  	optional string code = 1;
	// 名称
  	optional string name = 2;
	// 操作人
  	optional string operater = 3;
	// 分页
  	optional PageInfo page = 4;

}

//  
message	ListResponse {
	// 	
	repeated PresentActivity presentActivity = 1;
	// 	
	required int64 totalCount = 2;

}

// 查询请求
message	GetRequest {
	// id
  	optional int32 id = 1;

}

//  
message	GetResponse {
	// 	
	required PresentActivity presentActivity = 1;

}

// 
message	SaveRequest {
	// 编码
  	optional string code = 1;
	// 名称
  	optional string name = 2;
	// 操作人
  	optional string operater = 3;
	// 操作人id
  	optional string operaterId = 4;
	// 文件名
  	optional string fileName = 5;
	// 文件id
  	optional string fileId = 6;

}

//  
message	SaveResponse {
	// 活动id	
	required int64 id = 1;

}

// 查询活动详情请求
message	DetailRequest {
	// 赠送活动id
  	required int32 presentActivityId = 1;
	// 卡号
  	optional string cardId = 2;
	// 赠送状态
  	optional int32 presentState = 3;
	// 分页
  	optional PageInfo page = 4;

}

//  
message	DetailResponse {
	// 	
	required PresentActivity presentActivity = 1;
	// 	
	repeated PresentActivityDetail detail = 2;
	// 	
	required int64 totalCount = 3;

}

// 查询活动详情请求
message	SaveDetailRequest {
	// 赠送活动卡信息详情
  	repeated PresentActivityDetail presentActivityDetail = 1;

}

// 更新卡赠送状态
message	UpdateCardPresentStateRequest {
	// 赠送活动id
  	required int32 presentActivityId = 1;
	// 卡号
  	required string cardId = 2;
	// 
  	required int32 presentState = 3;
	// 失败说明(1000字符以内)
  	optional string failRemark = 4;

}

// 
message	FinishRequest {
	// id
  	optional int32 id = 1;

}

// 
message	ProcessingRequest {
	// id
  	optional int32 id = 1;

}
