
// Generated by the mtime code generator.  DO NOT EDIT!
// Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.

option java_package = "cmc.card.basic.admin.service.dto";
option java_outer_classname = "FixProto";

// 服务记录信息
message	ServiceRecord {
	// 主键	
	optional int64 id = 1;
	// 卡号	
	optional string cardId = 2;
	// 服务时间	
	optional int64 serviceTime = 3;
	// 服务类型	
	optional int32 serviceType = 4;
	// 服务前余额	
	optional int32 beforeServiceBalance = 5;
	// 服务后余额	
	optional int32 afterServiceBalance = 6;
	// 卡余额变化	
	optional int32 changeBalance = 7;
	// 备注	
	optional string remarks = 8;
	// 卡类型名称	
	optional string cardTypeName = 9;
	// 卡类型编码	
	optional string cardTypeCode = 10;

}

//  
message	GetDatabaseNameResponse {
	// 读分片名称集合	
	repeated string DatabaseNameList = 1;

}

// 
message	FindAnnualFeeServiceRecordRequest {
	// 数据库名称
  	optional string dbName = 1;
	// 服务类型
  	optional int32 serviceType = 2;
	// 扣减年度
  	optional string deductYear = 3;
	// 分页信息
  	required PageInfo pageInfo = 4;

}

//  
message	FindAnnualFeeServiceRecordResponse {
	// 服务记录列表	
	repeated ServiceRecord serviceRecordList = 1;

}

// 
message	RecoverCardBalanceRequest {
	// 卡号
  	optional string cardId = 1;
	// 恢复金额
  	optional int32 recoverBalance = 2;
	// 卡类型名称
  	optional string cardTypeName = 3;
	// 卡类型编码
  	optional string cardTypeCode = 4;

}
