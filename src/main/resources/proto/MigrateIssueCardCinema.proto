
// Generated by the mtime code generator.  DO NOT EDIT!
// Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.

option java_package = "cmc.card.basic.admin.service.dto";
option java_outer_classname = "MigrateIssueCardCinemaProto";

// 迁移数据记录
message	MigrateCardInfo {
	// 卡号	
	required string cardId = 1;
	// 卡类型id	
	optional int64 cardTypeId = 2;
	// 手机号	
	optional string phoneNo = 3;
	// 余额	
	optional int32 balance = 4;

}

// 
message	MigrateRequest {
	// 数据库分片节点
  	required string dbName = 1;
	// 源影城内码
  	required string sourceCinemaInnerCode = 2;
	// 目的影城内码
  	required string targetCinemaInnerCode = 3;
	// 迁移id
  	required int64 migrateId = 4;

}

// 
message	SynCardInfoEsRequest {
	// 迁移id
  	required int64 migrateId = 1;
	// 目的影城内码
  	required string targetCinemaInnerCode = 2;

}

// 
message	QueryCountRequest {
	// 源影城内码
  	required string sourceCinemaInnerCode = 1;

}

//  
message	QueryCountResponse {
	// 当前总数	
	required int64 curCount = 1;
	// 已迁移总数	
	required int64 migratedCount = 2;
	// 已迁移ids	
	repeated int64 migrateIds = 3;

}

// 
message	QueryMigrateIssueCardInfoRequest {
	// 迁移ids
  	repeated int64 migrateIds = 1;
	// 页码
  	required int32 page = 2;
	// 页大小
  	required int32 pageSize = 3;

}

//  
message	QueryMigrateIssueCardInfoResponse {
	// 迁移的卡信息	
	repeated MigrateCardInfo migrateCardInfo = 1;

}

// 
message	SaveMigrateIssueCardCinemaRequest {
	// 源影城内码
  	required string sourceCinemaInnerCode = 1;
	// 目的影城内码
  	required string targetCinemaInnerCode = 2;

}

//  
message	SaveMigrateIssueCardCinemaResponse {
	// 总数	
	required int64 id = 1;

}

// 
message	UpdateMigrateIssueCardCinemaRequest {
	// 迁移id
  	required int64 migrateId = 1;
	// 迁移id
  	required int32 state = 2;

}

// 
message	LoadMigrateCardInfoByCinemaInnerCodeRequest {
	// 数据节点名称
  	required string dbName = 1;
	// 原影城内码
  	required string sourceCinemaInnerCode = 2;
	// 页码
  	required int32 pageNo = 3;

}

//  
message	LoadMigrateCardInfoByCinemaInnerCodeResponse {
	// 迁移的卡信息	
	repeated MigrateCardInfo migrateCardInfo = 1;

}

// 
message	LogRequest {
	// 迁移id
  	required int64 migrateId = 1;
	// 迁移的卡信息
  	repeated MigrateCardInfo migrateCardInfo = 2;

}
