
// Generated by the mtime code generator.  DO NOT EDIT!
// Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.

option java_package = "cmc.card.basic.admin.service.dto";
option java_outer_classname = "CardHandleProto";

// 卡查询信息对象
message	QueryVirtualCardInfo {
	// 卡ID	
	optional string cardId = 1;
	// 卡密码	
	optional string passWordClear = 2;

}

// 每个数据节点总卡数
message	EveryDbNodeTotalCardCount {
	// 数据节点名	
	required string dbNodeName = 1;
	// 总记录数	
	required int64 totalCount = 2;

}

// 卡查询信息对象
message	ChangedCardInfo {
	// 卡ID	
	optional string cardId = 1;
	// 手机号	
	optional string mobile = 2;
	// 卡储值类型	
	optional int32 valueType = 3;
	// 卡有效截止时间	
	optional int64 exiredDate = 4;

}

// 卡查询信息对象
message	QueryCardInfo {
	// 卡ID	
	optional string cardId = 1;
	// 手机号	
	optional string phoneNo = 2;
	// 用户名	
	optional string userName = 3;
	// 卡类型	
	optional int32 cardType = 4;
	// 是否是实体卡	
	optional int32 ifEntity = 5;
	// 区域ID	
	optional string areaId = 6;
	// 影院内码	
	optional string cinemaInnerCode = 7;
	// 卡状态名称	
	optional int32 cardState = 8;
	// 有效期截止日期	
	optional int64 effectiveEnd = 9;
	// 卡类型id	
	optional int64 cardTypeNo = 10;
	// 卡类型状态	
	optional string cardTypeNoState = 11;
	// 证件类型名称	
	optional string certificatesTypeName = 12;
	// 证件号	
	optional string certificatesNo = 13;
	// 邮箱	
	optional string email = 14;
	// QQ号	
	optional string qq = 15;
	// 是否是刮刮卡	
	optional string yesOrNoGuaGuaCard = 16;
	// 邮编	
	optional int32 postCode = 17;
	// 卡状态值	
	optional int32 cardStateValue = 18;
	// 卡状态名称	
	optional string cardStateName = 19;
	// 卡类型编号	
	optional string cardTypeCode = 20;
	// 卡类型名称	
	optional string cardTypeName = 21;
	// 证件类型值	
	optional int32 certificatesType = 22;

}

// 查询有变更卡的请求
message	QueryChangedCardListRequest {
	// 指定的开始时间
  	required int64 startTime = 1;
	// 指定的结束时间
  	required int64 endTime = 2;
	// 每次获取的记录数
  	required int32 count = 3;
	// ES游标的标记
  	optional string scrollId = 4;

}

//  
message	QueryChangedCardListResponse {
	// 有变化的卡信息列表	
	repeated ChangedCardInfo changedCardInfos = 1;
	// ES游标的标记	
	optional string scrollId = 2;

}

// 
message	DisableNoActiveCardRequest {
	// 卡号
  	repeated string cardIds = 1;

}

// 
message	NegativeRechargeNoActiveCardRequest {
	// 卡号
  	repeated string cardIds = 1;
	// 操作人id
  	optional int64 operatorId = 2;
	// 操作人
  	optional string operatorName = 3;

}

// 
message	RevertNoActiveCardRequest {
	// 卡号
  	repeated string cardIds = 1;

}

// 
message	QueryEveryDbNodeTotalCardCountRequest {
	// 批量申请单号
  	required string sellOrderId = 1;

}

//  
message	QueryEveryDbNodeTotalCardCountResponse {
	// 每个数据节点总卡数	
	repeated EveryDbNodeTotalCardCount everyDbNodeTotalCardCount = 1;

}

// 
message	QueryCardInfoBySellOrderIdRequest {
	// 数据库节点
  	required string dbNode = 1;
	// 批量申请单号
  	required string sellOrderId = 2;
	// 批量申请单号
  	required PageInfo page = 3;

}

//  
message	QueryCardInfoBySellOrderIdResponse {
	// 卡信息	
	repeated QueryVirtualCardInfo queryVirtualCardInfo = 1;

}

// 
message	RefundCardRequest {
	// 卡号,以逗号分隔
  	required string cardIds = 1;
	// 备注
  	required string remarks = 2;
	// 操作人
  	required string operator = 3;
	// 操作人id
  	required int64 operatorId = 4;

}
