
// Generated by the mtime code generator.  DO NOT EDIT!
// Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.

option java_package = "cmc.card.basic.admin.service.dto";
option java_outer_classname = "PushBiServiceRecordProto";

// 
message	GetBiServiceRecordListByTimeRequest {
	// 起始时间
  	optional int64 startTime = 1;
	// 结束时间
  	optional int64 endTime = 2;
	// 服务类型
  	optional string serviceType = 3;

}

//  
message	GetBiServiceRecordListByTimeResponse {
	// 卡服务记录列表	
	repeated ServiceRecord ServiceRecordList = 1;

}

// 
message	GetBiEsServiceRecordListByTimeRequest {
	// 起始时间
  	optional int64 startTime = 1;
	// 结束时间
  	optional int64 endTime = 2;
	// 服务类型
  	optional string serviceType = 3;

}

//  
message	GetBiEsServiceRecordListByTimeResponse {
	// 卡服务记录列表	
	repeated ServiceRecord ServiceRecordList = 1;

}

// 
message	GetBiServiceRecordTotalCountByTimeRequest {
	// 起始时间
  	optional int64 startTime = 1;
	// 结束时间
  	optional int64 endTime = 2;
	// 服务类型
  	optional string serviceType = 3;

}

//  
message	GetBiServiceRecordTotalCountByTimeResponse {
	// 总条数	
	required int64 totalCount = 1;

}

// 
message	GetBiServiceRecordPageNumsByTimeRequest {
	// 起始时间
  	optional int64 startTime = 1;
	// 结束时间
  	optional int64 endTime = 2;
	// 每页大小
  	required int32 pageSize = 3;
	// 服务类型
  	optional string serviceType = 4;

}

//  
message	GetBiServiceRecordPageNumsByTimeResponse {
	// 总页数	
	required int32 pageNum = 1;

}

// 
message	GetBiServiceRecordListByTimeWithPageRequest {
	// 起始时间
  	optional int64 startTime = 1;
	// 结束时间
  	optional int64 endTime = 2;
	// 页码
  	required int32 pageIdx = 3;
	// 每页大小
  	required int32 pageSize = 4;
	// 服务类型
  	optional string serviceType = 5;

}

//  
message	GetBiServiceRecordListByTimeWithPageResponse {
	// 卡服务记录列表	
	repeated ServiceRecord ServiceRecordList = 1;

}
