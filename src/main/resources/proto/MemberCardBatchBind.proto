
// Generated by the mtime code generator.  DO NOT EDIT!
// Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.

option java_package = "cmc.card.basic.admin.service.dto";
option java_outer_classname = "MemberCardBatchBindProto";

// 电话号和卡号
message	PhoneNoAndCardId {
	// 	
	optional string phoneNo = 1;
	// 	
	optional string cardId = 2;

}

// 批次保存参数
message	BatchSaveParam {
	// 	
	required string activityName = 1;
	// 	
	required int64 operatorId = 2;
	// 	
	required string operator = 3;
	// 	
	required int32 isSms = 4;
	// 	
	optional string smsContent = 5;
	// 	
	repeated PhoneNoAndCardId phoneNoAndCardId = 6;

}

// 
message	Detail {
	// 	
	optional int64 id = 1;
	// 	
	optional int64 batchId = 2;
	// 	
	optional string cardId = 3;
	// 	
	optional string phoneNo = 4;
	// 	
	optional string cardTypeName = 5;
	// 	
	optional int64 createTime = 6;
	// 	
	optional string operator = 7;
	// 	
	optional int32 status = 8;
	// 	
	optional int32 failReason = 9;
	// 	
	optional string cinemaInnerCode = 10;
	// 	
	optional int32 smsStatus = 11;

}

// 
message	Batch {
	// 	
	optional int64 id = 1;
	// 	
	optional string activityName = 2;
	// 	
	optional int64 createTime = 3;
	// 	
	optional string operator = 4;
	// 	
	optional int32 cardNum = 5;
	// 	
	optional int32 isSms = 6;
	// 	
	optional int32 status = 7;
	// 	
	optional string smsContent = 8;

}

// 查询有变更卡的请求
message	ListRequest {
	// 指定的开始时间
  	optional int64 startTime = 1;
	// 指定的结束时间
  	optional int64 endTime = 2;
	// 活动编号
  	optional string batchId = 3;
	// 
  	optional string activityName = 4;
	// 状态
  	optional int32 status = 5;
	// 分页
  	optional PageInfo page = 6;

}

//  
message	ListResponse {
	// 	
	repeated Batch batch = 1;
	// 	
	required int64 totalCount = 2;

}

// 查询有变更卡的请求
message	GetRequest {
	// 活动编号
  	required string batchId = 1;

}

//  
message	GetResponse {
	// 	
	optional Batch batch = 1;

}

// 查询有变更卡的请求
message	QueryActiveNameRequest {
	// 活动名称
  	required string activityName = 1;

}

//  
message	QueryActiveNameResponse {
	// 	
	optional Batch batch = 1;

}

// 查询有变更卡的请求
message	SaveRequest {
	// 保存参数
  	required BatchSaveParam saveParam = 1;

}

//  
message	SaveResponse {
	// 批次id	
	required int64 batchId = 1;

}

// 
message	UpdateBatchDetailRequest {
	// 批次详情id
  	required int64 id = 1;
	// 
  	required int32 status = 3;
	// 
  	optional int32 failReason = 4;
	// 
  	optional int32 smsStatus = 5;

}

// 
message	ReBindUpdateBatchAndDetailBindingStatusRequest {
	// 批次id
  	required int64 id = 1;

}

// 
message	UpdateBatchDetailSmsStatusRequest {
	// 批次详情id
  	required int64 id = 1;
	// 
  	optional int32 smsStatus = 2;

}

// 
message	UpdateBatchStatusRequest {
	// 批次号
  	required string batchId = 1;
	// 状态
  	required int32 status = 2;

}

// 查询有变更卡的请求
message	DetailListRequest {
	// 活动编号
  	required string batchId = 1;
	// 卡号
  	optional string cardId = 2;
	// 手机号
  	optional string phoneNo = 3;
	// 状态
  	optional int32 status = 4;
	// 分页
  	optional PageInfo page = 5;

}

//  
message	DetailListResponse {
	// 	
	repeated Detail detail = 1;
	// 	
	required int64 totalCount = 2;

}
