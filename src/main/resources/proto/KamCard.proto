
// Generated by the mtime code generator.  DO NOT EDIT!
// Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.

option java_package = "cmc.card.basic.admin.service.dto";
option java_outer_classname = "KamCardProto";

// 充值请求
message	rechargeRequest {
	// 卡号
  	required string cardNo = 1;
	// 合同号
  	required string contractNo = 2;
	// 充值订单号
  	required string rechargeOrderNo = 3;
	// 充值流水号
  	required string batchNo = 4;
	// 充值金额
  	required int32 rechargeAmount = 5;
	// 赠送金额
  	optional int32 presentAmount = 6;
	// 操作人
  	required string operator = 7;

}

//  
message	rechargeResponse {
	// 充值是否成功	
	required boolean success = 1;
	// 错误信息（充值失败时填充）	
	optional string errorMsg = 2;
	// 充值金额（分）	
	optional int32 rechargeAmount = 3;
	// 赠送金额（分）	
	optional int32 presentAmount = 4;
	// 余额变动（充值金额+赠送金额）（分）	
	optional int32 changeBalance = 5;
	// 充值前余额（分）	
	optional int32 beforeBalance = 6;
	// 充值后余额（分）	
	optional int32 afterBalance = 7;

}
