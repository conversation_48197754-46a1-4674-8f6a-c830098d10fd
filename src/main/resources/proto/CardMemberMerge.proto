
// Generated by the mtime code generator.  DO NOT EDIT!
// Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.

option java_package = "cmc.card.basic.admin.service.dto";
option java_outer_classname = "CardMemberMergeProto";

// 会员卡信息
message	CardInfo {
	// 会员编码	
	optional string memberCode = 1;
	// 会员手机号	
	optional string memberPhoneNumber = 2;
	// 卡手机号	
	optional string cardPhoneNumber = 3;
	// 卡号	
	optional string cardNumber = 4;
	// 卡类别	
	optional string cardSort = 5;
	// 卡形式	
	optional string cardForm = 6;
	// 卡类型编码	
	optional string cardTypeCode = 7;
	// 卡类型名称	
	optional string cardTypeName = 8;
	// 卡余额(单位元)	
	optional string balance = 9;
	// 卡状态	
	optional string cardStatus = 10;
	// 有效期截止日期	
	optional int64 expiryDate = 11;
	// 密码	
	optional string password = 12;

}

// 会员卡合并快照信息
message	CardInfoSnapshot {
	// 主键ID	
	optional int64 id = 1;
	// 合并订单ID	
	optional int64 mergeId = 2;
	// 保留会员编码	
	optional string reservedMemberCode = 3;
	// 废弃会员编码	
	optional string disusedMemberCode = 4;
	// 保留会员手机号	
	optional string reservedMemberPhone = 5;
	// 废弃会员手机号	
	optional string disusedMemberPhone = 6;
	// 卡手机号	
	optional string cardPhoneNumber = 7;
	// 卡号	
	optional string cardNumber = 8;
	// 卡类别	
	optional string cardSort = 9;
	// 卡形式	
	optional string cardForm = 10;
	// 卡类型编码	
	optional string cardTypeCode = 11;
	// 卡类型名称	
	optional string cardTypeName = 12;
	// 卡余额(单位元)	
	optional string balance = 13;
	// 卡状态	
	optional string cardStatus = 14;
	// 有效期截止日期	
	optional int64 expiryDate = 15;
	// 合并状态	
	optional int32 mergeStatus = 16;
	// 备注	
	optional string remark = 17;

}

// 
message	QueryCardInfoByMemberCodesRequest {
	// 会员编号
  	repeated string memberCodes = 1;

}

// 卡查询返回结果 
message	QueryCardInfoByMemberCodesResponse {
	// 卡列表	
	repeated CardInfo cardList = 1;

}

// 
message	MergeCardMemberRequest {
	// 合并订单ID
  	required int64 mergeId = 1;
	// 保留的会员编码
  	required string reservedMemberCode = 2;
	// 废弃的会员编码
  	repeated string disusedMemberCode = 3;

}

//  
message	MergeCardMemberResponse {
	// 执行结果	
	required bool acknowledge = 1;
	// 返回失败原因	
	optional string message = 2;

}

// 
message	QueryCardMergeSnapshotsRequest {
	// 合并订单ID
  	required int64 mergeId = 1;

}

// 卡查询返回结果 
message	QueryCardMergeSnapshotsResponse {
	// 卡快照列表	
	repeated CardInfoSnapshot cardSnapshotList = 1;

}

// 
message	UpdateSnapshotMergeStatusRequest {
	// 合并订单ID
  	required int64 mergeId = 1;
	// 卡号
  	required string cardNumber = 2;
	// 合并状态
  	optional int32 mergeStatus = 3;

}

// 卡查询返回结果 
message	UpdateSnapshotMergeStatusResponse {
	// 执行结果	
	required bool acknowledge = 1;
	// 返回失败原因	
	optional string message = 2;

}
