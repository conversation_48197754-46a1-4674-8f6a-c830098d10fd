
// Generated by the mtime code generator.  DO NOT EDIT!
// Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.

option java_package = "cmc.card.basic.admin.service.dto";
option java_outer_classname = "CardAnnualFeeProto";

// 应收取年费卡类型
message	CardManageResult {
	// 卡类型ID	
	optional int64 id = 1;
	// 区域ID	
	optional string areaId = 2;
	// 卡类型编码	
	optional string cardCode = 3;
	// 卡类型	
	required int32 cardType = 4;
	// 卡类别	
	required int32 cardSort = 5;
	// 卡类型名称	
	required string cardName = 6;
	// oa编码	
	optional string oaCode = 7;
	// 负责单位	
	required string unit = 8;
	// 是否允许区域修改	
	required int32 areaUpdate = 9;
	// 有效期	
	required string validityDate = 10;
	// 免年费最低消费次数	
	required int32 annualFeeMinCount = 11;
	// 卡状态	
	required int32 businessStatus = 12;
	// 年费	
	optional int32 annualFee = 13;

}

// 卡常用信息
message	CardInfo {
	// 卡ID	
	optional string cardId = 1;
	// 手机号	
	optional string phoneNo = 2;
	// 密码	
	optional string passWord = 3;
	// 卡的明文卡号	
	optional string passWordClear = 4;
	// 用户名	
	optional string userName = 5;
	// 卡类型	
	optional int32 cardType = 6;
	// 卡类别	
	optional int32 cardSort = 7;
	// 是否是实体卡	
	optional int32 ifEntity = 8;
	// 区域ID	
	optional string areaId = 9;
	// 影院内码	
	optional string cinemaInnerCode = 10;
	// 卡余额	
	optional int32 balance = 11;
	// 是否允许网购	
	optional int32 canuseOnLine = 12;
	// 卡状态	
	optional int32 cardState = 13;
	// 有效期截止日期	
	optional int64 effectiveEnd = 14;
	// 卡类型ID	
	optional int64 cardTypeNo = 15;
	// 消费次数	
	optional int32 spendTimes = 16;
	// 影院名称	
	optional string cinemaName = 17;
	// 是否是刮刮卡初始密码	
	optional string useOriginPwd = 18;
	// 销售单id	
	optional string sellOrderId = 19;
	// 预冻总金额	
	optional int32 txTransBal = 20;
	// 发卡时间	
	optional int64 issueTime = 21;
	// 最后更新时间	
	optional int64 lastTime = 22;
	// 版本号	
	optional int32 version = 23;

}

// 年费未扣减记录
message	AnnualFeeDeductRecord {
	// id	
	optional int64 id = 1;
	// 卡号	
	optional string cardId = 2;
	// 手机号	
	optional string phoneNo = 3;
	// 扣减年度	
	optional string deductYear = 4;
	// 卡类型ID	
	optional int64 cardManageId = 5;
	// 卡类型名称	
	optional string cardManageName = 6;
	// 免年费消费次数	
	optional int32 annualFeeMinCount = 7;
	// 年费	
	optional int32 annualFee = 8;
	// 实际消费次数	
	optional int32 consumeCount = 9;
	// 卡余额	
	optional int32 balance = 10;
	// 收取状态	
	optional int32 status = 11;
	// 创建时间	
	optional int64 createTime = 12;
	// 更新时间	
	optional int64 updateTime = 13;

}

// 
message	DeductAnnualFeeRequest {
	// 卡类型
  	required CardManageResult cardManage = 1;
	// 卡信息
  	required CardInfo cardInfo = 2;
	// 实际消费次数
  	required int32 spendTimes = 3;
	// 扣减年度
  	optional string deductYear = 4;

}

//  
message	DeductAnnualFeeResponse {
	// 返回结果	
	required bool flag = 1;
	// 失败详情	
	optional string msg = 2;

}

// 
message	FindAnnualFeeDeductRecordByPageRequest {
	// 状态
  	optional int32 state = 1;
	// 卡类型Id
  	optional string cardManageId = 2;
	// 卡号
  	optional string cardId = 3;
	// 手机号
  	optional string phoneNo = 4;
	// 扣减年度
  	optional string deductYear = 5;
	// 分页信息
  	required PageInfo pageInfo = 6;

}

//  
message	FindAnnualFeeDeductRecordByPageResponse {
	// 返回结果	
	repeated AnnualFeeDeductRecord annualFeeDeductRecordList = 1;
	// 总条数	
	required int32 totalCount = 2;

}

// 
message	FindRecordByPageRequest {
	// 状态
  	required int32 state = 1;
	// 分页信息
  	required PageInfo pageInfo = 2;

}

//  
message	FindRecordByPageResponse {
	// 返回结果	
	repeated AnnualFeeDeductRecord annualFeeDeductRecordList = 1;

}

// 
message	UpdateStateRequest {
	// 状态
  	required int32 state = 1;
	// id
  	required int64 id = 2;
	// 卡号
  	required string cardId = 3;

}

//  
message	UpdateStateResponse {
	// 操作结果	
	required bool flag = 1;
	// 异常信息	
	optional string msg = 2;

}

// 
message	DeductAnnualFeeOfRecordRequest {
	// 未扣减年费记录
  	required AnnualFeeDeductRecord annualFeeDeductRecord = 1;

}

//  
message	DeductAnnualFeeOfRecordResponse {
	// 返回结果	
	required bool flag = 1;
	// 失败详情	
	optional string msg = 2;

}

// 
message	FindRecordByCardIdRequest {
	// 卡号
  	required string cardId = 1;

}

//  
message	FindRecordByCardIdResponse {
	// 返回结果	
	repeated AnnualFeeDeductRecord annualFeeDeductRecordList = 1;

}

// 
message	FindRecordByPageAndDateRequest {
	// 开始时间
  	optional int64 createTimeStartDate = 1;
	// 结束时间
  	optional int64 createTimeEndDate = 2;
	// 页数
  	required int32 pageIndex = 3;
	// 每页的数量
  	required int32 pageSize = 4;

}

//  
message	FindRecordByPageAndDateResponse {
	// 返回结果	
	repeated AnnualFeeDeductRecord annualFeeDeductRecordList = 1;

}
