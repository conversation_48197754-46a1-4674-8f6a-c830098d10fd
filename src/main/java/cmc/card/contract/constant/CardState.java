// Generated by the lark-cli v0.8.2.  DO NOT EDIT!
// Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.
package cmc.card.contract.constant;

import wanda.stark.core.lang.EnumDisplayNameSupport;
import wanda.stark.core.lang.EnumValueSupport;
import wanda.stark.core.lang.Enums;
import wanda.stark.core.lang.Description;

/**
 * 卡状态
 */
@Description("卡状态")
public enum CardState implements EnumValueSupport, EnumDisplayNameSupport {
    /**
     * 已预制未激活
     */
    PREFABRICATED(1, "已预制未激活", "I"),
    /**
     * 正常
     */
    AVAILABLE(2, "正常", "O"),
    /**
     * 禁用
     */
    DISABLE(3, "禁用", "D"),
    /**
     * 预制卡失败
     */
    PREFABRICATEDFIELD(4, "预制卡失败", "Unknown"),
    /**
     * 已丢失不可用
     */
    REPORTLOSS(5, "已丢失不可用", "L"),
    /**
     * 已补卡不可用
     */
    FILLCARD(6, "已补卡不可用", "N"),
    /**
     * 已换卡不可用
     */
    CHANGEDCARD(7, "已换卡不可用", "R"),
    /**
     * 已退卡不可用
     */
    REFUNDEDCARD(8, "已退卡不可用", "T"),
    /**
     * 已升级不可用
     */
    UPGRADECARD(9, "已升级不可用", "U"),
    /**
     * 已销卡不可用
     */
    CANCELCARD(10, "已销卡不可用", "X"),
    /**
     * 过期
     */
    EXPIRED(11, "过期", "E"),
    /**
     * 未激活
     */
    NOTACTIVE(12, "未激活", "W"),
    /**
     * 已冻结
     */
    FROZEN(13, "已冻结", "F"),
    /**
     * 无状态
     */
    NULL(14, "无状态", "Unknown"),
    /**
     * 查无此卡
     */
    NOCARD(15, "查无此卡", "Unknown"),
    /**
     * 不可用
     */
    NA(16, "不可用", "Unknown");

    private int value;
    private String displayName;
    private String terminalName;

    CardState(int value, String displayName, String terminalName) {
        this.value = value;
        this.displayName = displayName;
        this.terminalName = terminalName;
    }

    /**
     * 获取枚举的 int 值,用于数据保存以及序列化
     *
     * @return 枚举的 int 值
     */
    @Override
    public int value() {
        return this.value;
    }

    /**
     * 获取枚举的显示名称
     *
     * @return 枚举的显示名称
     */
    @Override
    public String displayName() {
        return this.displayName;
    }

    /**
     * @return 终端名称
     */
    public String terminalName() {
        return this.terminalName;
    }

    /**
     * 根据 int 值构建一个枚举对象
     *
     * @param value 需要构建枚举的 int 的值
     * @return 返回相应 value 值的枚举对象
     */
    public static CardState valueOf(int value) {
        return Enums.valueOf(CardState.class, value);
    }
}
