// Generated by the lark-cli v0.8.2.  DO NOT EDIT!
// Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.
package cmc.card.contract.constant;

import wanda.stark.core.lang.EnumDisplayNameSupport;
import wanda.stark.core.lang.EnumValueSupport;
import wanda.stark.core.lang.Enums;
import wanda.stark.core.lang.Description;

/**
 * 卡类别
 */
@Description("卡类别")
public enum CardSort implements EnumValueSupport, EnumDisplayNameSupport {
    /**
     * 储值卡
     */
    PETCARD(10, "储值卡"),
    /**
     * 记次卡
     */
    COUNTCARD(20, "记次卡"),
    /**
     * 权益卡
     */
    EQUITYCARD(30, "权益卡"),
    /**
     * 影票计次卡
     */
    GIFT_COUNT_CARD(40, "影票计次卡");

    private int value;
    private String displayName;

    private CardSort(int value, String displayName) {
        this.value = value;
        this.displayName = displayName;
    }

    /**
     * 获取枚举的 int 值,用于数据保存以及序列化
     *
     * @return 枚举的 int 值
     */
    @Override
    public int value() {
        return this.value;
    }

    /**
     * 获取枚举的显示名称
     *
     * @return 枚举的显示名称
     */
    @Override
    public String displayName() {
        return this.displayName;
    }
    
    /**
     * 根据 int 值构建一个枚举对象
     *
     * @param value 需要构建枚举的 int 的值
     * @return 返回相应 value 值的枚举对象
     */
    public static CardSort valueOf(int value) {
        return Enums.valueOf(CardSort.class, value);
    }
}
