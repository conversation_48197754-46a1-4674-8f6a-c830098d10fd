// Generated by the lark-cli v0.8.2.  DO NOT EDIT!
// Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.
package cmc.card.contract.constant;

import wanda.stark.core.lang.EnumDisplayNameSupport;
import wanda.stark.core.lang.EnumValueSupport;
import wanda.stark.core.lang.Enums;
import wanda.stark.core.lang.Description;

/**
 * 是否允许网购
 */
@Description("是否允许网购")
public enum CanUseOnline implements EnumValueSupport, EnumDisplayNameSupport {
    /**
     * 允许网购
     */
    YES(1, "允许网购"),
    /**
     * 不允许网购
     */
    NO(2, "不允许网购");

    private int value;
    private String displayName;

    private CanUseOnline(int value, String displayName) {
        this.value = value;
        this.displayName = displayName;
    }

    /**
     * 获取枚举的 int 值,用于数据保存以及序列化
     *
     * @return 枚举的 int 值
     */
    @Override
    public int value() {
        return this.value;
    }

    /**
     * 获取枚举的显示名称
     *
     * @return 枚举的显示名称
     */
    @Override
    public String displayName() {
        return this.displayName;
    }
    
    /**
     * 根据 int 值构建一个枚举对象
     *
     * @param value 需要构建枚举的 int 的值
     * @return 返回相应 value 值的枚举对象
     */
    public static CanUseOnline valueOf(int value) {
        return Enums.valueOf(CanUseOnline.class, value);
    }
}
