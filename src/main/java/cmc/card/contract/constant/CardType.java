// Generated by the lark-cli v0.8.2.  DO NOT EDIT!
// Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.
package cmc.card.contract.constant;

import wanda.stark.core.lang.EnumDisplayNameSupport;
import wanda.stark.core.lang.EnumValueSupport;
import wanda.stark.core.lang.Enums;
import wanda.stark.core.lang.Description;

/**
 * 卡类型级别
 */
@Description("卡类型级别")
public enum CardType implements EnumValueSupport, EnumDisplayNameSupport {
    /**
     * 院线级
     */
    MEDIALEVEL(10, "院线级", "G"),
    /**
     * 区域级
     */
    AREALEVEL(20, "区域级", "R");

    private int value;
    private String displayName;
    private String terminalName;

    private CardType(int value, String displayName, String terminalName) {
        this.value = value;
        this.displayName = displayName;
        this.terminalName = terminalName;
    }

    /**
     * 获取枚举的 int 值,用于数据保存以及序列化
     *
     * @return 枚举的 int 值
     */
    @Override
    public int value() {
        return this.value;
    }

    /**
     * 获取枚举的显示名称
     *
     * @return 枚举的显示名称
     */
    @Override
    public String displayName() {
        return this.displayName;
    }

    /**
     * @return 终端名称
     */
    public String terminalName() {
        return this.terminalName;
    }
    
    /**
     * 根据 int 值构建一个枚举对象
     *
     * @param value 需要构建枚举的 int 的值
     * @return 返回相应 value 值的枚举对象
     */
    public static CardType valueOf(int value) {
        return Enums.valueOf(CardType.class, value);
    }
}
