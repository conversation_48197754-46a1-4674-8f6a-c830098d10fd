
// Generated by the mtime code generator.  DO NOT EDIT!
// Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.

package cmc.card.basic.admin.service.iface;

import cmc.card.basic.admin.service.RpcAutoConfiguration;
import cmc.card.basic.admin.service.dto.CardQueryAdminDto.*;
import mtime.lark.net.rpc.annotation.RpcService;
import mtime.lark.util.Profiles;

/**
 *
 * mscgenVersion:  0.5.4
 */
@RpcService(profile = Profiles.CMC, server = RpcAutoConfiguration.SERVER)
public interface CardQueryAdminService {

    /**
	 * 查询卡信息
     * @param request
     * @return
	 */
    CardInfoQueryByDBResponse cardInfoQueryByDB(CardInfoQueryByDBRequest request);
    /**
	 * 根据卡号查询卡面名称
     * @param request
     * @return
	 */
    GetCardCoverNameByCardIdResponse getCardCoverNameByCardId(GetCardCoverNameByCardIdRequest request);
    /**
	 * 根据卡号查询不是未激活的卡
     * @param request
     * @return
	 */
    GetNotNoActiveCardListResponse getNotNoActiveCardList(GetNotNoActiveCardListRequest request);
    /**
	 * 批量查询卡信息销售单列表
     * @param request
     * @return
	 */
    QueryCardInfoListResponse queryCardInfoList(QueryCardInfoListRequest request);
    /**
	 * 查询未激活卡
     * @param request
     * @return
	 */
    QueryNoActiveAndHaveBalanceCardsResponse queryNoActiveAndHaveBalanceCards(QueryNoActiveAndHaveBalanceCardsRequest request);
    /**
	 * 查询未激活卡销售单
     * @param request
     * @return
	 */
    QueryNoActiveHaveBalanceCardAndSellOrderIdResponse queryNoActiveHaveBalanceCardAndSellOrderId(QueryNoActiveHaveBalanceCardAndSellOrderIdRequest request);
    /**
	 * 查询卡销售单列表
     * @param request
     * @return
	 */
    QuerySellOrderListResponse querySellOrderList(QuerySellOrderListRequest request);
}
