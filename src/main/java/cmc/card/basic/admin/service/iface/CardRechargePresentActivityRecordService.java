
// Generated by the mtime code generator.  DO NOT EDIT!
// Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.

package cmc.card.basic.admin.service.iface;

import cmc.card.basic.admin.service.RpcAutoConfiguration;
import cmc.card.basic.admin.service.dto.CardRechargePresentActivityRecordDto.*;
import mtime.lark.net.rpc.annotation.RpcService;
import mtime.lark.util.Profiles;

/**
 * 卡充赠活动记录
 * mscgenVersion:  0.5.4
 */
@RpcService(profile = Profiles.CMC, server = RpcAutoConfiguration.SERVER)
public interface CardRechargePresentActivityRecordService {
    /**
	 * 根据游标id查询列表
     * @param request
     * @return
	 */
    ListByScrollIdResponse listByScrollId(ListByScrollIdRequest request);
    /**
	 * 记录分页列表
     * @param request
     * @return
	 */
    ListPageResponse listPage(ListPageRequest request);
}
