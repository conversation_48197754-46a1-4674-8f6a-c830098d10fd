
// Generated by the mtime code generator.  DO NOT EDIT!
// Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.

package cmc.card.basic.admin.service.iface;

import cmc.card.basic.admin.service.RpcAutoConfiguration;
import cmc.card.basic.admin.service.dto.CardInfoAdminV2Dto.*;
import mtime.lark.net.rpc.annotation.RpcService;
import mtime.lark.util.Profiles;

/**
 * 卡信息后台服务
 * mscgenVersion:  0.5.4
 */
@RpcService(profile = Profiles.CMC, server = RpcAutoConfiguration.SERVER)
public interface CardInfoAdminV2Service {
    /**
	 * 计算卡赠送余额
     * @param request
     * @return
	 */
    CalculatePresentBalanceResponse calculatePresentBalance(CalculatePresentBalanceRequest request);
    /**
	 * 计算新储值卡某时间赠送余额
     * @param request
     * @return
	 */
    CalculatePresentBalanceSnapshootResponse calculatePresentBalanceSnapshoot(CalculatePresentBalanceSnapshootRequest request);
    /**
	 * 根据时间分页获取卡列表
     * @param request
     * @return
	 */
    FindCardByTimeWithPageResponse findCardByTimeWithPage(FindCardByTimeWithPageRequest request);
    /**
	 * 查询卡充值订单信息
     * @param request
     * @return
	 */
    FindCardRechargeOrderInfoResponse findCardRechargeOrderInfo(FindCardRechargeOrderInfoRequest request);
    /**
	 * 根据卡号查询卡类型和卡面编码
     * @param request
     * @return
	 */
    FindCardTypeInfoListResponse findCardTypeInfoList(FindCardTypeInfoListRequest request);
    /**
	 * 查询卡基本信息
     * @param request
     * @return
	 */
    GetCardInfoResponse getCardInfo(GetCardInfoRequest request);
    /**
	 * 页面负充
     * @param request
     * @return
	 */
    NegativeRechargeResponse negativeRecharge(NegativeRechargeRequest request);
    /**
	 * 修改卡状态
     * @param request
     * @return
	 */
    UpdateCardStateResponse updateCardState(UpdateCardStateRequest request);
    /**
	 * 排量修改卡状态
     * @param request
     * @return
	 */
    UpdateCardStateInBatchesResponse updateCardStateInBatches(UpdateCardStateInBatchesRequest request);
    /**
	 * 修改卡赠送余额
     * @param request
     * @return
	 */
    UpdatePresentBalanceResponse updatePresentBalance(UpdatePresentBalanceRequest request);
}
