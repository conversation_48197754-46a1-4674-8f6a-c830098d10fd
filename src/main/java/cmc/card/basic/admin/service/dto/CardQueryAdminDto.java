package cmc.card.basic.admin.service.dto;

import cmc.card.contract.constant.*;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import mtime.lark.pb.FieldType;
import mtime.lark.pb.annotation.ProtoField;
import mtime.lark.pb.annotation.ProtoMessage;
import mtime.lark.pb.data.PageInfo;

/**
 * Dto
 * mscgenVersion: 0.5.4
 */
public class CardQueryAdminDto {

    private CardQueryAdminDto() {

    }

    /**
     * NoActiveCardSellOrder
     */
    @Setter
    @Getter
    @ToString
    @ProtoMessage(description = "")
    public static class NoActiveCardSellOrder {
        /**
         * 批量发卡销售单
         */
        @ProtoField(order = 1, type = FieldType.STRING, required = true, description = "批量发卡销售单")
        private String sellOrderId;
        /**
         * 未激活卡数量
         */
        @ProtoField(order = 2, type = FieldType.INT32, required = true, description = "未激活卡数量")
        private int noActiveCardNum;
        /**
         * 附加前缀销售单
         */
        @ProtoField(order = 3, type = FieldType.STRING, required = false, description = "附加前缀销售单")
        private String sellOrderIdAttachPrefix;


    }


    /**
     * 未激活卡信息
     */
    @Setter
    @Getter
    @ToString
    @ProtoMessage(description = "未激活卡信息")
    public static class NoActiveCardInfo {
        /**
         * 卡号
         */
        @ProtoField(order = 1, type = FieldType.STRING, required = true, description = "卡号")
        private String cardId;
        /**
         * 卡类型名
         */
        @ProtoField(order = 2, type = FieldType.STRING, required = true, description = "卡类型名")
        private String cardTypeName;
        /**
         * 单张卡负充金额
         */
        @ProtoField(order = 3, type = FieldType.INT32, required = true, description = "单张卡负充金额")
        private int cardBalance;
        /**
         * 是否选中
         */
        @ProtoField(order = 4, type = FieldType.INT32, required = true, description = "是否选中")
        private int isSelect;


    }


    /**
     * 销售单
     */
    @Setter
    @Getter
    @ToString
    @ProtoMessage(description = "销售单")
    public static class SellOrder {
        /**
         * 销售单id
         */
        @ProtoField(order = 1, type = FieldType.STRING, required = false, description = "销售单id")
        private String sellOrderId;
        /**
         * 未激活卡总数
         */
        @ProtoField(order = 2, type = FieldType.INT32, required = false, description = "未激活卡总数")
        private int noActiveCardNum;
        /**
         * 未激活卡总价格
         */
        @ProtoField(order = 3, type = FieldType.INT32, required = false, description = "未激活卡总价格")
        private int noActiveCardTotalPrice;


    }


    /**
     * 卡查询参数
     */
    @Setter
    @Getter
    @ToString
    @ProtoMessage(description = "卡查询参数")
    public static class QueryParam {
        /**
         * 卡ID
         */
        @ProtoField(order = 1, type = FieldType.STRING, required = false, description = "卡ID")
        private String cardId;
        /**
         * 手机号
         */
        @ProtoField(order = 2, type = FieldType.STRING, required = false, description = "手机号")
        private String phoneNo;
        /**
         * 用户名
         */
        @ProtoField(order = 3, type = FieldType.STRING, required = false, description = "用户名")
        private String userName;
        /**
         * 卡状态
         */
        @ProtoField(order = 4, type = FieldType.INT32, required = false, description = "卡状态")
        private int cardStateValue;
        /**
         * 是否是实体卡
         */
        @ProtoField(order = 5, type = FieldType.ENUM, required = false, description = "是否是实体卡")
        private IfEntity ifEntity;
        /**
         * 证件类型值
         */
        @ProtoField(order = 6, type = FieldType.INT32, required = false, description = "证件类型值")
        private int certificatesType;
        /**
         * 证件号
         */
        @ProtoField(order = 7, type = FieldType.STRING, required = false, description = "证件号")
        private String certificatesNo;
        /**
         * 是否是刮刮卡
         */
        @ProtoField(order = 8, type = FieldType.STRING, required = false, description = "是否是刮刮卡")
        private String yesOrNoGuaGuaCard;
        /**
         * QQ号
         */
        @ProtoField(order = 9, type = FieldType.STRING, required = false, description = "QQ号")
        private String qq;
        /**
         * 影院内码
         */
        @ProtoField(order = 10, type = FieldType.STRING, required = false, description = "影院内码")
        private String cinemaInnerCode;
        /**
         * 区域ID
         */
        @ProtoField(order = 11, type = FieldType.STRING, required = false, description = "区域ID")
        private String areaId;


    }


    /**
     * 简单卡查询信息对象
     */
    @Setter
    @Getter
    @ToString
    @ProtoMessage(description = "简单卡查询信息对象")
    public static class SimpleCardInfo {
        /**
         * 卡ID
         */
        @ProtoField(order = 1, type = FieldType.STRING, required = false, description = "卡ID")
        private String cardId;
        /**
         * 卡类型编码
         */
        @ProtoField(order = 2, type = FieldType.STRING, required = false, description = "卡类型编码")
        private String cardTypeCode;
        /**
         * 卡类型名称
         */
        @ProtoField(order = 3, type = FieldType.STRING, required = false, description = "卡类型名称")
        private String cardTypeName;
        /**
         * 卡状态
         */
        @ProtoField(order = 4, type = FieldType.ENUM, required = false, description = "卡状态")
        private CardState cardState;


    }


    /**
     * 销售单卡信息
     */
    @Setter
    @Getter
    @ToString
    @ProtoMessage(description = "销售单卡信息")
    public static class SellOrderCardInfo {
        /**
         * 卡ID
         */
        @ProtoField(order = 1, type = FieldType.STRING, required = false, description = "卡ID")
        private String cardId;
        /**
         * 卡类型Id
         */
        @ProtoField(order = 2, type = FieldType.INT64, required = false, description = "卡类型Id")
        private long cardTypeNo;
        /**
         * 卡状态
         */
        @ProtoField(order = 3, type = FieldType.ENUM, required = false, description = "卡状态")
        private CardState cardState;
        /**
         * 销售单Id
         */
        @ProtoField(order = 4, type = FieldType.STRING, required = false, description = "销售单Id")
        private String sellOrderId;


    }


    /**
     * 销售单信息
     */
    @Setter
    @Getter
    @ToString
    @ProtoMessage(description = "销售单信息")
    public static class SellOrderInfo {
        /**
         * 销售单Id
         */
        @ProtoField(order = 1, type = FieldType.STRING, required = false, description = "销售单Id")
        private String sellOrderId;
        /**
         * 卡类型Id
         */
        @ProtoField(order = 2, type = FieldType.INT64, required = false, description = "卡类型Id")
        private long cardTypeNo;
        /**
         * 卡类型编码
         */
        @ProtoField(order = 3, type = FieldType.STRING, required = false, description = "卡类型编码")
        private String cardCode;
        /**
         * 客户编码
         */
        @ProtoField(order = 4, type = FieldType.STRING, required = false, description = "客户编码")
        private String customerId;
        /**
         * 客户名称
         */
        @ProtoField(order = 5, type = FieldType.STRING, required = false, description = "客户名称")
        private String customerName;


    }


    /**
     * 卡查询信息对象
     */
    @Setter
    @Getter
    @ToString
    @ProtoMessage(description = "卡查询信息对象")
    public static class QueryCardInfo {
        /**
         * 卡ID
         */
        @ProtoField(order = 1, type = FieldType.STRING, required = false, description = "卡ID")
        private String cardId;
        /**
         * 手机号
         */
        @ProtoField(order = 2, type = FieldType.STRING, required = false, description = "手机号")
        private String phoneNo;
        /**
         * 用户名
         */
        @ProtoField(order = 3, type = FieldType.STRING, required = false, description = "用户名")
        private String userName;
        /**
         * 密码
         */
        @ProtoField(order = 4, type = FieldType.STRING, required = false, description = "密码")
        private String passWord;
        /**
         * 区域ID
         */
        @ProtoField(order = 6, type = FieldType.STRING, required = false, description = "区域ID")
        private String areaId;
        /**
         * 影院内码
         */
        @ProtoField(order = 7, type = FieldType.STRING, required = false, description = "影院内码")
        private String cinemaInnerCode;
        /**
         * 影院名称
         */
        @ProtoField(order = 8, type = FieldType.STRING, required = false, description = "影院名称")
        private String cinemaName;
        /**
         * 卡状态
         */
        @ProtoField(order = 9, type = FieldType.ENUM, required = false, description = "卡状态")
        private CardState cardState;
        /**
         * 卡类别
         */
        @ProtoField(order = 11, type = FieldType.ENUM, required = false, description = "卡类别")
        private CardSort cardSort;
        /**
         * 是否是实体卡
         */
        @ProtoField(order = 12, type = FieldType.ENUM, required = false, description = "是否是实体卡")
        private IfEntity ifEntity;
        /**
         * 卡余额
         */
        @ProtoField(order = 13, type = FieldType.INT32, required = false, description = "卡余额")
        private int balance;
        /**
         * 卡余次
         */
        @ProtoField(order = 14, type = FieldType.INT32, required = false, description = "卡余次")
        private int balNum;
        /**
         * 有效期截止日期
         */
        @ProtoField(order = 15, type = FieldType.INT64, required = false, description = "有效期截止日期")
        private LocalDateTime effectiveEnd;
        /**
         * 卡类型id
         */
        @ProtoField(order = 16, type = FieldType.INT64, required = false, description = "卡类型id")
        private long cardTypeNo;
        /**
         * 卡类型名称
         */
        @ProtoField(order = 18, type = FieldType.STRING, required = false, description = "卡类型名称")
        private String cardTypeName;
        /**
         * 卡类型状态
         */
        @ProtoField(order = 19, type = FieldType.STRING, required = false, description = "卡类型状态")
        private String cardTypeState;
        /**
         * 证件类型值
         */
        @ProtoField(order = 20, type = FieldType.INT32, required = false, description = "证件类型值")
        private int certificatesType;
        /**
         * 证件类型名称
         */
        @ProtoField(order = 21, type = FieldType.STRING, required = false, description = "证件类型名称")
        private String certificatesTypeName;
        /**
         * 证件号
         */
        @ProtoField(order = 22, type = FieldType.STRING, required = false, description = "证件号")
        private String certificatesNo;
        /**
         * 邮箱
         */
        @ProtoField(order = 23, type = FieldType.STRING, required = false, description = "邮箱")
        private String email;
        /**
         * QQ号
         */
        @ProtoField(order = 24, type = FieldType.STRING, required = false, description = "QQ号")
        private String qq;
        /**
         * 是否是刮刮卡
         */
        @ProtoField(order = 25, type = FieldType.STRING, required = false, description = "是否是刮刮卡")
        private String isScratchCard;
        /**
         * 邮编
         */
        @ProtoField(order = 26, type = FieldType.STRING, required = false, description = "邮编")
        private String postCode;


    }


    /**
     * 卡查询信息对象
     */
    @Setter
    @Getter
    @ToString
    @ProtoMessage(description = "卡查询信息对象")
    public static class CardBaseInfo {
        /**
         * 卡ID
         */
        @ProtoField(order = 1, type = FieldType.STRING, required = false, description = "卡ID")
        private String cardId;
        /**
         * 手机号
         */
        @ProtoField(order = 2, type = FieldType.STRING, required = false, description = "手机号")
        private String phoneNo;
        /**
         * 用户名
         */
        @ProtoField(order = 3, type = FieldType.STRING, required = false, description = "用户名")
        private String userName;
        /**
         * 卡类型
         */
        @ProtoField(order = 4, type = FieldType.INT32, required = false, description = "卡类型")
        private int cardType;
        /**
         * 是否是实体卡
         */
        @ProtoField(order = 5, type = FieldType.INT32, required = false, description = "是否是实体卡")
        private int ifEntity;
        /**
         * 区域ID
         */
        @ProtoField(order = 6, type = FieldType.STRING, required = false, description = "区域ID")
        private String areaId;
        /**
         * 影院内码
         */
        @ProtoField(order = 7, type = FieldType.STRING, required = false, description = "影院内码")
        private String cinemaInnerCode;
        /**
         * 卡状态名称
         */
        @ProtoField(order = 8, type = FieldType.INT32, required = false, description = "卡状态名称")
        private int cardState;
        /**
         * 有效期截止日期
         */
        @ProtoField(order = 9, type = FieldType.INT64, required = false, description = "有效期截止日期")
        private LocalDateTime effectiveEnd;
        /**
         * 卡类型id
         */
        @ProtoField(order = 10, type = FieldType.INT64, required = false, description = "卡类型id")
        private long cardTypeNo;
        /**
         * 卡类别
         */
        @ProtoField(order = 11, type = FieldType.INT32, required = false, description = "卡类别")
        private int cardSort;
        /**
         * 卡余额
         */
        @ProtoField(order = 12, type = FieldType.INT32, required = false, description = "卡余额")
        private int balance;
        /**
         * 密码
         */
        @ProtoField(order = 13, type = FieldType.STRING, required = false, description = "密码")
        private String passWord;
        /**
         * 明文密码
         */
        @ProtoField(order = 14, type = FieldType.STRING, required = false, description = "明文密码")
        private String passWordClear;
        /**
         * 卡面编码
         */
        @ProtoField(order = 15, type = FieldType.STRING, required = false, description = "卡面编码")
        private String coverCode;


    }


    /**
     * QueryNoActiveAndHaveBalanceCards 请求参数
     */
    @Setter
    @Getter
    @ToString
    @ProtoMessage(description = "QueryNoActiveAndHaveBalanceCards 请求参数")
    public static class QueryNoActiveAndHaveBalanceCardsRequest {
        /**
         * 批量发卡销售单
         */
        @ProtoField(order = 1, type = FieldType.STRING, required = true, description = "批量发卡销售单")
        private String sellOrderId;


    }


    /**
     * QueryNoActiveAndHaveBalanceCards 响应结果
     */
    @Setter
    @Getter
    @ToString
    @ProtoMessage(description = "QueryNoActiveAndHaveBalanceCards 响应结果")
    public static class QueryNoActiveAndHaveBalanceCardsResponse {
        /**
         * 订单汇总信息
         */
        @ProtoField(order = 1, type = FieldType.OBJECT, required = true, description = "订单汇总信息")
        private SellOrder order;
        /**
         * 未激活卡列表
         */
        @ProtoField(order = 2, type = FieldType.OBJECT, required = false, description = "未激活卡列表")
        private List<NoActiveCardInfo> cardInfoList;


        /**
         * add item method
         * @param item
         */
        public void addCardInfoList(NoActiveCardInfo item) {
            ensureCardInfoList();
            this.cardInfoList.add(item);
        }

        /**
         * add collection method
         * @param items
         */
        public void addCardInfoList(Collection<? extends NoActiveCardInfo> items) {
            ensureCardInfoList();
            this.cardInfoList.addAll(items);
        }

        private void ensureCardInfoList() {
            if (cardInfoList == null) {
                this.cardInfoList = new ArrayList<>();
            }
        }


    }


    /**
     * QueryNoActiveHaveBalanceCardAndSellOrderId 请求参数
     */
    @Setter
    @Getter
    @ToString
    @ProtoMessage(description = "QueryNoActiveHaveBalanceCardAndSellOrderId 请求参数")
    public static class QueryNoActiveHaveBalanceCardAndSellOrderIdRequest {
        /**
         * 批量发卡销售单
         */
        @ProtoField(order = 1, type = FieldType.STRING, required = true, description = "批量发卡销售单")
        private String cinemaInnerCode;


    }


    /**
     * QueryNoActiveHaveBalanceCardAndSellOrderId 响应结果
     */
    @Setter
    @Getter
    @ToString
    @ProtoMessage(description = "QueryNoActiveHaveBalanceCardAndSellOrderId 响应结果")
    public static class QueryNoActiveHaveBalanceCardAndSellOrderIdResponse {
        /**
         * 未激活卡销售单
         */
        @ProtoField(order = 1, type = FieldType.OBJECT, required = false, description = "未激活卡销售单")
        private List<NoActiveCardSellOrder> noActiveCardSellOrder;


        /**
         * add item method
         * @param item
         */
        public void addNoActiveCardSellOrder(NoActiveCardSellOrder item) {
            ensureNoActiveCardSellOrder();
            this.noActiveCardSellOrder.add(item);
        }

        /**
         * add collection method
         * @param items
         */
        public void addNoActiveCardSellOrder(Collection<? extends NoActiveCardSellOrder> items) {
            ensureNoActiveCardSellOrder();
            this.noActiveCardSellOrder.addAll(items);
        }

        private void ensureNoActiveCardSellOrder() {
            if (noActiveCardSellOrder == null) {
                this.noActiveCardSellOrder = new ArrayList<>();
            }
        }


    }


    /**
     * GetNotNoActiveCardList 请求参数
     */
    @Setter
    @Getter
    @ToString
    @ProtoMessage(description = "GetNotNoActiveCardList 请求参数")
    public static class GetNotNoActiveCardListRequest {
        /**
         * 根据卡id查询
         */
        @ProtoField(order = 1, type = FieldType.STRING, required = false, description = "根据卡id查询")
        private List<String> cardId;


        /**
         * add item method
         * @param item
         */
        public void addCardId(String item) {
            ensureCardId();
            this.cardId.add(item);
        }

        /**
         * add collection method
         * @param items
         */
        public void addCardId(Collection<? extends String> items) {
            ensureCardId();
            this.cardId.addAll(items);
        }

        private void ensureCardId() {
            if (cardId == null) {
                this.cardId = new ArrayList<>();
            }
        }


    }


    /**
     * GetNotNoActiveCardList 响应结果
     */
    @Setter
    @Getter
    @ToString
    @ProtoMessage(description = "GetNotNoActiveCardList 响应结果")
    public static class GetNotNoActiveCardListResponse {
        /**
         * 不是未激活的卡号
         */
        @ProtoField(order = 1, type = FieldType.STRING, required = false, description = "不是未激活的卡号")
        private List<String> cardId;


        /**
         * add item method
         * @param item
         */
        public void addCardId(String item) {
            ensureCardId();
            this.cardId.add(item);
        }

        /**
         * add collection method
         * @param items
         */
        public void addCardId(Collection<? extends String> items) {
            ensureCardId();
            this.cardId.addAll(items);
        }

        private void ensureCardId() {
            if (cardId == null) {
                this.cardId = new ArrayList<>();
            }
        }


    }


    /**
     * 卡查询返回结果
     */
    @Setter
    @Getter
    @ToString
    @ProtoMessage(description = "卡查询返回结果")
    public static class CardInfoQueryResponse {
        /**
         * 返回卡信息列表
         */
        @ProtoField(order = 1, type = FieldType.OBJECT, required = false, description = "返回卡信息列表")
        private List<QueryCardInfo> queryCardInfo;
        /**
         * 总记录数
         */
        @ProtoField(order = 2, type = FieldType.INT32, required = true, description = "总记录数")
        private int total;


        /**
         * add item method
         * @param item
         */
        public void addQueryCardInfo(QueryCardInfo item) {
            ensureQueryCardInfo();
            this.queryCardInfo.add(item);
        }

        /**
         * add collection method
         * @param items
         */
        public void addQueryCardInfo(Collection<? extends QueryCardInfo> items) {
            ensureQueryCardInfo();
            this.queryCardInfo.addAll(items);
        }

        private void ensureQueryCardInfo() {
            if (queryCardInfo == null) {
                this.queryCardInfo = new ArrayList<>();
            }
        }


    }


    /**
     * GetCardCoverNameByCardId 请求参数
     */
    @Setter
    @Getter
    @ToString
    @ProtoMessage(description = "GetCardCoverNameByCardId 请求参数")
    public static class GetCardCoverNameByCardIdRequest {
        /**
         * 卡号
         */
        @ProtoField(order = 1, type = FieldType.STRING, required = true, description = "卡号")
        private String cardId;


    }


    /**
     * 返回结果
     */
    @Setter
    @Getter
    @ToString
    @ProtoMessage(description = "返回结果")
    public static class GetCardCoverNameByCardIdResponse {
        /**
         * 卡号
         */
        @ProtoField(order = 1, type = FieldType.STRING, required = true, description = "卡号")
        private String cardId;
        /**
         * 卡面编码
         */
        @ProtoField(order = 2, type = FieldType.STRING, required = false, description = "卡面编码")
        private String coverCode;
        /**
         * 卡面名称
         */
        @ProtoField(order = 3, type = FieldType.STRING, required = false, description = "卡面名称")
        private String coverName;


    }


    /**
     * CardInfoQueryByDB 请求参数
     */
    @Setter
    @Getter
    @ToString
    @ProtoMessage(description = "CardInfoQueryByDB 请求参数")
    public static class CardInfoQueryByDBRequest {
        /**
         * 卡号list
         */
        @ProtoField(order = 1, type = FieldType.STRING, required = false, description = "卡号list")
        private List<String> cardIds;


        /**
         * add item method
         * @param item
         */
        public void addCardIds(String item) {
            ensureCardIds();
            this.cardIds.add(item);
        }

        /**
         * add collection method
         * @param items
         */
        public void addCardIds(Collection<? extends String> items) {
            ensureCardIds();
            this.cardIds.addAll(items);
        }

        private void ensureCardIds() {
            if (cardIds == null) {
                this.cardIds = new ArrayList<>();
            }
        }


    }


    /**
     * 卡查询返回结果
     */
    @Setter
    @Getter
    @ToString
    @ProtoMessage(description = "卡查询返回结果")
    public static class CardInfoQueryByDBResponse {
        /**
         * 返回卡信息列表
         */
        @ProtoField(order = 1, type = FieldType.OBJECT, required = false, description = "返回卡信息列表")
        private List<SimpleCardInfo> simpleCardInfo;


        /**
         * add item method
         * @param item
         */
        public void addSimpleCardInfo(SimpleCardInfo item) {
            ensureSimpleCardInfo();
            this.simpleCardInfo.add(item);
        }

        /**
         * add collection method
         * @param items
         */
        public void addSimpleCardInfo(Collection<? extends SimpleCardInfo> items) {
            ensureSimpleCardInfo();
            this.simpleCardInfo.addAll(items);
        }

        private void ensureSimpleCardInfo() {
            if (simpleCardInfo == null) {
                this.simpleCardInfo = new ArrayList<>();
            }
        }


    }


    /**
     * QueryCardInfoList 请求参数
     */
    @Setter
    @Getter
    @ToString
    @ProtoMessage(description = "QueryCardInfoList 请求参数")
    public static class QueryCardInfoListRequest {
        /**
         * 卡号list
         */
        @ProtoField(order = 1, type = FieldType.STRING, required = false, description = "卡号list")
        private List<String> cardIds;


        /**
         * add item method
         * @param item
         */
        public void addCardIds(String item) {
            ensureCardIds();
            this.cardIds.add(item);
        }

        /**
         * add collection method
         * @param items
         */
        public void addCardIds(Collection<? extends String> items) {
            ensureCardIds();
            this.cardIds.addAll(items);
        }

        private void ensureCardIds() {
            if (cardIds == null) {
                this.cardIds = new ArrayList<>();
            }
        }


    }


    /**
     * 卡查询返回结果
     */
    @Setter
    @Getter
    @ToString
    @ProtoMessage(description = "卡查询返回结果")
    public static class QueryCardInfoListResponse {
        /**
         * 返回包含销售单的卡信息列表
         */
        @ProtoField(order = 1, type = FieldType.OBJECT, required = false, description = "返回包含销售单的卡信息列表")
        private List<SellOrderCardInfo> sellOrderCardInfo;


        /**
         * add item method
         * @param item
         */
        public void addSellOrderCardInfo(SellOrderCardInfo item) {
            ensureSellOrderCardInfo();
            this.sellOrderCardInfo.add(item);
        }

        /**
         * add collection method
         * @param items
         */
        public void addSellOrderCardInfo(Collection<? extends SellOrderCardInfo> items) {
            ensureSellOrderCardInfo();
            this.sellOrderCardInfo.addAll(items);
        }

        private void ensureSellOrderCardInfo() {
            if (sellOrderCardInfo == null) {
                this.sellOrderCardInfo = new ArrayList<>();
            }
        }


    }


    /**
     * QuerySellOrderList 请求参数
     */
    @Setter
    @Getter
    @ToString
    @ProtoMessage(description = "QuerySellOrderList 请求参数")
    public static class QuerySellOrderListRequest {
        /**
         * 销售单Idlist
         */
        @ProtoField(order = 1, type = FieldType.STRING, required = false, description = "销售单Idlist")
        private List<String> sellOrderIds;


        /**
         * add item method
         * @param item
         */
        public void addSellOrderIds(String item) {
            ensureSellOrderIds();
            this.sellOrderIds.add(item);
        }

        /**
         * add collection method
         * @param items
         */
        public void addSellOrderIds(Collection<? extends String> items) {
            ensureSellOrderIds();
            this.sellOrderIds.addAll(items);
        }

        private void ensureSellOrderIds() {
            if (sellOrderIds == null) {
                this.sellOrderIds = new ArrayList<>();
            }
        }


    }


    /**
     * 卡查询返回结果
     */
    @Setter
    @Getter
    @ToString
    @ProtoMessage(description = "卡查询返回结果")
    public static class QuerySellOrderListResponse {
        /**
         * 销售单列表
         */
        @ProtoField(order = 1, type = FieldType.OBJECT, required = false, description = "销售单列表")
        private List<SellOrderInfo> sellOrderInfo;


        /**
         * add item method
         * @param item
         */
        public void addSellOrderInfo(SellOrderInfo item) {
            ensureSellOrderInfo();
            this.sellOrderInfo.add(item);
        }

        /**
         * add collection method
         * @param items
         */
        public void addSellOrderInfo(Collection<? extends SellOrderInfo> items) {
            ensureSellOrderInfo();
            this.sellOrderInfo.addAll(items);
        }

        private void ensureSellOrderInfo() {
            if (sellOrderInfo == null) {
                this.sellOrderInfo = new ArrayList<>();
            }
        }


    }



}
