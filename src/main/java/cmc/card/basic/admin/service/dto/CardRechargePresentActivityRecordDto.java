package cmc.card.basic.admin.service.dto;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import mtime.lark.pb.FieldType;
import mtime.lark.pb.annotation.ProtoField;
import mtime.lark.pb.annotation.ProtoMessage;
import mtime.lark.pb.data.PageInfo;

/**
 * Dto
 * mscgenVersion: 0.5.4
 */
public class CardRechargePresentActivityRecordDto {

    private CardRechargePresentActivityRecordDto() {

    }

    /**
     * CardRechargePresentActivityRecord
     */
    @Setter
    @Getter
    @ToString
    @ProtoMessage(description = "")
    public static class CardRechargePresentActivityRecord {
        /**
         * 卡号
         */
        @ProtoField(order = 1, type = FieldType.STRING, required = false, description = "卡号")
        private String cardId;
        /**
         * 订单号
         */
        @ProtoField(order = 2, type = FieldType.STRING, required = false, description = "订单号")
        private String orderId;
        /**
         * 会员编码
         */
        @ProtoField(order = 3, type = FieldType.STRING, required = false, description = "会员编码")
        private String memberCode;
        /**
         * 手机号
         */
        @ProtoField(order = 4, type = FieldType.STRING, required = false, description = "手机号")
        private String phoneNo;
        /**
         * 订单状态
         */
        @ProtoField(order = 5, type = FieldType.INT32, required = false, description = "订单状态")
        private Integer status;
        /**
         * 卡类型编码
         */
        @ProtoField(order = 6, type = FieldType.STRING, required = false, description = "卡类型编码")
        private String cardTypeCode;
        /**
         * 卡类型名称
         */
        @ProtoField(order = 7, type = FieldType.STRING, required = false, description = "卡类型名称")
        private String cardTypeName;
        /**
         * 活动编码
         */
        @ProtoField(order = 8, type = FieldType.STRING, required = false, description = "活动编码")
        private String activityCode;
        /**
         * 活动名称
         */
        @ProtoField(order = 9, type = FieldType.STRING, required = false, description = "活动名称")
        private String activityName;
        /**
         * 充值金额
         */
        @ProtoField(order = 10, type = FieldType.INT32, required = false, description = "充值金额")
        private int amount;
        /**
         * 赠送金额
         */
        @ProtoField(order = 11, type = FieldType.INT32, required = false, description = "赠送金额")
        private int presentAmount;
        /**
         * 首充赠送金额
         */
        @ProtoField(order = 12, type = FieldType.INT32, required = false, description = "首充赠送金额")
        private int firstPresentAmount;
        /**
         * 渠道编码
         */
        @ProtoField(order = 13, type = FieldType.STRING, required = false, description = "渠道编码")
        private String channelCode;
        /**
         * 渠道名称
         */
        @ProtoField(order = 14, type = FieldType.STRING, required = false, description = "渠道名称")
        private String channelName;
        /**
         * 创建时间
         */
        @ProtoField(order = 15, type = FieldType.INT64, required = false, description = "创建时间")
        private LocalDateTime createTime;
        /**
         * esId
         */
        @ProtoField(order = 16, type = FieldType.STRING, required = false, description = "esId")
        private String esId;
        /**
         * 操作类型 1发卡2充值
         */
        @ProtoField(order = 17, type = FieldType.INT32, required = false, description = "操作类型 1发卡2充值")
        private Integer operationType;
        /**
         * 状态名称
         */
        @ProtoField(order = 18, type = FieldType.STRING, required = false, description = "状态名称")
        private String statusName;


    }


    /**
     * ListPage 请求参数
     */
    @Setter
    @Getter
    @ToString
    @ProtoMessage(description = "ListPage 请求参数")
    public static class ListPageRequest {
        /**
         * 卡号
         */
        @ProtoField(order = 1, type = FieldType.STRING, required = false, description = "卡号")
        private String cardId;
        /**
         * 订单号
         */
        @ProtoField(order = 2, type = FieldType.STRING, required = false, description = "订单号")
        private String orderId;
        /**
         * 会员编码
         */
        @ProtoField(order = 3, type = FieldType.STRING, required = false, description = "会员编码")
        private String memberCode;
        /**
         * 手机号
         */
        @ProtoField(order = 4, type = FieldType.STRING, required = false, description = "手机号")
        private String phoneNo;
        /**
         * 订单状态
         */
        @ProtoField(order = 5, type = FieldType.INT32, required = false, description = "订单状态")
        private Integer status;
        /**
         * 卡类型编码
         */
        @ProtoField(order = 6, type = FieldType.STRING, required = false, description = "卡类型编码")
        private String cardTypeCode;
        /**
         * 活动编码
         */
        @ProtoField(order = 7, type = FieldType.STRING, required = false, description = "活动编码")
        private String activityCode;
        /**
         * 活动名称
         */
        @ProtoField(order = 8, type = FieldType.STRING, required = false, description = "活动名称")
        private String activityName;
        /**
         * 充值金额
         */
        @ProtoField(order = 9, type = FieldType.INT32, required = false, description = "充值金额")
        private Integer amount;
        /**
         * 赠送总金额(含首充赠送金额)
         */
        @ProtoField(order = 10, type = FieldType.INT32, required = false, description = "赠送总金额(含首充赠送金额)")
        private Integer totalPresentAmount;
        /**
         * 创建开始时间
         */
        @ProtoField(order = 11, type = FieldType.INT64, required = false, description = "创建开始时间")
        private LocalDateTime createStartTime;
        /**
         * 创建结束时间
         */
        @ProtoField(order = 12, type = FieldType.INT64, required = false, description = "创建结束时间")
        private LocalDateTime createEndTime;
        /**
         * 分页数据
         */
        @ProtoField(order = 13, type = FieldType.OBJECT, required = true, description = "分页数据")
        private PageInfo pageInfo;


    }


    /**
     * ListPage 响应结果
     */
    @Setter
    @Getter
    @ToString
    @ProtoMessage(description = "ListPage 响应结果")
    public static class ListPageResponse {
        /**
         * 记录总数
         */
        @ProtoField(order = 1, type = FieldType.INT32, required = false, description = "记录总数")
        private int totalCount;
        /**
         * 记录总数
         */
        @ProtoField(order = 2, type = FieldType.OBJECT, required = false, description = "记录总数")
        private List<CardRechargePresentActivityRecord> recordList;


        /**
         * add item method
         * @param item
         */
        public void addRecordList(CardRechargePresentActivityRecord item) {
            ensureRecordList();
            this.recordList.add(item);
        }

        /**
         * add collection method
         * @param items
         */
        public void addRecordList(Collection<? extends CardRechargePresentActivityRecord> items) {
            ensureRecordList();
            this.recordList.addAll(items);
        }

        private void ensureRecordList() {
            if (recordList == null) {
                this.recordList = new ArrayList<>();
            }
        }


    }


    /**
     * ListByScrollId 请求参数
     */
    @Setter
    @Getter
    @ToString
    @ProtoMessage(description = "ListByScrollId 请求参数")
    public static class ListByScrollIdRequest {
        /**
         * 卡号
         */
        @ProtoField(order = 1, type = FieldType.STRING, required = false, description = "卡号")
        private String cardId;
        /**
         * 订单号
         */
        @ProtoField(order = 2, type = FieldType.STRING, required = false, description = "订单号")
        private String orderId;
        /**
         * 活动编码
         */
        @ProtoField(order = 3, type = FieldType.STRING, required = false, description = "活动编码")
        private String activityCode;
        /**
         * 创建开始时间
         */
        @ProtoField(order = 4, type = FieldType.INT64, required = false, description = "创建开始时间")
        private LocalDateTime createStartTime;
        /**
         * 创建结束时间
         */
        @ProtoField(order = 5, type = FieldType.INT64, required = false, description = "创建结束时间")
        private LocalDateTime createEndTime;
        /**
         * 游标id
         */
        @ProtoField(order = 6, type = FieldType.STRING, required = false, description = "游标id")
        private String scrollId;
        /**
         * 分页大小
         */
        @ProtoField(order = 7, type = FieldType.INT32, required = false, description = "分页大小")
        private int pageSize;


    }


    /**
     * ListByScrollId 响应结果
     */
    @Setter
    @Getter
    @ToString
    @ProtoMessage(description = "ListByScrollId 响应结果")
    public static class ListByScrollIdResponse {
        /**
         * 游标id
         */
        @ProtoField(order = 1, type = FieldType.STRING, required = false, description = "游标id")
        private String scrollId;
        /**
         * 记录总数
         */
        @ProtoField(order = 2, type = FieldType.OBJECT, required = false, description = "记录总数")
        private List<CardRechargePresentActivityRecord> recordList;


        /**
         * add item method
         * @param item
         */
        public void addRecordList(CardRechargePresentActivityRecord item) {
            ensureRecordList();
            this.recordList.add(item);
        }

        /**
         * add collection method
         * @param items
         */
        public void addRecordList(Collection<? extends CardRechargePresentActivityRecord> items) {
            ensureRecordList();
            this.recordList.addAll(items);
        }

        private void ensureRecordList() {
            if (recordList == null) {
                this.recordList = new ArrayList<>();
            }
        }


    }



}