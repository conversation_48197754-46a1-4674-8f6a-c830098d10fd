package cmc.card.basic.admin.service.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import mtime.lark.pb.FieldType;
import mtime.lark.pb.annotation.ProtoField;
import mtime.lark.pb.annotation.ProtoMessage;

/**
 * Dto
 * mscgenVersion: 0.5.5
 */
public class KamCardDto {

    private KamCardDto() {

    }

    /**
     * 充值请求
     */
    @Setter
    @Getter
    @ToString
    @ProtoMessage(description = "充值请求")
    public static class RechargeRequest {
        /**
         * 卡号
         */
        @ProtoField(order = 1, type = FieldType.STRING, required = true, description = "卡号")
        private String cardNo;
        /**
         * 合同号
         */
        @ProtoField(order = 2, type = FieldType.STRING, required = true, description = "合同号")
        private String contractNo;
        /**
         * 充值订单号
         */
        @ProtoField(order = 3, type = FieldType.STRING, required = true, description = "充值订单号")
        private String rechargeOrderNo;
        /**
         * 充值流水号
         */
        @ProtoField(order = 4, type = FieldType.STRING, required = true, description = "充值流水号")
        private String batchNo;
        /**
         * 充值金额
         */
        @ProtoField(order = 5, type = FieldType.INT32, required = true, description = "充值金额")
        private int rechargeAmount;
        /**
         * 赠送金额
         */
        @ProtoField(order = 6, type = FieldType.INT32, required = false, description = "赠送金额")
        private int presentAmount;
        /**
         * 操作人
         */
        @ProtoField(order = 7, type = FieldType.STRING, required = true, description = "操作人")
        private String operator;
        /**
         * 客户名称
         */
        @ProtoField(order = 8, type = FieldType.STRING, required = false, description = "客户名称")
        private String customerName;
        /**
         * 客户编码
         */
        @ProtoField(order = 9, type = FieldType.STRING, required = false, description = "客户编码")
        private String customerCode;


    }


    /**
     * recharge 响应结果
     */
    @Setter
    @Getter
    @ToString
    @ProtoMessage(description = "recharge 响应结果")
    public static class RechargeResponse {
        /**
         * 充值是否成功
         */
        @ProtoField(order = 1, type = FieldType.BOOL, required = true, description = "充值是否成功")
        private boolean success;
        /**
         * 错误信息（充值失败时填充）
         */
        @ProtoField(order = 2, type = FieldType.STRING, required = false, description = "错误信息（充值失败时填充）")
        private String errorMsg;
        /**
         * 充值金额（分）
         */
        @ProtoField(order = 3, type = FieldType.INT32, required = false, description = "充值金额（分）")
        private int rechargeAmount;
        /**
         * 赠送金额（分）
         */
        @ProtoField(order = 4, type = FieldType.INT32, required = false, description = "赠送金额（分）")
        private int presentAmount;
        /**
         * 余额变动（充值金额+赠送金额）（分）
         */
        @ProtoField(order = 5, type = FieldType.INT32, required = false, description = "余额变动（充值金额+赠送金额）（分）")
        private int changeBalance;
        /**
         * 充值前余额（分）
         */
        @ProtoField(order = 6, type = FieldType.INT32, required = false, description = "充值前余额（分）")
        private int beforeBalance;
        /**
         * 充值后余额（分）
         */
        @ProtoField(order = 7, type = FieldType.INT32, required = false, description = "充值后余额（分）")
        private int afterBalance;
        /**
         * 充值时间 yyyy-MM-dd HH:mm:ss
         */
        @ProtoField(order = 8, type = FieldType.STRING, required = false, description = "充值时间 yyyy-MM-dd HH:mm:ss")
        private String rechargeTime;
        /**
         * 发票号
         */
        @ProtoField(order = 9, type = FieldType.STRING, required = false, description = "发票号")
        private String invoiceId;


    }



}