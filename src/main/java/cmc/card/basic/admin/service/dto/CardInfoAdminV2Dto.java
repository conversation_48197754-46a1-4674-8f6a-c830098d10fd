package cmc.card.basic.admin.service.dto;

import cmc.card.contract.constant.*;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import mtime.lark.pb.FieldType;
import mtime.lark.pb.annotation.ProtoField;
import mtime.lark.pb.annotation.ProtoMessage;

/**
 * Dto
 * mscgenVersion: 0.5.4
 */
public class CardInfoAdminV2Dto {

    private CardInfoAdminV2Dto() {

    }

    /**
     * 卡常用信息
     */
    @Setter
    @Getter
    @ToString
    @ProtoMessage(description = "卡常用信息")
    public static class CardInfo {
        /**
         * 卡ID
         */
        @ProtoField(order = 1, type = FieldType.STRING, required = false, description = "卡ID")
        private String cardId;
        /**
         * 手机号
         */
        @ProtoField(order = 2, type = FieldType.STRING, required = false, description = "手机号")
        private String phoneNo;
        /**
         * 密码
         */
        @ProtoField(order = 3, type = FieldType.STRING, required = false, description = "密码")
        private String passWord;
        /**
         * 卡的密码
         */
        @ProtoField(order = 4, type = FieldType.STRING, required = false, description = "卡的密码")
        private String passWordClear;
        /**
         * 用户名
         */
        @ProtoField(order = 5, type = FieldType.STRING, required = false, description = "用户名")
        private String userName;
        /**
         * 卡类型
         */
        @ProtoField(order = 6, type = FieldType.ENUM, required = false, description = "卡类型")
        private CardType cardType;
        /**
         * 卡类别
         */
        @ProtoField(order = 7, type = FieldType.ENUM, required = false, description = "卡类别")
        private CardSort cardSort;
        /**
         * 是否是实体卡
         */
        @ProtoField(order = 8, type = FieldType.ENUM, required = false, description = "是否是实体卡")
        private IfEntity ifEntity;
        /**
         * 区域ID
         */
        @ProtoField(order = 9, type = FieldType.STRING, required = false, description = "区域ID")
        private String areaId;
        /**
         * 影院内码
         */
        @ProtoField(order = 10, type = FieldType.STRING, required = false, description = "影院内码")
        private String cinemaInnerCode;
        /**
         * 卡余额
         */
        @ProtoField(order = 11, type = FieldType.INT32, required = false, description = "卡余额")
        private int balance;
        /**
         * 是否允许网购
         */
        @ProtoField(order = 12, type = FieldType.ENUM, required = false, description = "是否允许网购")
        private CanUseOnline canuseOnLine;
        /**
         * 卡状态
         */
        @ProtoField(order = 13, type = FieldType.ENUM, required = false, description = "卡状态")
        private CardState cardState;
        /**
         * 有效期截止日期
         */
        @ProtoField(order = 14, type = FieldType.INT64, required = false, description = "有效期截止日期")
        private LocalDateTime effectiveEnd;
        /**
         * 卡类型ID
         */
        @ProtoField(order = 15, type = FieldType.INT64, required = false, description = "卡类型ID")
        private long cardTypeNo;
        /**
         * 消费次数
         */
        @ProtoField(order = 16, type = FieldType.INT32, required = false, description = "消费次数")
        private int spendTimes;
        /**
         * 影院名称
         */
        @ProtoField(order = 17, type = FieldType.STRING, required = false, description = "影院名称")
        private String cinemaName;
        /**
         * 是否是刮刮卡初始密码
         */
        @ProtoField(order = 18, type = FieldType.STRING, required = false, description = "是否是刮刮卡初始密码")
        private String useOriginPwd;
        /**
         * 销售单id
         */
        @ProtoField(order = 19, type = FieldType.STRING, required = false, description = "销售单id")
        private String sellOrderId;
        /**
         * 预冻总金额
         */
        @ProtoField(order = 20, type = FieldType.INT32, required = false, description = "预冻总金额")
        private int txTransBal;
        /**
         * 发卡时间
         */
        @ProtoField(order = 21, type = FieldType.INT64, required = false, description = "发卡时间")
        private LocalDateTime issueTime;
        /**
         * 最后更新时间
         */
        @ProtoField(order = 22, type = FieldType.INT64, required = false, description = "最后更新时间")
        private LocalDateTime lastTime;
        /**
         * 版本号
         */
        @ProtoField(order = 23, type = FieldType.INT32, required = false, description = "版本号")
        private int version;
        /**
         * 赠送总金额
         */
        @ProtoField(order = 24, type = FieldType.INT32, required = false, description = "赠送总金额")
        private int presentAmount;
        /**
         * 赠送余额
         */
        @ProtoField(order = 25, type = FieldType.INT32, required = false, description = "赠送余额")
        private int presentBalance;


    }


    /**
     * 卡类型卡面信息
     */
    @Setter
    @Getter
    @ToString
    @ProtoMessage(description = "卡类型卡面信息")
    public static class CardTypeInfo {
        /**
         * 卡号
         */
        @ProtoField(order = 1, type = FieldType.STRING, required = false, description = "卡号")
        private String cardId;
        /**
         * 卡类型编码
         */
        @ProtoField(order = 2, type = FieldType.STRING, required = false, description = "卡类型编码")
        private String cardTypeCode;
        /**
         * 卡面编码
         */
        @ProtoField(order = 3, type = FieldType.STRING, required = false, description = "卡面编码")
        private String coverCode;


    }


    /**
     * 卡充值订单组合信息
     */
    @Setter
    @Getter
    @ToString
    @ProtoMessage(description = "卡充值订单组合信息")
    public static class CardRechargeOrderInfo {
        /**
         * 卡号
         */
        @ProtoField(order = 1, type = FieldType.STRING, required = false, description = "卡号")
        private String cardId;
        /**
         * 手机号
         */
        @ProtoField(order = 2, type = FieldType.STRING, required = false, description = "手机号")
        private String phoneNo;
        /**
         * 余额
         */
        @ProtoField(order = 3, type = FieldType.INT32, required = false, description = "余额")
        private int balance;
        /**
         * 状态
         */
        @ProtoField(order = 4, type = FieldType.STRING, required = false, description = "状态")
        private String status;
        /**
         * 订单号
         */
        @ProtoField(order = 5, type = FieldType.STRING, required = false, description = "订单号")
        private String orderId;
        /**
         * 充值金额
         */
        @ProtoField(order = 6, type = FieldType.INT32, required = false, description = "充值金额")
        private int rechargeAmount;
        /**
         * 操作人
         */
        @ProtoField(order = 7, type = FieldType.STRING, required = false, description = "操作人")
        private String operator;
        /**
         * 操作时间
         */
        @ProtoField(order = 8, type = FieldType.STRING, required = false, description = "操作时间")
        private String operationTime;
        /**
         * 备注
         */
        @ProtoField(order = 9, type = FieldType.STRING, required = false, description = "备注")
        private String remark;


    }


    /**
     * GetCardInfo 请求参数
     */
    @Setter
    @Getter
    @ToString
    @ProtoMessage(description = "GetCardInfo 请求参数")
    public static class GetCardInfoRequest {
        /**
         * 卡号
         */
        @ProtoField(order = 1, type = FieldType.STRING, required = true, description = "卡号")
        private String cardId;


    }


    /**
     * 卡查询返回结果
     */
    @Setter
    @Getter
    @ToString
    @ProtoMessage(description = "卡查询返回结果")
    public static class GetCardInfoResponse {
        /**
         * 卡基本信息
         */
        @ProtoField(order = 1, type = FieldType.OBJECT, required = false, description = "卡基本信息")
        private CardInfo cardInfo;


    }


    /**
     * UpdateCardState 请求参数
     */
    @Setter
    @Getter
    @ToString
    @ProtoMessage(description = "UpdateCardState 请求参数")
    public static class UpdateCardStateRequest {
        /**
         * 卡号
         */
        @ProtoField(order = 1, type = FieldType.STRING, required = true, description = "卡号")
        private String cardId;
        /**
         * 备注
         */
        @ProtoField(order = 2, type = FieldType.STRING, required = false, description = "备注")
        private String remark;
        /**
         * 要修改的卡状态值
         */
        @ProtoField(order = 3, type = FieldType.ENUM, required = true, description = "要修改的卡状态值")
        private CardState cardState;
        /**
         * 操作员
         */
        @ProtoField(order = 4, type = FieldType.STRING, required = false, description = "操作员")
        private String userName;
        /**
         * 操作员Id
         */
        @ProtoField(order = 5, type = FieldType.INT64, required = false, description = "操作员Id")
        private long userId;
        /**
         * 登录人的影城
         */
        @ProtoField(order = 6, type = FieldType.STRING, required = true, description = "登录人的影城")
        private String userCinemaInnerCode;
        /**
         * 登录人的影城名称
         */
        @ProtoField(order = 7, type = FieldType.STRING, required = true, description = "登录人的影城名称")
        private String userCinemaName;
        /**
         * 渠道编码
         */
        @ProtoField(order = 8, type = FieldType.STRING, required = true, description = "渠道编码")
        private String channelCode;
        /**
         * 渠道编码名称
         */
        @ProtoField(order = 9, type = FieldType.STRING, required = true, description = "渠道编码名称")
        private String channelCodeName;
        /**
         * 校验退款金额
         */
        @ProtoField(order = 10, type = FieldType.INT32, required = false, description = "校验退款金额")
        private int validAmount;


    }


    /**
     * UpdateCardState 响应结果
     */
    @Setter
    @Getter
    @ToString
    @ProtoMessage(description = "UpdateCardState 响应结果")
    public static class UpdateCardStateResponse {
        /**
         * 返回执行结果
         */
        @ProtoField(order = 1, type = FieldType.BOOL, required = false, description = "返回执行结果")
        private boolean success;
        /**
         * 失败原因
         */
        @ProtoField(order = 2, type = FieldType.STRING, required = false, description = "失败原因")
        private String failReason;


    }


    /**
     * UpdateCardStateInBatches 请求参数
     */
    @Setter
    @Getter
    @ToString
    @ProtoMessage(description = "UpdateCardStateInBatches 请求参数")
    public static class UpdateCardStateInBatchesRequest {
        /**
         * 卡号
         */
        @ProtoField(order = 1, type = FieldType.STRING, required = false, description = "卡号")
        private List<String> cardIds;
        /**
         * 备注
         */
        @ProtoField(order = 2, type = FieldType.STRING, required = false, description = "备注")
        private String remark;
        /**
         * 要修改的卡状态值
         */
        @ProtoField(order = 3, type = FieldType.INT32, required = true, description = "要修改的卡状态值")
        private int cardState;
        /**
         * 操作员
         */
        @ProtoField(order = 4, type = FieldType.STRING, required = false, description = "操作员")
        private String userName;
        /**
         * 操作员Id
         */
        @ProtoField(order = 5, type = FieldType.INT64, required = false, description = "操作员Id")
        private long userId;


        /**
         * add item method
         * @param item
         */
        public void addCardIds(String item) {
            ensureCardIds();
            this.cardIds.add(item);
        }

        /**
         * add collection method
         * @param items
         */
        public void addCardIds(Collection<? extends String> items) {
            ensureCardIds();
            this.cardIds.addAll(items);
        }

        private void ensureCardIds() {
            if (cardIds == null) {
                this.cardIds = new ArrayList<>();
            }
        }


    }


    /**
     * UpdateCardStateInBatches 响应结果
     */
    @Setter
    @Getter
    @ToString
    @ProtoMessage(description = "UpdateCardStateInBatches 响应结果")
    public static class UpdateCardStateInBatchesResponse {
        /**
         * 返回执行结果
         */
        @ProtoField(order = 1, type = FieldType.BOOL, required = false, description = "返回执行结果")
        private boolean success;
        /**
         * 失败原因
         */
        @ProtoField(order = 2, type = FieldType.STRING, required = false, description = "失败原因")
        private String failReason;


    }


    /**
     * UpdatePresentBalance 请求参数
     */
    @Setter
    @Getter
    @ToString
    @ProtoMessage(description = "UpdatePresentBalance 请求参数")
    public static class UpdatePresentBalanceRequest {
        /**
         * 卡号
         */
        @ProtoField(order = 1, type = FieldType.STRING, required = true, description = "卡号")
        private String cardId;
        /**
         * 赠送余额
         */
        @ProtoField(order = 2, type = FieldType.INT32, required = true, description = "赠送余额")
        private int presentBalance;
        /**
         * 版本号
         */
        @ProtoField(order = 3, type = FieldType.INT32, required = true, description = "版本号")
        private int version;


    }


    /**
     * UpdatePresentBalance 响应结果
     */
    @Setter
    @Getter
    @ToString
    @ProtoMessage(description = "UpdatePresentBalance 响应结果")
    public static class UpdatePresentBalanceResponse {
        /**
         * 返回执行结果
         */
        @ProtoField(order = 1, type = FieldType.BOOL, required = false, description = "返回执行结果")
        private boolean success;
        /**
         * 失败原因
         */
        @ProtoField(order = 2, type = FieldType.STRING, required = false, description = "失败原因")
        private String failReason;


    }


    /**
     * 获取应收取年费卡列表请求参数
     */
    @Setter
    @Getter
    @ToString
    @ProtoMessage(description = "获取应收取年费卡列表请求参数")
    public static class FindCardByTimeWithPageRequest {
        /**
         * 开始时间
         */
        @ProtoField(order = 1, type = FieldType.INT64, required = true, description = "开始时间")
        private LocalDateTime startTime;
        /**
         * 结束时间
         */
        @ProtoField(order = 2, type = FieldType.INT64, required = true, description = "结束时间")
        private LocalDateTime endTime;
        /**
         * 页码
         */
        @ProtoField(order = 3, type = FieldType.INT32, required = true, description = "页码")
        private int pageNo;
        /**
         * 页数
         */
        @ProtoField(order = 4, type = FieldType.INT32, required = true, description = "页数")
        private int pageSize;


    }


    /**
     * 获取应收取年费卡列表请求响应
     */
    @Setter
    @Getter
    @ToString
    @ProtoMessage(description = "获取应收取年费卡列表请求响应")
    public static class FindCardByTimeWithPageResponse {
        /**
         * 卡列表
         */
        @ProtoField(order = 1, type = FieldType.OBJECT, required = false, description = "卡列表")
        private List<CardInfo> cardInfoList;


        /**
         * add item method
         * @param item
         */
        public void addCardInfoList(CardInfo item) {
            ensureCardInfoList();
            this.cardInfoList.add(item);
        }

        /**
         * add collection method
         * @param items
         */
        public void addCardInfoList(Collection<? extends CardInfo> items) {
            ensureCardInfoList();
            this.cardInfoList.addAll(items);
        }

        private void ensureCardInfoList() {
            if (cardInfoList == null) {
                this.cardInfoList = new ArrayList<>();
            }
        }


    }


    /**
     * CalculatePresentBalance 请求参数
     */
    @Setter
    @Getter
    @ToString
    @ProtoMessage(description = "CalculatePresentBalance 请求参数")
    public static class CalculatePresentBalanceRequest {
        /**
         * 卡号
         */
        @ProtoField(order = 1, type = FieldType.STRING, required = true, description = "卡号")
        private String cardId;
        /**
         * 卡类型编码
         */
        @ProtoField(order = 2, type = FieldType.STRING, required = false, description = "卡类型编码")
        private String cardTypeCode;


    }


    /**
     * CalculatePresentBalance 响应结果
     */
    @Setter
    @Getter
    @ToString
    @ProtoMessage(description = "CalculatePresentBalance 响应结果")
    public static class CalculatePresentBalanceResponse {
        /**
         * 卡赠送余额
         */
        @ProtoField(order = 1, type = FieldType.INT32, required = false, description = "卡赠送余额")
        private int cardPresentBalance;


    }


    /**
     * 根据卡号查询卡类型和卡面编码
     */
    @Setter
    @Getter
    @ToString
    @ProtoMessage(description = "根据卡号查询卡类型和卡面编码")
    public static class FindCardTypeInfoListRequest {
        /**
         * 卡号
         */
        @ProtoField(order = 1, type = FieldType.STRING, required = false, description = "卡号")
        private List<String> cardIdList;


        /**
         * add item method
         * @param item
         */
        public void addCardIdList(String item) {
            ensureCardIdList();
            this.cardIdList.add(item);
        }

        /**
         * add collection method
         * @param items
         */
        public void addCardIdList(Collection<? extends String> items) {
            ensureCardIdList();
            this.cardIdList.addAll(items);
        }

        private void ensureCardIdList() {
            if (cardIdList == null) {
                this.cardIdList = new ArrayList<>();
            }
        }


    }


    /**
     * FindCardTypeInfoList 响应结果
     */
    @Setter
    @Getter
    @ToString
    @ProtoMessage(description = "FindCardTypeInfoList 响应结果")
    public static class FindCardTypeInfoListResponse {
        /**
         * 卡类型信息列表
         */
        @ProtoField(order = 1, type = FieldType.OBJECT, required = false, description = "卡类型信息列表")
        private List<CardTypeInfo> cardTypeInfoList;


        /**
         * add item method
         * @param item
         */
        public void addCardTypeInfoList(CardTypeInfo item) {
            ensureCardTypeInfoList();
            this.cardTypeInfoList.add(item);
        }

        /**
         * add collection method
         * @param items
         */
        public void addCardTypeInfoList(Collection<? extends CardTypeInfo> items) {
            ensureCardTypeInfoList();
            this.cardTypeInfoList.addAll(items);
        }

        private void ensureCardTypeInfoList() {
            if (cardTypeInfoList == null) {
                this.cardTypeInfoList = new ArrayList<>();
            }
        }


    }


    /**
     * CalculatePresentBalanceSnapshoot 请求参数
     */
    @Setter
    @Getter
    @ToString
    @ProtoMessage(description = "CalculatePresentBalanceSnapshoot 请求参数")
    public static class CalculatePresentBalanceSnapshootRequest {
        /**
         * 卡号
         */
        @ProtoField(order = 1, type = FieldType.STRING, required = true, description = "卡号")
        private String cardId;
        /**
         * 卡类型编码
         */
        @ProtoField(order = 2, type = FieldType.STRING, required = true, description = "卡类型编码")
        private String cardTypeCode;
        /**
         * 某时间卡余额
         */
        @ProtoField(order = 3, type = FieldType.INT32, required = true, description = "某时间卡余额")
        private int cardBalanceSnapshoot;
        /**
         * 结束时间
         */
        @ProtoField(order = 4, type = FieldType.INT64, required = true, description = "结束时间")
        private LocalDateTime endTime;


    }


    /**
     * CalculatePresentBalanceSnapshoot 响应结果
     */
    @Setter
    @Getter
    @ToString
    @ProtoMessage(description = "CalculatePresentBalanceSnapshoot 响应结果")
    public static class CalculatePresentBalanceSnapshootResponse {
        /**
         * 卡赠送余额
         */
        @ProtoField(order = 1, type = FieldType.INT32, required = false, description = "卡赠送余额")
        private int cardPresentBalance;


    }


    /**
     * NegativeRecharge 请求参数
     */
    @Setter
    @Getter
    @ToString
    @ProtoMessage(description = "NegativeRecharge 请求参数")
    public static class NegativeRechargeRequest {
        /**
         * 卡号
         */
        @ProtoField(order = 1, type = FieldType.STRING, required = true, description = "卡号")
        private String cardId;
        /**
         * 负充金额
         */
        @ProtoField(order = 2, type = FieldType.INT32, required = true, description = "负充金额")
        private int negativeNum;
        /**
         * 赠送负充金额
         */
        @ProtoField(order = 3, type = FieldType.INT32, required = false, description = "赠送负充金额")
        private int negativePresentNum;
        /**
         * 备注
         */
        @ProtoField(order = 4, type = FieldType.STRING, required = false, description = "备注")
        private String remark;
        /**
         * 操作人
         */
        @ProtoField(order = 5, type = FieldType.STRING, required = false, description = "操作人")
        private String operator;
        /**
         * 操作人id
         */
        @ProtoField(order = 6, type = FieldType.INT32, required = false, description = "操作人id")
        private int operatorId;


    }


    /**
     * NegativeRecharge 响应结果
     */
    @Setter
    @Getter
    @ToString
    @ProtoMessage(description = "NegativeRecharge 响应结果")
    public static class NegativeRechargeResponse {
        /**
         * 返回执行结果
         */
        @ProtoField(order = 1, type = FieldType.BOOL, required = false, description = "返回执行结果")
        private boolean success;
        /**
         * 失败原因
         */
        @ProtoField(order = 2, type = FieldType.STRING, required = false, description = "失败原因")
        private String failReason;


    }


    /**
     * FindCardRechargeOrderInfo 请求参数
     */
    @Setter
    @Getter
    @ToString
    @ProtoMessage(description = "FindCardRechargeOrderInfo 请求参数")
    public static class FindCardRechargeOrderInfoRequest {
        /**
         * 订单号
         */
        @ProtoField(order = 1, type = FieldType.STRING, required = false, description = "订单号")
        private List<String> orderIds;


        /**
         * add item method
         * @param item
         */
        public void addOrderIds(String item) {
            ensureOrderIds();
            this.orderIds.add(item);
        }

        /**
         * add collection method
         * @param items
         */
        public void addOrderIds(Collection<? extends String> items) {
            ensureOrderIds();
            this.orderIds.addAll(items);
        }

        private void ensureOrderIds() {
            if (orderIds == null) {
                this.orderIds = new ArrayList<>();
            }
        }


    }


    /**
     * 返回结果
     */
    @Setter
    @Getter
    @ToString
    @ProtoMessage(description = "返回结果")
    public static class FindCardRechargeOrderInfoResponse {
        /**
         * 卡充值订单信息
         */
        @ProtoField(order = 1, type = FieldType.OBJECT, required = false, description = "卡充值订单信息")
        private List<CardRechargeOrderInfo> cardRechargeOrderInfoList;


        /**
         * add item method
         * @param item
         */
        public void addCardRechargeOrderInfoList(CardRechargeOrderInfo item) {
            ensureCardRechargeOrderInfoList();
            this.cardRechargeOrderInfoList.add(item);
        }

        /**
         * add collection method
         * @param items
         */
        public void addCardRechargeOrderInfoList(Collection<? extends CardRechargeOrderInfo> items) {
            ensureCardRechargeOrderInfoList();
            this.cardRechargeOrderInfoList.addAll(items);
        }

        private void ensureCardRechargeOrderInfoList() {
            if (cardRechargeOrderInfoList == null) {
                this.cardRechargeOrderInfoList = new ArrayList<>();
            }
        }


    }



}