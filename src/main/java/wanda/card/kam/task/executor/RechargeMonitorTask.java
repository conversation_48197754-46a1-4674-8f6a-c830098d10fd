package wanda.card.kam.task.executor;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.alibaba.fastjson.JSON;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import wanda.card.kam.common.provider.util.PublisherUtil;
import wanda.card.kam.task.facade.RechargeMonitorFacade;

/**
 * 充值监控任务
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@AllArgsConstructor
public class RechargeMonitorTask {

    private final RechargeMonitorFacade rechargeMonitorFacade;

    /**
     * 监控长时间处于充值中状态的卡信息
     * 每10分钟执行一次，扫描超过30分钟仍处于充值中状态的卡信息
     */
    @XxlJob("rechargeMonitorTask")
    public ReturnT<String> execute(String param) {
        log.info("开始执行充值监控任务，参数:{}", param);
        MonitorParam monitorParam = JSON.parseObject(param, MonitorParam.class);
        try {
            // 执行监控逻辑
            rechargeMonitorFacade.monitorRechargeStatus(monitorParam.alarmEmail, monitorParam.getTimeThreshold());
            if (!StringUtils.isEmpty(monitorParam.resendTopic)) {
                // 用于手动发送充值消息 eg: card-kam-recharge-card:123456 或 card-kam-recharge-notify:123456
                String resendTopic = monitorParam.getResendTopic();
                String[] split = resendTopic.split(":");
                PublisherUtil.send(split[0], split[1]);
            }
            log.info("充值监控任务执行完成");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("充值监控任务执行异常", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "充值监控任务执行异常: " + e.getMessage());
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MonitorParam {
        private int timeThreshold = 30;
        private String alarmEmail;
        private String resendTopic;
    }
}