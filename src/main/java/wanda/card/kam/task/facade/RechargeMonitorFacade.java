package wanda.card.kam.task.facade;

import com.alibaba.cloud.commons.lang.StringUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import wanda.card.kam.common.contract.constant.PushStatus;
import wanda.card.kam.common.contract.constant.Topic;
import wanda.card.kam.common.provider.dao.RechargeOrderCardInfoDao;
import wanda.card.kam.common.provider.dao.RechargeOrderLogDao;
import wanda.card.kam.common.provider.entity.RechargeOrderCardInfo;
import wanda.card.kam.common.provider.util.PublisherUtil;
import wanda.cloud.commons.mail.contract.MailService;
import wanda.cloud.commons.mail.contract.dto.MailDto;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 充值监控外观类
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@AllArgsConstructor
public class RechargeMonitorFacade {
    private final MailService mailService;
    private final RechargeOrderLogDao rechargeOrderLogDao;
    private final RechargeOrderCardInfoDao rechargeOrderCardInfoDao;

    public void monitorNotifyCaiGuanSysStatus(String email) {
        log.info("开始监控推送财管系统异常日志...");
        List<String> batchNoList = rechargeOrderLogDao.findByPushStatus(PushStatus.PUSH_FAIL).stream().peek(log -> {
            PublisherUtil.send(Topic.RECHARGE_NOTIFY_TOPIC, log.getBatchNo());
        }).map(log -> log.getRechargeOrderNo() + ":" + log.getBatchNo()).collect(Collectors.toList());
        if (batchNoList.isEmpty()) {
            log.info("未发现推送财管系统异常日志");
            return;
        } else {
            log.info("发现{}个批次推送财管系统异常日志", batchNoList.size());
            sendAlarmEmail(email, batchNoList.stream().collect(Collectors.joining("\n")));
        }
    }

    /**
     * 监控充值状态
     */
    public void monitorRechargeStatus(String email, int timeThreshold_) {
        log.info("开始监控长时间处于充值中状态的卡信息");

        // 查询超过30分钟仍处于充值中状态的卡信息
        LocalDateTime timeThreshold = LocalDateTime.now().minusMinutes(timeThreshold_);
        List<RechargeOrderCardInfo> longTimeRechargingCards = findLongTimeRechargingCards(timeThreshold);
        log.info("查询超过{}分钟仍处于充值中状态的卡信息，共{}张卡", timeThreshold_, longTimeRechargingCards.size());
        if (longTimeRechargingCards.isEmpty()) {
            log.info("未发现长时间处于充值中状态的卡信息");
            return;
        }

        log.info("发现{}张卡长时间处于充值中状态", longTimeRechargingCards.size());

        // 按批次号分组统计
        Map<String, List<RechargeOrderCardInfo>> batchGroups = longTimeRechargingCards.stream()
                .collect(Collectors.groupingBy(RechargeOrderCardInfo::getBatchNo));

        // 发送报警邮件
        sendAlarmEmail(email, batchGroups);

        // 重新发送充值消息
        resendRechargeMessages(batchGroups);

        log.info("充值监控处理完成，共处理{}个批次", batchGroups.size());
    }

    /**
     * 查询长时间处于充值中状态的卡信息
     */
    private List<RechargeOrderCardInfo> findLongTimeRechargingCards(LocalDateTime timeThreshold) {
        return rechargeOrderCardInfoDao.findLongTimeRechargingCards(timeThreshold);
    }

    /**
     * 发送报警邮件
     */
    private void sendAlarmEmail(String email, Map<String, List<RechargeOrderCardInfo>> batchGroups) {
        if (StringUtils.isEmpty(email)) {
            log.warn("报警邮箱未配置，跳过发送报警邮件");
            return;
        }
        log.info("开始发送充值异常报警邮件");
        StringBuilder content = new StringBuilder();
        for (Map.Entry<String, List<RechargeOrderCardInfo>> entry : batchGroups.entrySet()) {
            String batchNo = entry.getKey();
            List<RechargeOrderCardInfo> cards = entry.getValue();
            RechargeOrderCardInfo rechargeOrderCardInfo = cards.get(0);
            String rechargeOrderNo = rechargeOrderCardInfo.getRechargeOrderNo();
            log.info("已发送批次{}的报警邮件，涉及{}张卡", batchNo, cards.size());
            content.append("批次号：").append(batchNo)
                    .append("，充值订单号：").append(rechargeOrderNo)
                    .append("异常充值中卡数量：").append(cards.size()).append("\n")
                    .append("卡号\n")
                    .append("-----------------------------\n")
                    .append(cards.stream().map(RechargeOrderCardInfo::getCardNo).collect(Collectors.joining(";")))
                    .append("\n\n")
            ;
        }
        <EMAIL> SendMailRequest mailRequest = new MailDto.SendMailRequest();
        MailDto.MailMessage mailMessage = new MailDto.MailMessage();
        mailMessage.setSubject("财管批量充值异常报警");
        mailMessage.setTo(List.of(email.split(",")));
        mailMessage.setContent(content.toString());
        mailRequest.setMailMessage(mailMessage);
        mailService.sendmail(mailRequest);
    }

    /**
     * 重新发送充值消息
     */
    private void resendRechargeMessages(Map<String, List<RechargeOrderCardInfo>> batchGroups) {
        log.info("开始重新发送充值消息");

        for (Map.Entry<String, List<RechargeOrderCardInfo>> entry : batchGroups.entrySet()) {
            String batchNo = entry.getKey();
            List<RechargeOrderCardInfo> cards = entry.getValue();
            // 发送重新充值消息
            PublisherUtil.send(Topic.RECHARGE_CARD_TOPIC, batchNo);
            log.info("已重新发送批次{}的充值消息，涉及{}张卡", batchNo, cards.size());
        }
    }
}