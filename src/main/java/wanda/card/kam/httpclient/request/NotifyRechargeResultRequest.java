package wanda.card.kam.httpclient.request;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.apache.http.HttpHeaders;
import org.springframework.util.MimeTypeUtils;
import wanda.card.kam.httpclient.response.NotifyRechargeResultResponse;
import wanda.stark.core.lang.NameStyle;
import wanda.stark.tools.httpclient.annotation.Header;
import wanda.stark.tools.httpclient.annotation.Params;
import wanda.stark.tools.httpclient.annotation.PostRequest;
import wanda.stark.tools.httpclient.lang.Request;

import java.util.List;

/**
 * Created by fuwei on 2023/4/21.
 */
@Getter
@Setter
@ToString
@Params(nameStyle = NameStyle.SELF)
@Header(name = HttpHeaders.CONTENT_TYPE, value = MimeTypeUtils.APPLICATION_JSON_VALUE)
@PostRequest(path = "/notify/recharge/result")
public class NotifyRechargeResultRequest implements Request<NotifyRechargeResultResponse> {

    /**
     * 充值请求流水号(批次号)
     */
    private String batchNo;

    /**
     * 合同号
     */
    private String contractNo;

    /**
     * 充值订单号
     */
    private String rechargeOrderNo;

    /**
     * 每张卡充值金额（分）
     */
    private Integer everyCardRechargeAmount;

    /**
     * 每张卡赠送金额（分）
     */
    private Integer everyCardPresentAmount;

    /**
     * 卡号列表
     */
    private List<CardInfo> cardInfos;

    /**
     * 卡信息
     */
    @Data
    public static class CardInfo {
        /**
         * 卡号
         */
        private String cardNo;

        /**
         * true为充值成功，false为充值失败
         */
        private boolean rechargeResult;

        /**
         * 失败信息
         */
        private String rechargeFailResult;
    }
}
