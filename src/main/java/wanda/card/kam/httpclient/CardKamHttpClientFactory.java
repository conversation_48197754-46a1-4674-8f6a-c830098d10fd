package wanda.card.kam.httpclient;

import wanda.stark.core.lang.Naming;
import wanda.stark.tools.httpclient.HttpClient;
import wanda.stark.tools.httpclient.HttpClientFactory;
import wanda.stark.tools.httpclient.lang.HttpClientOptions;

@Naming("card-kam")
public class CardKamHttpClientFactory implements HttpClientFactory {
    @Override
    public HttpClient create(HttpClientOptions options) {
        return new HttpClient(options);
    }
}
