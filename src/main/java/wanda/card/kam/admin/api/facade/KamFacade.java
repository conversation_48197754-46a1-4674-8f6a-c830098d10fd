package wanda.card.kam.admin.api.facade;

import cmc.card.basic.admin.service.dto.CardQueryAdminDto.SellOrderInfo;
import cmc.card.basic.admin.service.dto.CardQueryAdminDto.QuerySellOrderListRequest;
import cmc.card.basic.admin.service.dto.CardQueryAdminDto.SellOrderCardInfo;
import cmc.card.basic.admin.service.dto.CardQueryAdminDto.QueryCardInfoListRequest;
import cmc.card.basic.admin.service.iface.CardQueryAdminService;
import cmc.card.type.admin.service.dto.CardManageAdminDto;
import cmc.card.type.admin.service.iface.CardManageAdminService;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import wanda.card.kam.admin.api.model.RechargeVerifyModel.*;
import wanda.card.kam.admin.api.model.Result;
import wanda.card.kam.admin.contract.RechargeOrderService;
import wanda.card.kam.admin.contract.dto.RechargeOrderDto;
import wanda.mdm.cinema.common.contract.dto.CinemaDto;
import wanda.stark.core.data.R;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Component
@AllArgsConstructor
public class KamFacade {

    private final CinemaFacade cinemaFacade;
    private final LocationFacade locationFacade;
    private final CardQueryAdminService cardQueryAdminService;
    private final CardManageAdminService cardManageAdminService;
    private final RechargeOrderService rechargeOrderService;

    /**
     * 批量卡充值校验接口
     */
    public Result<VerifyResponse> rechargeVerify(VerifyRequest request) {
        Map<String, String> errorCardsMap = Maps.newHashMap();
        List<String> cardNos = Lists.newArrayList(request.getCardNos().split(","));
        //批量查询卡信息,校验卡状态
        QueryCardInfoListRequest infoListRequest = new QueryCardInfoListRequest();
        infoListRequest.setCardIds(cardNos);
        List<SellOrderCardInfo> cardInfoList = cardQueryAdminService.queryCardInfoList(infoListRequest).getSellOrderCardInfo();
        verifyCardStatus(cardInfoList, errorCardsMap);
        //批量查询卡销售单，校验客户信息
        verifyCustomerInfo(request.getCustomerCode(), cardInfoList, errorCardsMap);
        //批量查询卡类型，校验是否可充值，最低充值金额限制
        verifyCardType(request, cardInfoList, errorCardsMap);
        return buildVerifyResponse(errorCardsMap);
    }

    /**
     * 充值订单变更接口
     */
    public Result<Object> rechargeOrderUpdate(OrderUpdateRequest request) {
        RechargeOrderDto.UpdateRequest updateRequest = new RechargeOrderDto.UpdateRequest();
        updateRequest.setContractNo(request.getContractNo());
        updateRequest.setRechargeOrderNo(request.getRechargeOrderNo());
        updateRequest.setOperator(request.getOperator());
        R<Void> response = rechargeOrderService.updateRechargeOrder(updateRequest);
        if (response.isSuccess()){
            return Result.success();
        }else{
            return Result.simple(-1, response.getMsg());
        }
    }

    /**
     * 构建卡校验接口响应结果
     */
    private Result<VerifyResponse> buildVerifyResponse(Map<String, String> errorCardsMap) {
        if (errorCardsMap.isEmpty()) {
            return Result.success();
        }
        VerifyResponse verifyResponse = new VerifyResponse();
        List<VerifyItem> verifyItems = Lists.newArrayList();
        verifyItems.addAll(errorCardsMap.entrySet().stream()
                .map(entry -> {
                    VerifyItem verifyItem = new VerifyItem();
                    verifyItem.setCardNo(entry.getKey());
                    verifyItem.setErrorMessage(entry.getValue());
                    return verifyItem;
                })
                .collect(Collectors.toList()));
        verifyResponse.setErrorCards(verifyItems);
        return Result.simple(-1, "校验失败", verifyResponse);
    }

    /**
     * 校验卡状态
     */
    private void verifyCardStatus(List<SellOrderCardInfo> cardInfoList, Map<String, String> errorCardsMap) {
        List<SellOrderCardInfo> errorStatusCardList = cardInfoList.stream()
                .filter(cardInfo -> cardInfo.getCardState().value() != 2)
                .collect(Collectors.toList());
        for (SellOrderCardInfo sellOrderCardInfo : errorStatusCardList) {
            errorCardsMap.put(sellOrderCardInfo.getCardId(), "卡状态异常 ");
        }
    }

    /**
     * 校验发卡客户信息是否一致
     */
    private void verifyCustomerInfo(String customerId, List<SellOrderCardInfo> cardInfoList,
                                    Map<String, String> errorCardsMap) {
        //根据销售单ID聚合分组
        Map<String, List<SellOrderCardInfo>> map = cardInfoList.stream()
                .collect(Collectors.groupingBy(sellOrderCardInfo ->
                        convertSellOrderId(sellOrderCardInfo.getSellOrderId())
                ));
        //查询销售单信息
        QuerySellOrderListRequest request = new QuerySellOrderListRequest();
        request.setSellOrderIds(this.convertSellOrderIds(map.keySet()));
        List<SellOrderInfo> sellOrderInfoList = cardQueryAdminService.querySellOrderList(request)
                .getSellOrderInfo();
        //校验销售单客户ID
        sellOrderInfoList.stream()
                .filter(sellOrderInfo -> !sellOrderInfo.getCustomerId().equals(customerId))
                .forEach(sellOrderInfo ->
                        map.get(sellOrderInfo.getSellOrderId())
                                .forEach(sellOrderCardInfo ->
                                        addErrorCustomerInfo(errorCardsMap, sellOrderCardInfo.getCardId(), "客户信息不一致 "))
                );

    }

    /**
     * 校验卡类型是否可充值，最低充值金额限制
     */
    private void verifyCardType(VerifyRequest verifyRequest, List<SellOrderCardInfo> cardInfoList,
                                Map<String, String> errorCardsMap) {
        //根据卡类型ID聚合分组
        Map<Long, List<SellOrderCardInfo>> map = cardInfoList.stream()
                .collect(Collectors.groupingBy(SellOrderCardInfo::getCardTypeNo));
        CardManageAdminDto.QueryCardManagesRequest request = new CardManageAdminDto.QueryCardManagesRequest();
        request.setCardTypeNos(Lists.newArrayList(map.keySet()));
        List<CardManageAdminDto.CardManage> cardManageList = cardManageAdminService.queryCardManages(request)
                .getCardManage();
        //查询影院所在城市是几线城市
        CinemaDto.CinemaBrief cinemaBrief = cinemaFacade.getCinemaBrief(verifyRequest.getCinemaInnerCode());
        int cityLevel = locationFacade.getCityLevel(cinemaBrief.getCity());
        //是否可充值及充值金额校验
        for (CardManageAdminDto.CardManage cardManage : cardManageList) {
            int minRechargeMoney = convertMinRechargeMoney(cityLevel, cardManage);
            if (cardManage.getRechargeMoney() != 1 || verifyRequest.getRechargeAmount() < minRechargeMoney) {
                map.get(cardManage.getId()).forEach(sellOrderCardInfo -> {
                    if (cardManage.getRechargeMoney() != 1) {
                        addErrorCustomerInfo(errorCardsMap, sellOrderCardInfo.getCardId(), "卡类型不允许充值");
                    }
                    if (verifyRequest.getRechargeAmount() < minRechargeMoney) {
                        addErrorCustomerInfo(errorCardsMap, sellOrderCardInfo.getCardId(), "充值金额小于最低充值金额");
                    }
                });
            }
        }
    }

    private String convertSellOrderId(String sellOrderId) {
        return sellOrderId.startsWith("22_") ? sellOrderId.replace("22_", "") : sellOrderId;
    }

    private List<String> convertSellOrderIds(Set<String> keySet) {
        List<String> sellOrderIds = Lists.newArrayList();
        keySet.forEach(key -> {
            sellOrderIds.add(this.convertSellOrderId(key));
        });
        return sellOrderIds;
    }

    private int convertMinRechargeMoney(int cityLevel, CardManageAdminDto.CardManage cardManage) {
        if (cityLevel == 1) {
            return cardManage.getMinRechargeMoney1();
        } else if (cityLevel == 2) {
            return cardManage.getMinRechargeMoney2();
        } else {
            return cardManage.getMinRechargeMoney3();
        }
    }

    private void addErrorCustomerInfo(Map<String, String> errorCardsMap, String cardNo, String errMsg) {
        String errorInfo = errorCardsMap.get(cardNo);
        if (Strings.isNullOrEmpty(errorInfo)) {
            errorCardsMap.put(cardNo, errMsg);
        } else {
            errorCardsMap.put(cardNo, errorInfo + errMsg);
        }
    }
}
