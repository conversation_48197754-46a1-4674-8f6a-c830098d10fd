package wanda.card.kam.admin.api.controller;

import lombok.extern.slf4j.Slf4j;
import wanda.portal.authority.context.PortalAuthUser;
import wanda.portal.authority.context.UserPrincipal;
import wanda.stark.core.lang.FaultException;

@Slf4j
public class BaseController {

    protected PortalAuthUser getCurrentUser() {
        PortalAuthUser user = null;
        try {
            user = UserPrincipal.current();
        } catch (Exception e) {
            log.warn("获取当前登录用户失败");
            throw new FaultException(-1, "未登陆");
        }
        return user;
    }

    protected PortalAuthUser getTestUser(){
        PortalAuthUser user = new PortalAuthUser();
        PortalAuthUser.CurrentAuthJob currentAuthJob = new PortalAuthUser.CurrentAuthJob();
        currentAuthJob.setRank(PortalAuthUser.Rank.CHAIN);
        user.setId(20000282);
        user.setCurrentAuthJob(currentAuthJob);
        return user;
    }
}
