package wanda.card.kam.admin.api.model;

import lombok.Getter;
import lombok.Setter;

import javax.validation.Valid;
import java.util.List;

/**
 * 充值订单推送相关模型
 *
 * <AUTHOR>
 */
public class RechargeOrderPushModel {

    /**
     * 充值订单推送请求
     */
    @Getter
    @Setter
    public static class RechargeOrderPushRequest {
        /**
         * 充值订单号
         */
        private String rechargeOrderNo;

        /**
         * 合同编码
         */
        private String contractNo;

        /**
         * 客户编码
         */
        private String customerCode;

        /**
         * 客户名称
         */
        private String customerName;

        /**
         * 销售员万信号
         */
        private String sellerId;

        /**
         * 销售员姓名
         */
        private String seller;

        /**
         * 区域编码
         */
        private String areaCode;

        /**
         * 区域名称
         */
        private String areaName;

        /**
         * 影城内码
         */
        private String cinemaInnerCode;

        /**
         * 影城名称
         */
        private String cinemaName;

        /**
         * 每张卡充值金额(分)
         */
        private Integer everyCardRechargeAmount;

        /**
         * 每张卡赠送金额(分)
         */
        private Integer everyCardPresentAmount;

        /**
         * 卡总数
         */
        private Integer count;

        /**
         * 总充值金额（含赠送金额）分,卡数量*单卡(充值金额+赠送金额)
         */
        private Integer amount;

        private String operator;

        /**
         * 卡信息列表
         */
        private List<RechargeOrderCardPushInfo> cardInfos;
    }

    /**
     * 推送的卡信息
     */
    @Getter
    @Setter
    public static class RechargeOrderCardPushInfo {
        /**
         * 卡号
         */
        private String cardNo;

        /**
         * 卡类型编码
         */
        private String cardTypeCode;

        /**
         * 卡类型名称
         */
        private String cardTypeName;
    }

    /**
     * 充值订单推送响应
     */
    @Getter
    @Setter
    public static class RechargeOrderPushResponse {
        /**
         * 推送结果消息
         */
        private String message;

        /**
         * 充值订单号
         */
        private String rechargeOrderNo;

        /**
         * 处理的卡数量
         */
        private Integer cardCount;

        /**
         * 总充值金额（分）
         */
        private Integer totalAmount;

        public RechargeOrderPushResponse() {
        }

        public RechargeOrderPushResponse(String message, String rechargeOrderNo, Integer cardCount, Integer totalAmount) {
            this.message = message;
            this.rechargeOrderNo = rechargeOrderNo;
            this.cardCount = cardCount;
            this.totalAmount = totalAmount;
        }
    }
}
