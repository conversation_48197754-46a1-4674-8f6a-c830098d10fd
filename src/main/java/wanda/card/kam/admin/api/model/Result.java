package wanda.card.kam.admin.api.model;

import lombok.Data;

@Data
public class Result<T>{

    int status;
    String msg;
    T data;

    public static <T> Result<T> success() {
        return success("请求成功", null);
    }

    public static <T> Result<T> success(T data) {
        return success("请求成功", data);
    }

    public static <T> Result<T> success(String message, T data) {
        Result<T> result = new Result<>();
        result.setStatus(0);
        result.setMsg(message);
        result.setData(data);
        return result;
    }

    public static <T> Result<T> simple(int statusCode, String message) {
        Result<T> result = new Result<>();
        result.setStatus(statusCode);
        result.setMsg(message);
        return result;
    }

    public static <T> Result<T> simple(int statusCode, String message, T data) {
        Result<T> result = new Result<>();
        result.setStatus(statusCode);
        result.setMsg(message);
        result.setData(data);
        return result;
    }
}
