package wanda.card.kam.admin.api.facade;

import com.google.common.base.Strings;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import wanda.card.kam.admin.api.mapper.RechargeOrderAdminMapper;
import wanda.card.kam.admin.api.model.RechargeOrderModel;
import wanda.card.kam.admin.contract.RechargeOrderAdminService;
import wanda.card.kam.admin.contract.dto.RechargeOrderAdminDto.*;
import wanda.card.kam.common.contract.constant.CardRechargeStatus;
import wanda.stark.core.data.PageInfo;
import wanda.stark.core.data.PageResult;
import wanda.stark.core.data.R;

import java.time.LocalDate;

@Slf4j
@Component
@AllArgsConstructor
public class RechargeOrderAdminFacade {

    private final RechargeOrderAdminService rechargeOrderAdminService;

    public PageResult<RechargeOrderModel.OrderDetail> list(RechargeOrderModel.ListRequest request) {
        PageResult<RechargeOrderModel.OrderDetail> result = new PageResult<>();
        ListRequest dtoRequest = new ListRequest();
        BeanUtils.copyProperties(request, dtoRequest);
        PageResult<RechargeOrder> response = rechargeOrderAdminService.list(dtoRequest);
        result.setTotalCount(response.getTotalCount());
        result.setItems(RechargeOrderAdminMapper.buildOrderDetail(response.getItems()));
        return result;

    }

    public R<RechargeOrderModel.OrderDetailAndLog> getRechargeOrderDetail(Long id) {
        R<RechargeOrderDetail> response = rechargeOrderAdminService.getRechargeOrderDetail(id);
        if (response.isSuccess()) {
            return R.success(RechargeOrderAdminMapper.of(response.getData()));
        }
        return R.fail(response.getMsg());
    }

    public PageResult<RechargeOrderModel.CardDetailItem> getRechargeOrderCardDetailList(RechargeOrderModel.CardDetailListRequest request) {
        CardDetailListRequest dtoRequest = new CardDetailListRequest();
        dtoRequest.setRechargeOrderNo(request.getRechargeOrderNo());
        dtoRequest.setCardNo(request.getCardNo());
        if (!Strings.isNullOrEmpty(request.getRechargeDateStart())) {
            dtoRequest.setStartTime(LocalDate.parse(request.getRechargeDateStart()).atStartOfDay());
        }
        if (!Strings.isNullOrEmpty(request.getRechargeDateEnd())) {
            dtoRequest.setEndTime(LocalDate.parse(request.getRechargeDateEnd()).atTime(23, 59, 59));
        }
        if (request.getStatus() > 0) {
            dtoRequest.setStatus(CardRechargeStatus.valueOf(request.getStatus()));
        }
        dtoRequest.setPageInfo(new PageInfo(request.getPageIndex(), request.getPageSize()));
        PageResult<RechargeCardDetail> response = rechargeOrderAdminService.getRechargeOrderCardDetailList(dtoRequest);
        PageResult<RechargeOrderModel.CardDetailItem> result = new PageResult<>();
        result.setTotalCount(response.getTotalCount());
        result.setItems(RechargeOrderAdminMapper.of(response.getItems()));
        return result;
    }
}
