package wanda.card.kam.admin.api.facade;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import wanda.location.admin.contract.GovRegionAdminService;
import wanda.location.common.contract.dto.GovRegionDto;

@Slf4j
@Component
@AllArgsConstructor
public class LocationFacade {

    private final GovRegionAdminService govRegionAdminService;

    public int getCityLevel(Integer cityId){
       String cityLevel = this.getCityInfo(cityId).getCityLevel();
       switch (cityLevel){
           case "01":
               return 1;
           case "02":
               return 2;
           default:
               return 3;
       }
    }
    private GovRegionDto.GovRegion getCityInfo(Integer cityId){
        GovRegionDto.GovRegion govRegion = govRegionAdminService.findGovRegion(cityId);
        return govRegion;
    }


}
