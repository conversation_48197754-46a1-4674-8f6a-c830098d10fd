package wanda.card.kam.admin.api.mapper;

import com.google.common.collect.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import wanda.card.kam.admin.api.model.RechargeOrderModel;
import wanda.card.kam.admin.api.model.RechargeOrderModel.RechargeOrderLog;
import wanda.card.kam.admin.api.model.RechargeOrderModel.OrderDetailAndLog;
import wanda.card.kam.admin.contract.dto.RechargeOrderAdminDto;
import wanda.card.kam.common.contract.utils.DateUtil;
import wanda.card.kam.common.contract.utils.StringUtil;

import java.util.List;

public class RechargeOrderAdminMapper {

    public static OrderDetailAndLog of(RechargeOrderAdminDto.RechargeOrderDetail dto){
        OrderDetailAndLog detail = new OrderDetailAndLog();
        BeanUtils.copyProperties(dto, detail);
        detail.setStatus(dto.getStatus().value());
        detail.setStatusName(dto.getStatus().displayName());
        detail.setCreateTime(DateUtil.dateTime2Str(dto.getCreateTime()));
        List<RechargeOrderLog> logs = Lists.newArrayList();
        RechargeOrderLog log;
        if (!CollectionUtils.isEmpty(dto.getLogs())){
            for (RechargeOrderAdminDto.OrderLog logDto : dto.getLogs()){
                log = new RechargeOrderLog();
                log.setCreateTime(DateUtil.dateTime2Str(logDto.getCreateTime()));
                log.setOperationType(logDto.getLogType().value()+"");
                log.setOperationLog(logDto.getOperationLog());
                logs.add(log);
            }
        }
        detail.setRechargeOrderLogs(logs);
        return detail;
    }

    public static List<RechargeOrderModel.CardDetailItem> of(List<RechargeOrderAdminDto.RechargeCardDetail> dtoList){
        List<RechargeOrderModel.CardDetailItem> list = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(dtoList)){
            RechargeOrderModel.CardDetailItem detail;
            for (RechargeOrderAdminDto.RechargeCardDetail dtoDto : dtoList){
                detail = new RechargeOrderModel.CardDetailItem();
                BeanUtils.copyProperties(dtoList, detail);
                detail.setCardNo(StringUtil.doSensitive(dtoDto.getCardNo()));
                detail.setStatus(dtoDto.getStatus().value());
                detail.setStatusName(dtoDto.getStatus().displayName());
                detail.setRechargeTime(DateUtil.dateTime2Str(dtoDto.getRechargeTime()));
                list.add(detail);
            }
        }
        return list;
    }

    public static List<RechargeOrderModel.OrderDetail> buildOrderDetail(List<RechargeOrderAdminDto.RechargeOrder> items) {
        List<RechargeOrderModel.OrderDetail> list = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(items)){
            RechargeOrderModel.OrderDetail detail;
            for (RechargeOrderAdminDto.RechargeOrder dtoOrder : items){
                detail = new RechargeOrderModel.OrderDetail();
                BeanUtils.copyProperties(dtoOrder, detail);
                detail.setStatus(dtoOrder.getStatus().value());
                detail.setStatusName(dtoOrder.getStatus().displayName());
                detail.setCreateTime(DateUtil.dateTime2Str(dtoOrder.getCreateTime()));
                list.add(detail);
            }
        }
        return list;
    }
}
