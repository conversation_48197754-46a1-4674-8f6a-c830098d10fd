package wanda.card.kam.admin.api.controller;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import wanda.card.kam.admin.api.facade.RechargeOrderAdminFacade;
import wanda.card.kam.admin.api.model.RechargeOrderModel.*;
import wanda.portal.authority.context.PortalAuthUser;
import wanda.stark.core.data.PageResult;
import wanda.stark.core.data.R;

import javax.validation.Valid;

/**
 * 卡续充充值订单管理后台API
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("card-kam/recharge-order")
public class KamRechargeCardOrderAdminController extends BaseController{

    private final RechargeOrderAdminFacade adminFacade;

    /**
     * @api {POST} /card-kam/recharge-order/list 1.销售单列表查询
     * @apiVersion 1.0.0
     * @apiGroup 卡续充充值订单管理
     * @apiName RechargeOrderList
     * @apiDescription 查询卡续充销售单列表数据，支持筛选、分页
     * @apiParam (请求体) {String} contractNo 合同编码（精准查询）
     * @apiParam (请求体) {String} customerCode 客户编码（精准查询）
     * @apiParam (请求体) {String} customerName 客户名称（模糊查询）
     * @apiParam (请求体) {String} seller 销售员（模糊查询）
     * @apiParam (请求体) {String} rechargeOrderNo 充值订单号（精准查询）
     * @apiParam (请求体) {String} createTimeStart 创建时间开始 yyyy-mm-dd
     * @apiParam (请求体) {String} createTimeEnd 创建时间结束 yyyy-mm-dd
     * @apiParam (请求体) {Number} status 0:全部, 1:待充值, 2:部分充值, 3:充值成功, 4:充值失败
     * @apiParam (请求体) {String} areaCode 区域代码
     * @apiParam (请求体) {String} cinemaInnerCode 影城内码
     * @apiParam (请求体) {Number} pageIndex 页码，从1开始
     * @apiParam (请求体) {Number} pageSize 每页数量
     * @apiParamExample 请求体示例
     * {"seller":"PNd9","areaCode":"PVD","rechargeOrderNo":"kroF8jyMey","pageIndex":2652,"contractNo":"Omg","createTimeEnd":"snRWxJmWEo","customerCode":"uW","cinemaInnerCode":"M3","pageSize":3728,"createTimeStart":"Xrf4","customerName":"B4","status":7060}
     * @apiSuccess (响应结果) {Array} items
     * @apiSuccess (响应结果) {Number} items.id 订单id
     * @apiSuccess (响应结果) {String} items.rechargeOrderNo 充值订单号
     * @apiSuccess (响应结果) {String} items.contractNo 合同编码
     * @apiSuccess (响应结果) {String} items.customerCode 客户编码
     * @apiSuccess (响应结果) {String} items.customerName 客户名称
     * @apiSuccess (响应结果) {String} items.sellerId 销售员万信号
     * @apiSuccess (响应结果) {String} items.seller 销售员姓名
     * @apiSuccess (响应结果) {String} items.areaName 区域
     * @apiSuccess (响应结果) {String} items.cinemaName 影城
     * @apiSuccess (响应结果) {Number} items.count 卡数量
     * @apiSuccess (响应结果) {Number} items.amount 总充值金额（元）
     * @apiSuccess (响应结果) {Number} items.status 状态 1:待充值, 2:部分充值, 3:充值成功, 4:充值失败
     * @apiSuccess (响应结果) {String} items.statusName 状态显示名称
     * @apiSuccess (响应结果) {String} items.createTime 创建时间 yyyy-mm-dd hh:mm:ss
     * @apiSuccess (响应结果) {Number} totalCount
     * @apiSuccessExample 响应结果示例
     * {"totalCount":1293,"items":[{"seller":"8t","amount":1494,"rechargeOrderNo":"Ro6j6GLJ","contractNo":"7Xku","count":8683,"customerCode":"Zqzm0Lxp","customerName":"Iz","sellerId":"QHf","areaName":"HW4tx3","createTime":"ZJ","cinemaName":"FsdGbv","statusName":"pQ1P","id":7841,"status":6425}]}
     */
    @PostMapping("/list")
    public PageResult<OrderDetail> list(@Valid @RequestBody ListRequest request) {
        log.info("销售单列表查询入参:{}", request);
        PortalAuthUser user = getCurrentUser();
        PortalAuthUser.Rank userRank = user.getCurrentAuthJob().getRank();
        if (userRank == PortalAuthUser.Rank.AREA) {
            request.setAreaCode(user.getCurrentAuthJob().getAreaId());
        } else if (userRank == PortalAuthUser.Rank.CINEMA) {
            request.setCinemaInnerCode(user.getCurrentAuthJob().getCinemaCode());
        }
        return adminFacade.list(request);
    }

    /**
     * @api {POST} /card-kam/recharge-order/export 2.销售单列表数据导出
     * @apiVersion 1.0.0
     * @apiGroup 卡续充充值订单管理
     * @apiName RechargeOrderExport
     * @apiDescription 查询卡续充销售单列表数据，支持筛选导出
     * @apiParam (请求体) {String} contractNo 合同编码（精准查询）
     * @apiParam (请求体) {String} customerCode 客户编码（精准查询）
     * @apiParam (请求体) {String} customerName 客户名称（模糊查询）
     * @apiParam (请求体) {String} seller 销售员（模糊查询）
     * @apiParam (请求体) {String} rechargeOrderNo 充值订单号（精准查询）
     * @apiParam (请求体) {String} createTimeStart 创建时间开始 yyyy-mm-dd
     * @apiParam (请求体) {String} createTimeEnd 创建时间结束 yyyy-mm-dd
     * @apiParam (请求体) {Number} status 0:全部, 1:待充值, 2:部分充值, 3:充值成功, 4:充值失败
     * @apiParam (请求体) {String} areaCode 区域代码
     * @apiParam (请求体) {String} cinemaInnerCode 影城内码
     * @apiParamExample 请求体示例
     * {"seller":"DR67LAS","areaCode":"Bf0GmufYz","rechargeOrderNo":"cK","contractNo":"Figohzg","createTimeEnd":"MGgmhc2L","customerCode":"6x","cinemaInnerCode":"ln4Cui","createTimeStart":"Bi9Nv","customerName":"E","status":225}
     * @apiSuccess (响应结果) {Number} code 0成功 非0失败
     * @apiSuccess (响应结果) {String} msg 失败信息
     * @apiSuccess (响应结果) {Number} data 导出id
     * @apiSuccessExample 响应结果示例
     * {"msg":"w7Ltybj","code":9655,"data":2623}
     */
    @PostMapping("/export")
    public R<Long> export(@Valid @RequestBody ListRequest request) {
        log.info("销售单列表导出入参:{}", request);

        // TODO: 调用service层导出数据
        Long exportId = 123L;

        log.info("销售单列表导出返回导出id:{}", exportId);
        return R.success(exportId);
    }

    /**
     * @api {GET} /card-kam/recharge-order/detail 3.销售单详情查询
     * @apiVersion 1.0.0
     * @apiGroup 卡续充充值订单管理
     * @apiName RechargeOrderDetail
     * @apiDescription 查询卡续充销售单详情数据
     * @apiParam (请求参数) {Number} id 订单id
     * @apiParamExample 请求参数示例
     * id=8431
     * @apiSuccess (响应结果) {Number} code 0成功 非0失败
     * @apiSuccess (响应结果) {String} msg 失败信息
     * @apiSuccess (响应结果) {Object} data
     * @apiSuccess (响应结果) {Array} data.rechargeOrderLogs 变更日志列表
     * @apiSuccess (响应结果) {Number} data.rechargeOrderLogs.id 日志id
     * @apiSuccess (响应结果) {String} data.rechargeOrderLogs.createTime 时间 yyyy-mm-dd hh:mm:ss
     * @apiSuccess (响应结果) {String} data.rechargeOrderLogs.operationType 操作类型
     * @apiSuccess (响应结果) {String} data.rechargeOrderLogs.operationLog 操作日志
     * @apiSuccess (响应结果) {Number} data.id 订单id
     * @apiSuccess (响应结果) {String} data.rechargeOrderNo 充值订单号
     * @apiSuccess (响应结果) {String} data.contractNo 合同编码
     * @apiSuccess (响应结果) {String} data.customerCode 客户编码
     * @apiSuccess (响应结果) {String} data.customerName 客户名称
     * @apiSuccess (响应结果) {String} data.sellerId 销售员万信号
     * @apiSuccess (响应结果) {String} data.seller 销售员姓名
     * @apiSuccess (响应结果) {String} data.areaName 区域
     * @apiSuccess (响应结果) {String} data.cinemaName 影城
     * @apiSuccess (响应结果) {Number} data.count 卡数量
     * @apiSuccess (响应结果) {Number} data.amount 总充值金额（元）
     * @apiSuccess (响应结果) {Number} data.status 状态 1:待充值, 2:部分充值, 3:充值成功, 4:充值失败
     * @apiSuccess (响应结果) {String} data.statusName 状态显示名称
     * @apiSuccess (响应结果) {String} data.createTime 创建时间 yyyy-mm-dd hh:mm:ss
     * @apiSuccessExample 响应结果示例
     * {"msg":"SqPxJ","code":2198,"data":{"seller":"EUdHOb2A","amount":"x","rechargeOrderNo":"pCy4","contractNo":"aTdM","count":5512,"customerCode":"7rSQALpqW","rechargeOrderLogs":[{"operationLog":"OYdwP","createTime":"hg","operationType":"cO8","id":838}],"customerName":"wty7ZJyRKI","sellerId":"DvkNNQow6","areaName":"PCGSnM8rX1","createTime":"uZ8mb2CseD","cinemaName":"6W8dKBtiEe","statusName":"uDGgzI0v9","id":8080,"status":8143}}
     */
    @GetMapping("/detail")
    public R<OrderDetailAndLog> detail(@RequestParam("id") Long id) {
        log.info("销售单详情查询入参id:{}", id);
        return adminFacade.getRechargeOrderDetail(id);
    }

    /**
     * @api {POST} /card-kam/recharge-order/card-detail/list 4.销售单卡充值明细列表查询
     * @apiVersion 1.0.0
     * @apiGroup 卡续充充值订单管理
     * @apiName RechargeOrderCardDetailList
     * @apiDescription 查询卡续充销售单卡充值明细列表数据，支持筛选、分页
     * @apiParam (请求体) {String} rechargeOrderNo 充值订单号（必填）
     * @apiParam (请求体) {String} cardNo 卡号（精确查询）
     * @apiParam (请求体) {String} rechargeDateStart 充值时间开始 yyyy-mm-dd
     * @apiParam (请求体) {String} rechargeDateEnd 充值时间结束 yyyy-mm-dd
     * @apiParam (请求体) {Number} status 充值状态 0:全部 1:待充值 2:充值中 3:充值成功 4:充值失败
     * @apiParam (请求体) {Number} pageIndex 页码，从1开始
     * @apiParam (请求体) {Number} pageSize 每页数量
     * @apiParamExample 请求体示例
     * {"rechargeTimeStart":"CUy2hh0","rechargeOrderNo":"TkYia3yeW","rechargeTimeEnd":"28p","pageSize":6443,"cardNo":"BeETDqGX","pageIndex":7586,"status":3124}
     * @apiSuccess (响应结果) {Number} code 0成功 非0失败
     * @apiSuccess (响应结果) {String} msg 失败信息
     * @apiSuccess (响应结果) {Object} data
     * @apiSuccess (响应结果) {Array} data.items
     * @apiSuccess (响应结果) {String} data.items.cardNo 卡号（脱敏展示）
     * @apiSuccess (响应结果) {String} data.items.cardTypeCode 卡类型编码
     * @apiSuccess (响应结果) {String} data.items.cardTypeName 卡类型名称
     * @apiSuccess (响应结果) {String} data.items.rechargeAmount 充值金额（元）
     * @apiSuccess (响应结果) {String} data.items.presentAmount 赠送金额（元）
     * @apiSuccess (响应结果) {String} data.items.changeBalance 余额变动（充值金额+赠送金额）（元）
     * @apiSuccess (响应结果) {String} data.items.beforeBalance 充值前余额（元）
     * @apiSuccess (响应结果) {String} data.items.afterBalance 充值后余额（元）
     * @apiSuccess (响应结果) {Number} data.items.status 充值状态 1:待充值 2:充值中 3:充值成功 4:充值失败
     * @apiSuccess (响应结果) {String} data.items.statusName 充值状态显示名称
     * @apiSuccess (响应结果) {String} data.items.rechargeTime 充值时间 yyyy-mm-dd hh:mm:ss
     * @apiSuccess (响应结果) {Number} data.totalCount
     * @apiSuccessExample 响应结果示例
     * {"msg":"IG","code":5820,"data":{"totalCount":5677,"items":[{"changeBalance":"ymavB","rechargeAmount":"nHvNd","cardTypeName":"8cnKHS7Y","statusName":"lkuOFLTsU7","rechargeTime":"RKhQ","cardTypeCode":"wgE7KOaN","presentAmount":"2","beforeBalance":"Vh6N","afterBalance":"bIRciKD8","cardNo":"yvW52","status":8928}]}}
     */
    @PostMapping("/card-detail/list")
    public PageResult<CardDetailItem> cardDetailList(@Valid @RequestBody CardDetailListRequest request) {
        log.info("销售单充值明细列表查询入参:{}", request);

       return adminFacade.getRechargeOrderCardDetailList(request);
    }
}
