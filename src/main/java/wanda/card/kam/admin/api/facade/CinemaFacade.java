package wanda.card.kam.admin.api.facade;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import wanda.mdm.cinema.admin.contract.CinemaAdminService;
import wanda.mdm.cinema.common.contract.dto.CinemaDto;

@Slf4j
@Component
@AllArgsConstructor
public class CinemaFacade {

    private final CinemaAdminService cinemaAdminService;

    public CinemaDto.CinemaBrief getCinemaBrief(String cinemaInnerCode) {
        return cinemaAdminService.getCinemaBriefByInnerCode(cinemaInnerCode);
    }
}
