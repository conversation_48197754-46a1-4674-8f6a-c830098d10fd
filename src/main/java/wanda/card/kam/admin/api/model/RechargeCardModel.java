package wanda.card.kam.admin.api.model;

import lombok.Getter;
import lombok.Setter;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.util.List;

public class RechargeCardModel {
    /**
     * 充值订单充值请求模型
     */
    @Getter
    @Setter
    public static class RechargeCardRequest {
        /**
         * 充值请求流水号(批次号)
         */
        private String batchNo;
        /**
         * 合同号
         */
        private String contractNo;
        /**
         * 充值订单号
         */
        private String rechargeOrderNo;
        /**
         * 每张卡充值金额（分）
         */
        private Integer everyCardRechargeAmount;

        /**
         * 每张卡赠送金额（分）
         */
        private Integer everyCardPresentAmount;

        private String operator;

        /**
         * 卡号列表
         */
        private List<String> cardNos;
    }

}
