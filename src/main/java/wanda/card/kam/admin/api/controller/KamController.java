package wanda.card.kam.admin.api.controller;

import com.alibaba.fastjson.JSON;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import wanda.card.kam.admin.api.facade.KamFacade;
import wanda.card.kam.admin.api.model.PushMapper;
import wanda.card.kam.admin.api.model.RechargeCardModel;
import wanda.card.kam.admin.api.model.RechargeOrderPushModel.RechargeOrderPushRequest;
import wanda.card.kam.admin.api.model.RechargeVerifyModel.OrderUpdateRequest;
import wanda.card.kam.admin.api.model.RechargeVerifyModel.VerifyRequest;
import wanda.card.kam.admin.api.model.RechargeVerifyModel.VerifyResponse;
import wanda.card.kam.admin.api.model.Result;
import wanda.card.kam.admin.contract.RechargeOrderService;
import wanda.card.kam.admin.contract.dto.RechargeOrderPushDto;
import wanda.stark.core.data.R;
import wanda.stark.web.annotation.IgnoreResponseAdvice;

import javax.validation.Valid;

/**
 * 给财管使用
 * qas域名:http://gateway-admin-b-qas.wandafilm.com
 * stg域名:http://gateway-admin-b-stg.wandafilm.com
 * prd域名:http://gateway-admin-b.wandafilm.com
 */
@Slf4j
@RestController
@RequestMapping("/card-kam/ysht")
@AllArgsConstructor
@IgnoreResponseAdvice
public class KamController {
    private final PushMapper pushMapper;
    private final RechargeOrderService rechargeOrderService;
    private final KamFacade kamFacade;

    /**
     * 充值验证
     */
    @PostMapping(value = "recharge/verify")
    public Result<VerifyResponse> rechargeVerify(@RequestBody VerifyRequest request) {
        log.info("充值校验 request:{}", request);
        return kamFacade.rechargeVerify(request);
    }

    /**
     * 充值订单变更接口
     */
    @PostMapping(value = "/recharge-order/update")
    public Result<Object> rechargeOrderUpdate(@RequestBody OrderUpdateRequest request) {
        log.info("充值订单变更 request:{}", request);
        return kamFacade.rechargeOrderUpdate(request);
    }

    @PostMapping(value = "recharge-order/push")
    public Result<Void> rechargeOrderPush(@RequestBody RechargeOrderPushRequest request) {
        log.info("充值订单推送入参:{}", request);
        RechargeOrderPushDto.RechargeOrderPushRequest rpcRequest = pushMapper.convertToRpcRequest(request);
        try {
            R<Void> rpcResponse = rechargeOrderService.pushRechargeOrder(rpcRequest);
            log.info("充值订单推送返回:{}", JSON.toJSONString(rpcResponse));
            if (rpcResponse.isSuccess())
                return Result.success();
            return Result.simple(-1, rpcResponse.getMsg());
        } catch (Exception e) {
            log.error(">>>充值订单推送异常", e);
            return Result.simple(-1, e.getMessage());
        }
    }

    @PostMapping(value = "recharge-order/recharge")
    public Result<Void> rechargeCard(@Valid @RequestBody RechargeCardModel.RechargeCardRequest request) {
        log.info("充值订单充值入参:{}", request);
        try {
            wanda.card.kam.admin.contract.dto.RechargeOrderRechargeDto.RechargeOrderRechargeRequest rpcRequest =
                    new wanda.card.kam.admin.contract.dto.RechargeOrderRechargeDto.RechargeOrderRechargeRequest();
            rpcRequest.setBatchNo(request.getBatchNo());
            rpcRequest.setContractNo(request.getContractNo());
            rpcRequest.setRechargeOrderNo(request.getRechargeOrderNo());
            rpcRequest.setEveryCardRechargeAmount(request.getEveryCardRechargeAmount());
            rpcRequest.setEveryCardPresentAmount(request.getEveryCardPresentAmount());
            rpcRequest.setOperator(request.getOperator());
            rpcRequest.setCardNos(request.getCardNos());

            R<Void> rpcResponse = rechargeOrderService.rechargeCard(rpcRequest);
            log.info("充值订单充值返回:{}", JSON.toJSONString(rpcResponse));
            if (rpcResponse.isSuccess())
                return Result.success();
            return Result.simple(-1, rpcResponse.getMsg());
        } catch (Exception e) {
            log.error(">>>充值订单充值异常", e);
            return Result.simple(-1, e.getMessage());
        }
    }


}
