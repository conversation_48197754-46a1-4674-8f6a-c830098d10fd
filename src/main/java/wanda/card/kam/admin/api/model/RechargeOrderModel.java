package wanda.card.kam.admin.api.model;

import lombok.Getter;
import lombok.Setter;
import wanda.stark.core.data.PageResult;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 充值订单管理相关模型
 *
 * <AUTHOR>
 */
public class RechargeOrderModel {

    /**
     * 分页请求基类
     */
    @Getter
    @Setter
    public static class PageRequest {
        /**
         * 页码，从1开始
         */
        @Min(value = 1, message = "页码必须大于0")
        private Integer pageIndex = 1;

        /**
         * 每页数量
         */
        @Min(value = 1, message = "每页数量必须大于0")
        @Max(value = 100, message = "每页数量不能超过100")
        private Integer pageSize = 10;
    }

    /**
     * 充值订单列表查询请求
     */
    @Getter
    @Setter
    public static class ListRequest extends PageRequest {
        /**
         * 合同编码（精准查询）
         */
        private String contractNo;

        /**
         * 客户编码（精准查询）
         */
        private String customerCode;

        /**
         * 客户名称（模糊查询）
         */
        private String customerName;

        /**
         * 销售员（模糊查询）
         */
        private String seller;

        /**
         * 充值订单号（精准查询）
         */
        private String rechargeOrderNo;

        /**
         * 创建时间开始 yyyy-mm-dd
         */
        private String createTimeStart;

        /**
         * 创建时间结束 yyyy-mm-dd
         */
        private String createTimeEnd;

        /**
         * 0:全部, 1:待充值, 2:部分充值, 3:充值成功, 4:充值失败
         */
        private int status;

        /**
         * 区域代码
         */
        private String areaCode;

        /**
         * 影城内码
         */
        private String cinemaInnerCode;
    }

    /**
     * 充值订单列表响应
     */
    @Getter
    @Setter
    public static class ListResponse extends PageResult<OrderDetail> {
    }

    /**
     * 充值订单列表项
     */
    @Getter
    @Setter
    public static class OrderDetail {
        /**
         * 订单id
         */
        private Long id;
        /**
         * 充值订单号
         */
        private String rechargeOrderNo;

        /**
         * 合同编码
         */
        private String contractNo;

        /**
         * 客户编码
         */
        private String customerCode;

        /**
         * 客户名称
         */
        private String customerName;

        /**
         * 销售员万信号
         */
        private String sellerId;

        /**
         * 销售员姓名
         */
        private String seller;

        /**
         * 区域
         */
        private String areaName;

        /**
         * 影城
         */
        private String cinemaName;

        /**
         * 卡数量
         */
        private Integer count;

        /**
         * 总充值金额（元）
         */
        private Integer amount;

        /**
         * 状态 1:待充值, 2:部分充值, 3:充值成功, 4:充值失败
         */
        private int status;

        /**
         * 状态显示名称
         */
        private String statusName;

        /**
         * 创建时间 yyyy-mm-dd hh:mm:ss
         */
        private String createTime;
    }

    /**
     * 充值订单详情响应
     */
    @Getter
    @Setter
    public static class OrderDetailAndLog extends OrderDetail {
        /**
         * 变更日志列表
         */
        private List<RechargeOrderLog> rechargeOrderLogs;
    }

    /**
     * 变更日志
     */
    @Getter
    @Setter
    public static class RechargeOrderLog {
//        /**
//         * 日志id
//         */
//        private Long id;
        /**
         * 时间 yyyy-mm-dd hh:mm:ss
         */
        private String createTime;

        /**
         * 操作类型
         */
        private String operationType;

        /**
         * 操作日志
         */
        private String operationLog;
    }

    /**
     * 充值明细列表查询请求
     */
    @Getter
    @Setter
    public static class CardDetailListRequest extends PageRequest {
        /**
         * 充值订单号（必填）
         */
        @NotBlank(message = "充值订单号不能为空")
        private String rechargeOrderNo;

        /**
         * 卡号（精确查询）
         */
        private String cardNo;

        /**
         * 充值时间开始 yyyy-mm-dd
         */
        private String rechargeDateStart;

        /**
         * 充值时间结束 yyyy-mm-dd
         */
        private String rechargeDateEnd;

        /**
         * 充值状态 0:全部 1:待充值 2:充值中 3:充值成功 4:充值失败
         */
        private int status;
    }

    /**
     * 充值明细列表项
     */
    @Getter
    @Setter
    public static class CardDetailItem {
        /**
         * 卡号（脱敏展示）
         */
        private String cardNo;

        /**
         * 卡类型编码
         */
        private String cardTypeCode;

        /**
         * 卡类型名称
         */
        private String cardTypeName;

        /**
         * 充值金额（元）
         */
        private String rechargeAmount;

        /**
         * 赠送金额（元）
         */
        private String presentAmount;

        /**
         * 余额变动（充值金额+赠送金额）（元）
         */
        private String changeBalance;

        /**
         * 充值前余额（元）
         */
        private String beforeBalance;

        /**
         * 充值后余额（元）
         */
        private String afterBalance;

        /**
         * 充值状态 1:待充值 2:充值中 3:充值成功 4:充值失败
         */
        private int status;

        /**
         * 充值状态显示名称
         */
        private String statusName;

        /**
         * 充值时间 yyyy-mm-dd hh:mm:ss
         */
        private String rechargeTime;
    }

    /**
     * 充值明细列表响应
     */
    @Getter
    @Setter
    public static class CardDetailListResponse extends PageResult<CardDetailItem> {
    }
}
