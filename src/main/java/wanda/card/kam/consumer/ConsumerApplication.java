package wanda.card.kam.consumer;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.annotation.ComponentScan;

@SpringBootApplication
@EnableDiscoveryClient
@ComponentScan(basePackages = {"wanda.card.kam"})
public class ConsumerApplication {

    public static void main(String[] args) {
        // System.setProperty("rocketmq.client.logUseSlf4j", "true");
        SpringApplication.run(ConsumerApplication.class, args);
    }
}