package wanda.card.kam.consumer.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import wanda.card.kam.common.contract.constant.PushStatus;
import wanda.card.kam.common.provider.dao.RechargeOrderLogDao;
import wanda.card.kam.consumer.service.biz.NotifyFinanceBiz;
import wanda.card.kam.consumer.service.biz.RechargeProcessBiz;

/**
 * 充值订单消费者服务
 */
@Slf4j
@Service
public class RechargeOrderConsumerService {

    @Autowired
    private NotifyFinanceBiz notifyFinanceBiz;

    @Autowired
    private RechargeProcessBiz rechargeProcessBiz;

    @Autowired
    private RechargeOrderLogDao rechargeOrderLogDao;

    /**
     * 处理批次充值消息
     *
     * @param batchNo 批次号
     */
    public void processBatchRecharge(String batchNo) {
        log.info("开始处理批次充值消息，批次号：{}", batchNo);
        try {
            // 处理充值逻辑
            rechargeProcessBiz.processRecharge(batchNo);
            log.info("批次充值处理完成，批次号：{}", batchNo);
        } catch (Exception e) {
            log.error("批次充值处理失败，批次号：{}", batchNo, e);
            throw e;
        }
    }

    /**
     * 处理充值结果通知消息
     *
     * @param batchNo 批次号
     */
    public void processRechargeNotify(String batchNo) {
        log.info("开始处理充值结果通知消息，批次号：{}", batchNo);
        try {
            notifyFinanceBiz.notifyFinanceSystem(batchNo);
            rechargeOrderLogDao.updatePushStatus(batchNo, PushStatus.PUSH_SUCCESS);
            log.info("充值结果通知处理完成，批次号：{}", batchNo);
        } catch (Exception e) {
            rechargeOrderLogDao.updatePushStatus(batchNo, PushStatus.PUSH_FAIL);
            log.error("充值结果通知处理失败，批次号：{}", batchNo, e);
            throw e;
        }
    }
}