package wanda.card.kam.consumer.service.biz;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import wanda.card.kam.common.contract.constant.CardRechargeStatus;
import wanda.card.kam.common.provider.dao.RechargeOrderCardInfoDao;
import wanda.card.kam.common.provider.dao.RechargeOrderDao;
import wanda.card.kam.common.provider.dao.RechargeOrderLogDao;
import wanda.card.kam.common.provider.entity.RechargeOrder;
import wanda.card.kam.common.provider.entity.RechargeOrderCardInfo;
import wanda.card.kam.common.provider.entity.RechargeOrderLog;
import wanda.card.kam.consumer.service.FinanceSystemHttpClient;
import wanda.card.kam.httpclient.request.NotifyRechargeResultRequest;
import wanda.stark.core.lang.FaultException;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 财管系统通知业务逻辑
 */
@Slf4j
@Service
public class NotifyFinanceBiz {

    @Autowired
    private RechargeOrderLogDao rechargeOrderLogDao;

    @Autowired
    private RechargeOrderDao rechargeOrderDao;

    @Autowired
    private RechargeOrderCardInfoDao rechargeOrderCardInfoDao;

    @Autowired
    private FinanceSystemHttpClient financeSystemHttpClient;

    /**
     * 通知财管系统充值结果
     *
     * @param batchNo 批次号
     */
    public void notifyFinanceSystem(String batchNo) {
        log.info("开始通知财管系统充值结果，批次号：{}", batchNo);
        try {
            // 根据批次号查询所有卡信息 - 使用正确的关联查询路径
            RechargeOrder rechargeOrder = getOrderAndRechargeCardInfosByBatchNo(batchNo);
            if (rechargeOrder == null || rechargeOrder.getCardInfos().isEmpty()) {
                log.warn("批次号 {} 下没有找到充值卡信息", batchNo);
                return;
            }
            // 构建财管系统通知请求
            NotifyRechargeResultRequest request = buildFinanceNotifyRequest(rechargeOrder);
            callFinanceSystemApi(request);
        } catch (Exception e) {
            log.error("通知财管系统异常，批次号：{}", batchNo, e);
            throw new FaultException("通知财管系统异常: " + e.getMessage());
        }
    }

    /**
     * 根据批次号获取充值卡信息列表
     * 查询路径：batchNo → RechargeOrderLog → RechargeOrder + BatchRechargeCardDetailLog → 遍历卡号 → 按订单号和卡号查询卡信息
     *
     * @param batchNo 批次号
     * @return 充值卡信息列表
     */
    private RechargeOrder getOrderAndRechargeCardInfosByBatchNo(String batchNo) {
        try {
            // 1. 先查log获取对应的order
            RechargeOrderLog orderLog = rechargeOrderLogDao.findByBatchNo(batchNo);
            if (orderLog == null) {
                log.warn("批次号 {} 对应的充值订单日志不存在", batchNo);
                return null;
            }

            // 2. 查询对应的充值订单
            RechargeOrder rechargeOrder = rechargeOrderDao.findByRechargeOrderNo(orderLog.getRechargeOrderNo());
            if (rechargeOrder == null) {
                log.warn("充值订单号 {} 对应的充值订单不存在", orderLog.getRechargeOrderNo());
                return null;
            }
            rechargeOrder.setBatchNo(batchNo);

            // 3. 遍历batchLog里的卡号，按订单号和卡号查询卡信息
            List<RechargeOrderCardInfo> cardInfos = rechargeOrderCardInfoDao.findByBatchNo(batchNo);
            rechargeOrder.setCardInfos(cardInfos);
            log.info("批次号 {} 查询到 {} 张卡信息", batchNo, cardInfos.size());
            return rechargeOrder;
        } catch (Exception e) {
            log.error("根据批次号查询充值卡信息异常，批次号：{}", batchNo, e);
            throw new FaultException("查询充值卡信息异常: " + e.getMessage());
        }
    }

    /**
     * 构建财管系统通知请求
     *
     * @return 财管系统通知请求
     */
    private NotifyRechargeResultRequest buildFinanceNotifyRequest(RechargeOrder rechargeOrder) {
        List<RechargeOrderCardInfo> rechargeCards = rechargeOrder.getCardInfos();
        if (rechargeCards == null || rechargeCards.isEmpty()) {
            throw new FaultException("充值卡信息列表为空");
        }
        NotifyRechargeResultRequest request = new NotifyRechargeResultRequest();
        request.setBatchNo(rechargeOrder.getBatchNo()); // 批次号
        request.setContractNo(rechargeOrder.getContractNo()); // 合同号
        request.setRechargeOrderNo(rechargeOrder.getRechargeOrderNo()); // 充值订单号
        request.setEveryCardRechargeAmount(rechargeOrder.getEveryCardRechargeAmount());//每张卡充值金额
        request.setEveryCardPresentAmount(rechargeOrder.getEveryCardPresentAmount()); // 每张卡赠送金额
        request.setCardInfos(rechargeCards.stream().map(NotifyFinanceBiz::getCardInfo).collect(Collectors.toList()));
        log.info("构建财管系统通知请求完成，批次号：{}，卡数量：{}",
                request.getBatchNo(), request.getCardInfos().size());
        return request;
    }

    private static NotifyRechargeResultRequest.CardInfo getCardInfo(RechargeOrderCardInfo cardInfo) {
        NotifyRechargeResultRequest.CardInfo cardInfoDto = new NotifyRechargeResultRequest.CardInfo();
        cardInfoDto.setCardNo(cardInfo.getCardNo());
        // 根据充值状态设置结果
        if (CardRechargeStatus.SUCCESS.equals(cardInfo.getStatus())) {
            cardInfoDto.setRechargeResult(true);
            cardInfoDto.setRechargeFailResult(null);
        } else {
            cardInfoDto.setRechargeResult(false);
            cardInfoDto.setRechargeFailResult(cardInfo.getErrorMsg());
        }
        return cardInfoDto;
    }

    /**
     * 调用财管系统API（模拟实现）
     *
     * @param request 请求参数
     * @return 响应结果
     */
    private void callFinanceSystemApi(NotifyRechargeResultRequest request) {
        log.info("调用财管系统API，批次号：{}，卡数量：{}",
                request.getBatchNo(), request.getCardInfos().size());
        try {
            financeSystemHttpClient.notifyFinanceSystem(request);
        } catch (Exception e) {
            log.error("调用财管系统API异常", e);
            throw new FaultException("调用财管系统API异常");
        }
    }
}