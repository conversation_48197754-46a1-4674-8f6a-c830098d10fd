package wanda.card.kam.consumer.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import wanda.card.kam.common.provider.util.RetryUtil;
import wanda.card.kam.httpclient.request.NotifyRechargeResultRequest;
import wanda.card.kam.httpclient.response.NotifyRechargeResultResponse;
import wanda.cloud.autoconfigure.tools.httpclient.HttpClients;
import wanda.stark.core.codec.JsonUtils;
import wanda.stark.tools.httpclient.HttpClient;

@Slf4j
@Service
public class FinanceSystemHttpClient {
    @Autowired
    HttpClients httpClients;

    public void notifyFinanceSystem(NotifyRechargeResultRequest request) {
//        RetryUtil.retry(() -> {
            NotifyRechargeResultResponse response = getHttpClient().data(request);
            if (response.getStatus() != 0) {
                log.info(">>>通知财管系统响应状态码异常：request:{} response:{}", JsonUtils.encode(request), JsonUtils.encode(response));
                return;
            }
            log.info(">>>成功通知财管系统：request:{} response:{}", JsonUtils.encode(request), JsonUtils.encode(response));
//        }, 3, 2000);
    }

    private HttpClient getHttpClient() {
        return httpClients.get("card-kam");
    }
}