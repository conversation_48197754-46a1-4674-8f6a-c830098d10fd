package wanda.card.kam.consumer.service.biz;

import cmc.card.basic.admin.service.dto.KamCardDto;
import cmc.card.basic.admin.service.iface.KamCardService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import wanda.card.kam.common.contract.constant.RechargeOrderStatus;
import wanda.card.kam.common.contract.constant.Topic;
import wanda.card.kam.common.provider.dao.RechargeOrderCardInfoDao;
import wanda.card.kam.common.provider.dao.RechargeOrderDao;
import wanda.card.kam.common.provider.dao.RechargeOrderLogDao;
import wanda.card.kam.common.provider.entity.RechargeOrder;
import wanda.card.kam.common.provider.entity.RechargeOrderCardInfo;
import wanda.card.kam.common.provider.entity.RechargeOrderLog;
import wanda.card.kam.common.provider.util.PublisherUtil;
import wanda.card.kam.common.provider.util.RedisLockSupport;
import wanda.stark.core.data.PageInfo;
import wanda.stark.core.data.PageResult;
import wanda.stark.core.lang.FaultException;
import wanda.stark.db.jsd.lang.UpdateValues;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

import static wanda.card.kam.common.contract.constant.CardRechargeStatus.FAILED;
import static wanda.card.kam.common.contract.constant.CardRechargeStatus.SUCCESS;
import static wanda.stark.db.jsd.lang.Shortcut.uv;

/**
 * 充值处理业务逻辑
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class RechargeProcessBiz {

    public static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    @Autowired
    private RechargeOrderCardInfoDao rechargeOrderCardInfoDao;

    @Autowired
    private RechargeOrderLogDao rechargeOrderLogDao;

    @Autowired
    private RechargeOrderDao rechargeOrderDao;

    @Autowired
    private RedisLockSupport redisLockSupport;

    @Autowired
    private KamCardService kamCardService;

    /**
     * 处理批次充值
     *
     * @param batchNo 批次号
     */
    public void processRecharge(String batchNo) {
        log.info("开始处理批次充值，批次号：{}", batchNo);

        try {
            // 1. 先查log获取对应的order和batchLog
            RechargeOrderLog orderLog = rechargeOrderLogDao.findByBatchNo(batchNo);
            if (orderLog == null) {
                log.warn("批次号 {} 对应的充值订单日志不存在", batchNo);
                return;
            }

            // 2. 查询对应的充值订单
            RechargeOrder rechargeOrder = rechargeOrderDao.findByRechargeOrderNo(orderLog.getRechargeOrderNo());
            if (rechargeOrder == null) {
                log.warn("充值订单号 {} 对应的充值订单不存在", orderLog.getRechargeOrderNo());
                return;
            }

            // 3. 查询批次下的所有卡详情
            List<RechargeOrderCardInfo> cardInfos = rechargeOrderCardInfoDao.findByBatchNo(batchNo);

            log.info("批次号 {} 共有 {} 张卡需要充值", batchNo, cardInfos);

            // 4. 遍历batchLog里的卡号，按订单号和卡号查询卡信息并处理充值
            for (RechargeOrderCardInfo cardInfo : cardInfos) {
                processCardRecharge(orderLog, rechargeOrder, cardInfo);
            }

            // 5. 遍历充值完所有卡后，统计订单卡明细状态，更新订单状态
            updateOrderStatus(rechargeOrder);

            // 6. 发送消息到 recharge_notify_topic 进行通知处理
            sendRechargeNotifyMessage(batchNo);

            log.info("批次号 {} 充值处理完成", batchNo);

        } catch (Exception e) {
            log.error("处理批次充值失败，批次号：{}", batchNo, e);
            throw new FaultException("批次充值处理失败: " + e.getMessage());
        }
    }

    /**
     * 处理单张卡充值
     */
    private void processCardRecharge(RechargeOrderLog orderLog, RechargeOrder rechargeOrder, RechargeOrderCardInfo cardInfo) {
        String rechargeOrderNo = rechargeOrder.getRechargeOrderNo();
        try {
            String cardNo = cardInfo.getCardNo();
            log.info("开始处理卡号 {} 的充值，订单号：{}", cardNo, rechargeOrderNo);

            // 每张卡充值操作要以卡号加redis分布式锁，使用lockTemplate简化锁操作
            String lockKey = "card_recharge:" + cardNo;

            redisLockSupport.lockTemplate(lockKey, () -> {
                RechargeOrderCardInfo currentCardInfo = rechargeOrderCardInfoDao.selectOne(cardInfo.getId());
                // 检查卡状态，如果已经充值成功则跳过
                if (SUCCESS.equals(currentCardInfo.getStatus())) {
                    log.info("卡号 {} 已经充值成功，跳过处理", cardNo);
                    return;
                }
                // 调用卡系统卡充值rpc接口
                CardRechargeResult rechargeResult = callCardSystemRecharge(orderLog, rechargeOrder, currentCardInfo);
                // 卡充值成功后更新卡信息状态和余额
                updateCardInfo(currentCardInfo, rechargeResult);
            });
        } catch (Exception e) {
            log.error("处理卡号 {} 充值失败", cardInfo.getCardNo(), e);
            CardRechargeResult rechargeResult = new CardRechargeResult();
            rechargeResult.setSuccess(false);
            rechargeResult.setErrorMsg(e.getMessage());
            updateCardInfo(cardInfo, rechargeResult);
        }
    }

    /**
     * /**
     * 在事务中更新卡信息
     */
    private void updateCardInfo(RechargeOrderCardInfo cardInfo, CardRechargeResult rechargeResult) {
        // 如果充值成功，使用充值结果中的金额信息更新卡信息
        LocalDateTime now = LocalDateTime.now();
        if (rechargeResult.isSuccess()) {
            UpdateValues uv = uv()
                    .add("change_balance", rechargeResult.getChangeBalance())
                    .add("before_balance", rechargeResult.getBeforeBalance())
                    .add("after_balance", rechargeResult.getAfterBalance())
                    .add("status", SUCCESS)
                    .add("update_time", now)
                    .add("recharge_time", rechargeResult.getRechargeTime());
            rechargeOrderCardInfoDao.update(cardInfo.getId(), uv);
            log.info("卡号 {} 充值成功，充值金额：{}，赠送金额：{}，充值后余额：{}",
                    cardInfo.getCardNo(), rechargeResult.getRechargeAmount(), rechargeResult.getPresentAmount(), rechargeResult.getAfterBalance());
        } else {
            // 充值失败时只更新状态和时间字段
            // 对errorMsg字段进行截断处理，防止超过数据库字段长度限制(VARCHAR(512))
            String errorMsg = rechargeResult.getErrorMsg();
            if (errorMsg != null && errorMsg.length() > 512) {
                errorMsg = errorMsg.substring(0, 509) + "...";
            }
            rechargeOrderCardInfoDao.update(cardInfo.getId(),
                    uv("status", FAILED)
                            .add("update_time", now)
                            .add("recharge_time", rechargeResult.getRechargeTime())
                            .add("error_msg", errorMsg)
            );
            log.error("卡号 {} 充值失败：{}", cardInfo.getCardNo(), rechargeResult.getErrorMsg());
        }
    }

    /**
     * /**
     * 调用卡系统卡充值rpc接口（模拟实现）
     *
     * @param cardInfo 卡信息
     * @return 充值结果
     */
    private CardRechargeResult callCardSystemRecharge(RechargeOrderLog orderLog, RechargeOrder rechargeOrder, RechargeOrderCardInfo cardInfo) {
        log.info("调用卡系统充值接口，卡号：{}，金额：{}", cardInfo.getCardNo(), cardInfo.getRechargeAmount());

        try {
            KamCardDto.RechargeRequest rechargeRequest = new KamCardDto.RechargeRequest();
            rechargeRequest.setCardNo(cardInfo.getCardNo());
            rechargeRequest.setContractNo(rechargeOrder.getContractNo());
            rechargeRequest.setRechargeOrderNo(rechargeOrder.getRechargeOrderNo());
            rechargeRequest.setCustomerCode(rechargeOrder.getCustomerCode());
            rechargeRequest.setCustomerName(rechargeOrder.getCustomerName());
            rechargeRequest.setBatchNo(cardInfo.getBatchNo());
            rechargeRequest.setRechargeAmount(cardInfo.getRechargeAmount());
            rechargeRequest.setPresentAmount(cardInfo.getPresentAmount());
            rechargeRequest.setOperator(orderLog.getOperator());

            KamCardDto.RechargeResponse response = kamCardService.recharge(rechargeRequest);

            CardRechargeResult cardRechargeResult = new CardRechargeResult();
            cardRechargeResult.setSuccess(response.isSuccess());
            cardRechargeResult.setErrorMsg(response.getErrorMsg());
            if (response.getRechargeTime() == null)
                response.setRechargeTime(LocalDateTime.now().format(FORMATTER));
            cardRechargeResult.setRechargeTime(LocalDateTime.parse(response.getRechargeTime(), FORMATTER));
            cardRechargeResult.setRechargeAmount(response.getRechargeAmount());

            cardRechargeResult.setPresentAmount(response.getPresentAmount());
            cardRechargeResult.setChangeBalance(response.getChangeBalance());
            cardRechargeResult.setBeforeBalance(response.getBeforeBalance());
            cardRechargeResult.setAfterBalance(response.getAfterBalance());
            return cardRechargeResult;
        } catch (Exception e) {
            log.error("调用卡系统充值接口异常", e);
            return CardRechargeResult.failed("调用cmc卡系统充值rpc接口异常: " + e.getMessage());
        }
    }

    /**
     * 更新订单状态
     */
    private void updateOrderStatus(RechargeOrder rechargeOrder) {
        String rechargeOrderNo = rechargeOrder.getRechargeOrderNo();
        log.info("开始统计订单 {} 的卡明细状态并更新订单状态", rechargeOrderNo);

        try {
            // 查询订单下所有卡的状态统计
            PageResult<RechargeOrderCardInfo> pageResult = rechargeOrderCardInfoDao.pageQuery(rechargeOrderNo, null, null, null, null, new PageInfo(1, 1000));
            List<RechargeOrderCardInfo> cardInfos = pageResult.getItems();
            if (cardInfos == null || cardInfos.isEmpty()) {
                log.warn("订单号 {} 下没有找到卡信息", rechargeOrderNo);
                return;
            }

            int totalCount = cardInfos.size();
            long successCount = cardInfos.stream()
                    .mapToLong(card -> SUCCESS.equals(card.getStatus()) ? 1 : 0)
                    .sum();
            long failedCount = cardInfos.stream()
                    .mapToLong(card -> FAILED.equals(card.getStatus()) ? 1 : 0)
                    .sum();

            log.info("订单 {} 卡状态统计：总数={}, 成功={}, 失败={}", rechargeOrderNo, totalCount, successCount, failedCount);

            // 根据截图逻辑确定订单状态
            RechargeOrderStatus orderStatus;
            if (successCount == totalCount) {
                // 充值订单中的卡号，全部卡号充值成功
                orderStatus = RechargeOrderStatus.SUCCESS;
            } else if (failedCount == totalCount) {
                // 充值订单中的卡号，有充值失败的卡号，没有充值成功的卡号，可包含部分卡未充值
                orderStatus = RechargeOrderStatus.FAILED;
            } else {
                // 充值订单中的卡号，部分卡号充值成功，部分卡号充值失败或未充值
                orderStatus = RechargeOrderStatus.PART_RECHARGE;
            }
            // 更新订单状态
            rechargeOrder.setStatus(orderStatus);
            rechargeOrder.setUpdateTime(LocalDateTime.now());
            rechargeOrderDao.updateByColumns(rechargeOrder, "status", "update_time");
        } catch (Exception e) {
            log.error("更新订单 {} 状态失败", rechargeOrderNo, e);
            // 这里不抛异常，避免影响整个批次处理
        }
    }

    /**
     * 发送充值通知消息
     *
     * @param batchNo 批次号
     */
    private void sendRechargeNotifyMessage(String batchNo) {
        log.info("发送充值通知消息，批次号：{}", batchNo);
        // 使用PublisherUtil发送消息到RECHARGE_NOTIFY_TOPIC
        PublisherUtil.send(Topic.RECHARGE_NOTIFY_TOPIC, batchNo);
        log.info("充值通知消息发送成功，批次号：{}", batchNo);
    }
}
