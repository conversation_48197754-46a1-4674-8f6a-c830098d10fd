package wanda.card.kam.consumer.consumers;

import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import wanda.card.kam.consumer.service.RechargeOrderConsumerService;
import wanda.stark.msg.subscriber.AbstractConsumer;
import wanda.stark.msg.subscriber.MessageConsumer;

import static wanda.card.kam.common.contract.constant.Topic.RECHARGE_NOTIFY_TOPIC;

/**
 * 充值结果通知消费者
 * 通知财管系统充值结果
 */
@Slf4j
@MessageConsumer(topic = RECHARGE_NOTIFY_TOPIC)
public class RechargeNotifyConsumer extends AbstractConsumer<String> {

    @Autowired
    private RechargeOrderConsumerService rechargeOrderConsumerService;

    @Override
    public void process(String batchNo, MessageExt origin) {
        log.info("接收到充值结果通知消息，批次号：{}", batchNo);

        try {
            // 通知财管系统充值结果
            rechargeOrderConsumerService.processRechargeNotify(batchNo);
            log.info("财管系统通知完成，批次号：{}", batchNo);
        } catch (Exception e) {
            log.error("通知财管系统失败，批次号：{}", batchNo, e);
            throw e; // 重新抛出异常，让消息队列进行重试
        }
    }
}