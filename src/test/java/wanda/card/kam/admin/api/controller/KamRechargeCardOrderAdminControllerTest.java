package wanda.card.kam.admin.api.controller;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.client.TestRestTemplate;
import wanda.card.kam.admin.api.ApiApplication;
import wanda.card.kam.admin.api.model.RechargeOrderModel;
import wanda.stark.core.data.PageResult;
import wanda.stark.core.data.R;

@SpringBootTest(classes = {ApiApplication.class}, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
class KamRechargeCardOrderAdminControllerTest {

    @Autowired
    private TestRestTemplate restTemplate;

    @Autowired
    private KamRechargeCardOrderAdminController adminController;

    @Test
    void list() {
        restTemplate.postForObject("/card-kam/recharge-order/list", null, String.class);
    }

    @Test
    void export() {
    }

    @Test
    void detail() {
        R<RechargeOrderModel.OrderDetailAndLog> result = adminController.detail(2L);
        System.out.println(result);
    }

    @Test
    void cardDetailList() {
        RechargeOrderModel.CardDetailListRequest request = new RechargeOrderModel.CardDetailListRequest();
        request.setRechargeOrderNo("RO20250107002");
        PageResult<RechargeOrderModel.CardDetailItem> result = adminController.cardDetailList(request);
        System.out.println(result);
    }
}
