package wanda.card.kam.admin.api.controller;

import com.alibaba.fastjson.JSON;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import wanda.card.kam.admin.api.ApiApplication;
import wanda.card.kam.admin.api.model.RechargeVerifyModel;
import wanda.card.kam.admin.api.model.Result;

@SpringBootTest(classes = {ApiApplication.class}, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class KamControllerTest {

    @Autowired
    private KamController kamController;

    @Test
    public void testRechargeVerify() {
        RechargeVerifyModel.VerifyRequest request = new RechargeVerifyModel.VerifyRequest();
        request.setRechargeAmount(50);
        request.setCustomerCode("123");
        request.setCinemaInnerCode("849");
        request.setCardNos("6879000762274759,6879000265964880");
        Result<RechargeVerifyModel.VerifyResponse> result = kamController.rechargeVerify(request);
        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public void testUpdateRechargeOrder() {
        RechargeVerifyModel.OrderUpdateRequest request = new RechargeVerifyModel.OrderUpdateRequest();
        request.setContractNo("CNT20250107002");
        request.setRechargeOrderNo("RO20250107002");
        request.setOperator("test");
        Result<Object> result = kamController.rechargeOrderUpdate(request);
        System.out.println(JSON.toJSONString(result));
    }
}
