package wanda.card.kam.consumer.consumers;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import wanda.card.kam.consumer.ConsumerApplication;

@SpringBootTest(classes = {ConsumerApplication.class})
class RechargeCardConsumerTest {
    @Autowired
    RechargeCardConsumer rechargeCardConsumer;

    @Test
    void process() {
        rechargeCardConsumer.process("BATCH202501070100", null);
    }
}