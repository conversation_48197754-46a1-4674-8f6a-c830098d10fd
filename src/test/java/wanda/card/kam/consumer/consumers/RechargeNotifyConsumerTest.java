package wanda.card.kam.consumer.consumers;

import junit.framework.TestCase;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import wanda.card.kam.consumer.ConsumerApplication;

@SpringBootTest(classes = {ConsumerApplication.class})
public class RechargeNotifyConsumerTest {
    @Autowired
    private RechargeNotifyConsumer rechargeNotifyConsumer;

    @Test
    void process() {
        rechargeNotifyConsumer.process("BATCH202501070100", null);
    }
}