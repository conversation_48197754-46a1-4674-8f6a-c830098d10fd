# stark-tools-httpclient【推荐】

***要求stark框架版本不得低于`1.1.1-SNAPSHOT`***

## 规范

基于stark-tools-httpclient实现的httpclient工具项目，请统一放在[commons/httpclients](http://gitlab.wandatech-dev.com/commons/httpclients)目录下

## POM

```xml
<dependency>
    <groupId>wanda.cloud.tools</groupId>
    <artifactId>stark-tools-httpclient</artifactId>
</dependency>
```

## 基本使用

纯工具，脱离了SpringBoot环境

### 1. HttpClient 

* 通用规范接口：`HttpClient#body(Object)` 返回字符串

```java
public class HttpClientTests {
    @Test
    public void testData() {
        // 创建Client配置类, 指定接口地址
        SimpleHttpClientOptions options = HttpClientOptions.create("http://www.baidu.com");
        // 创建Client实例
        HttpClient httpClient = HttpClientFactory.DEFAULT.create(options);
        // 获取响应字符串
        String response = httpClient.body(BaiduRequest.builder().build());
        System.out.println(response);
    }
}
```

* JavaBean规范接口：`HttpClient#data(...)` 返回JavaBean，可自定义反序列化规范  

```java
public class HttpClientTests {
    @Test
    public void testData() {
        // 创建Client配置类, 指定接口地址
        SimpleHttpClientOptions options = HttpClientOptions.create("http://mdm-prd-mx.wandafilm.com")
                // 这里可以自定义反序列化规范类，默认就是JSON规范(JsonResponseDecoder.class)，如果是XML规范，请自己实现接口
                .setDecoder(CustomResponseDecoder.class);
        // 创建Client实例
        HttpClient httpClient = HttpClientFactory.DEFAULT.create(options);
        // 请求数据接口
        StateResponse response = httpClient.data(StateRequest.builder().build(), StateResponse.class);
        System.out.println(response);
    }
}
```

### 2. Request & Response 

* 定义简单的JavaBean参数类，字段即参数：支持Header/Path/Url/Body传参

```java

@Getter
@Builder
// 注解指定GET请求地址
@GetRequest("/_$/${var}")
// 指定参数命名风格，默认是小写下划线
@Params(nameStyle = NameStyle.LOWER)
public class StateRequest {
    // GET方式默认URL传参
    private final long timeStamp;
    // 指定参数名称为term
    @Param("term")
    private final String keyword;
    // 设置version请求头，并指定默认值1.0
    @Param(transfer = ParamTransfer.HEADER, defaults = "1.0")
    private final Float version;
    // 如果没有设置该字段值，则取custom配置中的配置值
    @Param(setting = "channel-code")
    private final String channelCode;
    // 指定path参数，该参数会替换@GetRequest中指定路径的EL变量表达式
    @Param(transfer = ParamTransfer.PATH)
    private final String var = "state";
    // 非http参数
    @Param(ignore = true)
    private final String tt;
}

```

> `@GetRequest`：指定GET请求路径，是否包含请求体，参数命名风格，参数默认传输方式等，类似的请求还有：`@PostRequest` `@DeleteRequest` `@OptionRequest` `@PutRequest` `@HeadRequest` `@PatchRequest` `@TraceRequest`

> `@Param`：指定参数名称，参数默认值，参数传输方式，配置参数值等

* 关联返回类型的参数类：实现`Request<T>` 接口

```java
/**
 * 响应类型
 */
@Data
public class StateResponse {
    /* 字段省略 */
}

/**
 * 请求类型，实现 Request 接口
 */
@Getter
@Builder
// 注解指定GET请求地址
@GetRequest("/_$/${var}")
public class StateRequest implements Request<StateResponse> {
    @Param(transfer = ParamTransfer.PATH)
    private final String var = "state";
    /* 其他字段省略 */
}

/**
 * 测试类
 */
public class HttpClientTest {
    public static void main(String[] args) {
        SimpleHttpClientOptions options = HttpClientOptions.create("http://mdm-prd-mx.wandafilm.com");
        HttpClient httpClient = HttpClientFactory.DEFAULT.create(options);
        // 请求数据接口，无需再次指定返回类型
//        StateResponse response = httpClient.data(StateRequest.builder().build(), StateResponse.class);
        StateResponse response = httpClient.data(StateRequest.builder().build());
        System.out.println(response);
    }
}

```

* 请求定制化接口：`ModifiableRequest`、`EntityModifiableRequest`、`MultipartModifiableRequest`

1. `ModifiableRequest`：支持修改body、headers、parameters、requestConfig

2. `EntityModifiableRequest`：支持修改支持修改body、headers、parameters、requestConfig、httpEntity

3. `MultipartModifiableRequest`：支持修改支持修改body、headers、parameters、requestConfig、multipartEntity

### 3. 文件上传

***由于HttpClient限制，上传文件时请求体不得大于25KB***

附件声明类型支持：FormBodyPart、File、InputStream、ContentBody、byte[]，区别如下：

* `FormBodyPart`：已经包含了附件参数名称，附件名称，附件内容等，所以该字段名称(或@Param指定的名称)将被忽略   
* `File`和`ContentBody`：包含了附件名称、附件内容等参数，字段名称(或@Param指定的名称)将作为附件参数名称
* `InputStream`和`byte[]`：只包含了附件内容，附件名称为空，字段名称(或@Param指定的名称)将作为附件参数名称

***建议使用`File`或者`ContentBody`类型***

Request要实现文件上传，可以通过两种方式：

1） 指定Content-Type为`multipart/*`：

> a. 通过`@Header`指定Content-Type请求头   

> b. 通过`ModifiableRequest#modifyHeader(...)`方法指定Content-Type请求头   

```java
@Getter
@Builder
// 这里通过@Header注解指定一个固定的请求头，当然，你也可以通过实现ModifiableRequest接口，在modifyHeaders方法中指定Content-Type头
@PostRequest(path = "/file/${storage}/${category}/upload", headers = @Header(name = HttpHeaders.CONTENT_TYPE, value = "multipart/form-data"))
public class UploadRequest implements Request<UploadResponse> {

    @Param(transfer = ParamTransfer.PATH)
    private final String storage;
    @Param(transfer = ParamTransfer.PATH)
    private final String category;
    /**
     * 附件声明类型支持：FormBodyPart、File、InputStream、ContentBody、byte[]
     */
    private final File file;
    /**
     * 其他表单参数
     */
    private final String otherParam;
}
```

2） 实现`MultipartModifiableRequest`接口：如果没有设置Content-Type时，HttpClient会自动默认`multipart/form-data`

```java
@Getter
@Builder
@PostRequest("/file/${storage}/${category}/upload")
public class UploadRequest implements Request<UploadResponse>, MultipartModifiableRequest {

    @Param(transfer = ParamTransfer.PATH)
    private final String storage;
    @Param(transfer = ParamTransfer.PATH)
    private final String category;
    /**
     * 附件声明类型支持：FormBodyPart、File、InputStream、ContentBody、byte[]
     */
    private final File file;
    /**
     * 其他表单参数
     */
    private final String otherParam;
}
```

### 4. Event & Listener

* 事件定义

| 事件类型                                 | 触发时机                 | 触发次数 | 用途描述                                                     |
| ---------------------------------------- | ------------------------ | -------- | ------------------------------------------------------------ |
| RequestParametersResolvedEvent           | URL参数集合解析完成后    | 每次     | 用于全局修改URL参数，例如每个接口都需要传递参数 `json=true` 可以通过该事件来实现 |
| RequestBodyResolvedEvent                 | 请求体集合解析完成后     | 每次     | 用于全局修改body参数                                         |
| RequestHeadersResolvedEvent              | 请求头集合解析完成后     | 每次     | 用于全局修改请求头，常用语签名生成等场景                     |
| RequestConfigBuildEvent                  | RequestConfig对象创建时  | 每次     | 用于全局修改请求配置                                         |
| RequestSendingEvent                      | 请求发送前               | 每次     | 可用于修改 Apache HttpRequest 实例，或用于记录请求开始时间，或增加限流策略 |
| ClientBuilderInitializingEvent           | 首次发送请求时           | 一次     | 用于修改 Apache HttpClient 实例                              |
| RequestSuccessEvent<br>RequestErrorEvent | 请求成功时<br>请求失败时 | 每次     | 可用于全局处理业务异常，或用于收集响应报文、响应时间等信息   |
| RequestCompleteEvent | 每次请求完成时(成功或失败都会触发) | 每次 | 可用于请求后的清理工作 |

* 事件绑定

1. `HttpClient#bind(Class<E>, HttpClientListener<E>)`: 绑定HttpClient全局事件
2. `HttpClient#bind(Class<E>, Class<R>, HttpClientListener<E>)`: 针对特定请求绑定事件

```java
public class HttpClientTests {
    @Test
    public void test() {
        SimpleHttpClientOptions options = HttpClientOptions.create("http://mdm-prd-mx.wandafilm.com");
        HttpClient httpClient = HttpClientFactory.DEFAULT.create(options);
        // 绑定 RequestHeadersResolvedEvent 全局事件
        httpClient.bind(RequestHeadersResolvedEvent.class, event -> {
            Map<String, String> headers = event.getHeaders();
            // 逻辑省略
            headers.put("sign", ".....");
        });
        StateResponse response = httpClient.data(StateRequest.builder().build());
        System.out.println(response);
    }
}
```


## SpringBoot集成

### 依赖引入

如果没有依赖 httpclient工具项目中执行单元测试是<strong style="color: red;">可能</strong>需要手动引入依赖：

```xml
<dependency>
    <groupId>wanda.cloud</groupId>
    <artifactId>spring-cloud-wanda-autoconfigure</artifactId>
</dependency>
```

<strong style="color: red;">一般来说不需要手动引入，因为wanda所有starter依赖都会间接引入此依赖包</strong>

### 调用方式

1. 依赖注入

```java
import org.springframework.beans.factory.annotation.Autowired;

public class TestHttpClient {
    @Autowired
    private HttpClients httpClients;
    
    public void test() {
        String response = httpClients.get("test").body(request);
        System.out.println(response);
    }
}
```

2. 静态调用 

```java
public class TestHttpClient {
    
    public void test() {
        String response = HttpClients.getClient("test").body(request);
        System.out.println(response);
    }
}
```

### HttpClient 配置方式

1. 应用配置

> 应用配置文件即服务启动时加载的文件，如服务本地的`application.yml`，又如 Nacos 中对应的服务配置文件等。   
> 
> 在服务配置文件中，配置命名空间为`stark.tools.httpclients`，支持多个HttpClient配置，Map，key为HttpClient名称.

如果所有连接都使用默认配置，则只需要配置域名即可，有一种简单的配置方式：

```yaml
stark:
  tools:
    httpclients:
      # 这里定义 httpclient 的名称为 dinghao1 , 接口地址为 http://dinghao1.com
      dingdao1: http://dinghao1.com
      # 这里定义 httpclient 的名称为 dinghao2 , 接口地址为 http://dinghao2.com
      dingdao2: http://dinghao2.com
      # 这里定义 httpclient 的名称为 dinghao3 , 接口地址为 http://dinghao3.com
      dingdao3: http://dinghao3.com
```

如果需要配置代理、连接池等，则可以使用httpclient标准配置方式，默认配置如下，不需要全部配置，只需要配置需要定制的几项即可。

```yaml
stark:
  tools:
    httpclients:
      dinghao: #Client名称 ----- [client的唯一标识]
        debug: false #是否打印请求和响应日志
        domain: http://dinghao.com #域
#        factory:   #指定HttpClient工厂类
        proxy-name: '' # 使用全局的代理配置
        proxy-enabled: false #是否开启代理 默认关闭
        proxy-host: *********** #代理地址，暂不支持多个代理
        proxy-port: 22 #代理端口
        proxy-protocol: http #代理协议 默认http
        proxy-username: name #代理用户名
        proxy-password: pass #代理密码
        validation-enabled: false #启用请求参数校验 支持基于JSR-303的验证注解 默认false
        gzip-enabled: false #是否启用gzip压缩
        socket-timeout: 5000 #socket传输超时时间 默认5000ms
        log-features: # 全局设置日志界别
          - REQUEST_BODY  #打印请求body
          - REQUEST_HEADER  #打印请求header
          - RESPONSE_BODY #打印响应报文
        request-charset: UTF-8 #请求编码
        response-charset: UTF-8 #响应编码
        response-decoder: wanda.stark.tools.httpclient.decoder.XxxResponseDecoder #响应反序列化方式,默认使用基于 Jackson 实现的 Json 解码
        connection-timeout: 3000 #建立连接的超时时间 默认3000ms
        connection-request-timeout: 3000 #连接池获取到连接的超时时间 默认3000ms
        connection-idle-timeout: 15000 #关闭空闲连接时间 默认15000ms
        connection-max-per-route: 20 #服务每次能并行接受请求数量 默认20
        connection-max-total: 50 #整个连接池的最大连接数 默认50
        connection-alive-time: 0 #连接存活时间,小于等于0表示短连接, 默认0ms
        request-options:  #根据url定制请求
          - pattern: '/admin/**'
            gzip-enabled: false
            socket-timeout: 30000
            log-features: request_body,request_header,response_body
        custom: #自定义配置 custom名称不可更改
          channelCode: mtime #自定义配置项 名称-值 随意
          _v: 1.0
```

2. 【推荐】动态配置

支持 Nacos 动态配置文件。   

在 Nacos 当前系统的命名空间下，创建一个`groupId=HTTPCLIENT`、`dataId=[HttpClientName]`的配置文件（这个文件叫动态配置文件，PS：`[HttpClientName]`是HttpClient名称，***dataId不要使用后缀***）   

配置文件格式与系统配置格式相同，配置文件内容如下   

> 动态配置文件中不支持配置多个HttpClient配置，所以动态配置文件中的配置前缀是`stark.tools.httpclient`，而应用配置文件中前缀是`stark.tools.httpclients`
> 
> 框架虽然会从nacos中动态加载httpclient配置并将结果缓存至内存中（缓存时间60分钟），但框架并不会监听httpclient配置文件的变化，如果httpclient配置文件发生变化需立即生效，请修改服务对应的应用配置来出发缓存刷新

```yaml
## dinghao
stark:
  tools:
    httpclient:
      debug: false
      domain: http://dinghao.com
```

### 事件监听

通过注解`@OnHttpClient`来配置HttpClient的监听器，例如通过`RequestHeadersResolvedEvent`事件设置验签请求头：

```java
@Configuration
public class TestHttpClientAutoConfiguration {
    /**
     * 验签头
     */
    @Bean
    public HttpClientListener<RequestHeadersResolvedEvent> testRequestHeadersResolvedEventListener() {
        return event -> {
            // 代码省略
            String value = generateSignature(event);
            event.getHeaders().put("sign", value);
        };
    }
}
```

### 自定义反序列化

目前框架仅提供了json反序列化实现，如果需要其他反序列化，可以自己实现`ResponseDecoder`接口，并在配置中通过`stark.tools.httpclients.[client-name].response-decoder`指定实现类类型，如下：

```yaml
stark:
  tools:
    httpclients:
      mtime:
        response-decoder: wanda.stark.tools.httpclient.decoder.CustomResponseDecoder
```

### 自定义 HttpClientFactory

有时候我们需要使用初始化自己的HttpClient实例，比如创建HttpClient实例时指定某些参数转换方式，或者指定响应的解析方式等，只需两步即可实现：

1. 实现自己的`HttpClientFactory`接口，可以通过`@Naming`指定HttpClient实例名称，如果没有使用注解，则根据工厂类类名解析：`AaaBbbHttpClientFactory` 解析名称为 `aaa-bbb`。

```java
/**
 * TestHttpClient 工厂类
 */
public class MyTestHttpClientFactory implements HttpClientFactory {

    @Override
    public HttpClient create(HttpClientOptions options) {
        // 自定义参数转换器
        RequestParameterConverter converter = RequestParameterConverter.from(adapter -> {
            // 所有LocalTime类型参数转为HHmm的数字形式
            adapter.register(ConverterBuilder.writing(LocalTime.class, Integer.class, source -> Integer.parseInt(DateConverter.toString(source, "HHmm"))));
        });
        // 自定义响应解析方式
        SimpleHttpClientOptions opts = new SimpleHttpClientOptions(options)
                .setResponseDecoder(XmlResponseDecoder.class);
        return new HttpClient(opts, converter)
                // 事件初始化
                .bind(RequestHeadersResolvedEvent.class, event -> {
                    // 验签逻辑 伪代码
                    Map<String, String> headers = event.getHeaders();
                    headers.put("sign", "...");
                });
    }
    
    private static class XmlResponseDecoder implements ResponseDecoder {
        @Override
        public <T> T decode(String value, Type type) {
            /* 代码省略 */
            return null;
        }
    }
}
```

2. 在`resources/META-INF/services`目录下新建文件`wanda.stark.tools.httpclient.HttpClientFactory`，内容为工厂类类名

```text
stark.tools.httpclients.test.MyTestHttpClientFactory
```
