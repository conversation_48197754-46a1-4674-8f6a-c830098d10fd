#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成充值订单推送接口的测试数据
包含1000张卡的完整HTTP请求文件
"""

import json
from datetime import datetime

def generate_card_infos(count=1000):
    """生成指定数量的卡信息"""
    card_infos = []
    
    # 定义几种不同的卡类型
    card_types = [
        {"code": "SVCARD001", "name": "储值卡"},
        {"code": "VIPCARD001", "name": "会员卡"},
        {"code": "GIFTCARD001", "name": "礼品卡"}
    ]
    
    for i in range(1, count + 1):
        # 生成16位卡号，前缀622588，后面补零到16位
        card_no = f"622588{i:010d}"
        
        # 循环使用不同的卡类型
        card_type = card_types[i % len(card_types)]
        
        card_info = {
            "cardNo": card_no,
            "cardTypeCode": card_type["code"],
            "cardTypeName": card_type["name"]
        }
        card_infos.append(card_info)
    
    return card_infos

def generate_http_request():
    """生成完整的HTTP请求"""
    
    # 生成时间戳用于订单号
    timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
    
    # 基本订单信息
    request_data = {
        "rechargeOrderNo": f"RO{timestamp}",
        "contractNo": f"CNT{timestamp}",
        "customerCode": "CUST001",
        "customerName": "测试客户公司",
        "sellerId": "SELLER001",
        "seller": "张三",
        "areaCode": "AREA001",
        "areaName": "华北区域",
        "cinemaInnerCode": "CINEMA001",
        "cinemaName": "万达影城测试店",
        "everyCardRechargeAmount": 10000,  # 100元，以分为单位
        "everyCardPresentAmount": 1000,    # 10元赠送，以分为单位
        "count": 1000,
        "amount": 1000 * (10000 + 1000),  # 总金额 = 卡数量 * (充值金额 + 赠送金额)
        "operator": "测试操作员",
        "cardInfos": generate_card_infos(1000)
    }
    
    # 生成HTTP请求内容
    http_content = f"""### 充值订单推送接口测试 - 1000张卡
POST http://localhost:8080/card-kam/ysht/recharge-order/push
Content-Type: application/json

{json.dumps(request_data, ensure_ascii=False, indent=2)}

### 说明
# 测试数据包含1000张卡
# 每张卡充值100元，赠送10元
# 总金额：1000 * (100 + 10) = 110,000元 = 11,000,000分
# 卡号范围：6225880000000001 到 6225881000000000
# 卡类型：储值卡、会员卡、礼品卡循环使用
"""
    
    return http_content

if __name__ == "__main__":
    # 生成HTTP请求文件
    content = generate_http_request()
    
    # 写入文件
    with open("rest-api_order_push_1000.http", "w", encoding="utf-8") as f:
        f.write(content)
    
    print("✅ 已生成 rest-api_order_push_1000.http 文件")
    print("📊 包含1000张卡的测试数据")
    print("🚀 可以直接在IDEA中使用HTTP Client工具测试")
