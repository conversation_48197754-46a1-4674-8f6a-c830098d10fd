#!/bin/bash

# 设置JAVA_HOME
export JAVA_HOME=/Library/Java/JavaVirtualMachines/jdk-11.0.26+4/Contents/Home

# 进入测试目录
cd card-kam-admin-service/provider

# 编译测试代码
echo "编译测试代码..."
mvn test-compile -q

# 检查编译是否成功
if [ $? -eq 0 ]; then
    echo "编译成功！"
    
    # 尝试运行特定测试
    echo "运行跨批次重复卡号校验测试..."
    mvn surefire:test -Dtest=RechargeOrderRechargeBizTest#testValidateCardNosAcrossBatches_DuplicateCards -Dmaven.test.skip=false -Dsurefire.skipTests=false
    
    if [ $? -ne 0 ]; then
        echo "尝试使用不同的方式运行测试..."
        mvn test -Dtest=RechargeOrderRechargeBizTest -Dmaven.test.skip=false -Dsurefire.skipTests=false -Dmaven.test.failure.ignore=true
    fi
else
    echo "编译失败！"
fi